{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "overlay-capability", "description": "Capability for overlay windows including window highlight and screenshot overlays", "windows": ["pre_initialized_window_highlight", "window_highlight_overlay", "screenshot_overlay", "region_selection_overlay", "window_highlight_*", "modern_overlay_*", "overlay_manager_*", "advanced_region_*", "quick_toolbar_*", "screenshot_overlay_*"], "permissions": ["core:default", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-set-focus", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-set-always-on-top", "core:window:allow-set-decorations", "core:window:allow-set-resizable", "core:window:allow-set-skip-taskbar", "core:window:allow-set-visible-on-all-workspaces", "core:window:allow-set-cursor-visible", "core:window:allow-set-cursor-icon", "core:window:allow-set-cursor-position", "core:window:allow-set-ignore-cursor-events", "core:window:allow-current-monitor", "core:window:allow-primary-monitor", "core:window:allow-available-monitors", "core:window:allow-cursor-position", "core:window:allow-is-visible", "core:window:allow-is-focused", "notification:default", "notification:allow-is-permission-granted", "notification:allow-request-permission", "notification:allow-show", "fs:allow-write-file", "fs:allow-read-file", "fs:allow-exists", "fs:allow-create", "fs:allow-mkdir", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "core:event:allow-listen", "core:event:allow-emit", {"identifier": "fs:scope", "allow": [{"path": "$PICTURE"}, {"path": "$PICTURE/**"}, {"path": "$HOME/Pictures"}, {"path": "$HOME/Pictures/**"}, {"path": "$TEMP"}, {"path": "$TEMP/**"}]}]}