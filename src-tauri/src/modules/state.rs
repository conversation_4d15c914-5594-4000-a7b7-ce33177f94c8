// 状态管理模块 - P1-T6: 分离capture和editor状态存储，实现状态重置机制
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{command, AppHandle, Emitter, Manager};

/// 截图状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CaptureState {
    pub is_capturing: bool,
    pub capture_mode: CaptureMode,
    pub current_overlay_id: Option<String>,
    pub selected_region: Option<RegionData>,
    pub selected_window: Option<WindowData>,
    pub capture_timestamp: Option<u64>,
}

/// 编辑器状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorState {
    pub is_editing: bool,
    pub current_tool: EditorTool,
    pub canvas_data: Option<String>,
    pub edit_history: Vec<EditAction>,
    pub unsaved_changes: bool,
    pub editor_window_id: Option<String>,
}

/// 应用程序全局状态
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppState {
    pub capture: CaptureState,
    pub editor: EditorState,
    pub ui_config: UIConfig,
    pub session_id: String,
    pub last_updated: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CaptureMode {
    None,
    Region,
    Window,
    Fullscreen,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EditorTool {
    None,
    Arrow,
    Rectangle,
    Circle,
    Text,
    Pen,
    Highlighter,
    Blur,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionData {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
    pub screen_width: u32,
    pub screen_height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowData {
    pub id: u32,
    pub title: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditAction {
    pub action_type: String,
    pub data: serde_json::Value,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UIConfig {
    pub theme: String,
    pub auto_save: bool,
    pub show_grid: bool,
    pub grid_size: u32,
}

impl Default for CaptureState {
    fn default() -> Self {
        Self {
            is_capturing: false,
            capture_mode: CaptureMode::None,
            current_overlay_id: None,
            selected_region: None,
            selected_window: None,
            capture_timestamp: None,
        }
    }
}

impl Default for EditorState {
    fn default() -> Self {
        Self {
            is_editing: false,
            current_tool: EditorTool::None,
            canvas_data: None,
            edit_history: Vec::new(),
            unsaved_changes: false,
            editor_window_id: None,
        }
    }
}

impl Default for UIConfig {
    fn default() -> Self {
        Self {
            theme: "dark".to_string(),
            auto_save: true,
            show_grid: false,
            grid_size: 20,
        }
    }
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            capture: CaptureState::default(),
            editor: EditorState::default(),
            ui_config: UIConfig::default(),
            session_id: uuid::Uuid::new_v4().to_string(),
            last_updated: chrono::Utc::now().timestamp_millis() as u64,
        }
    }
}

// 全局状态管理器
lazy_static::lazy_static! {
    static ref APP_STATE: Arc<Mutex<AppState>> = Arc::new(Mutex::new(AppState::default()));
    static ref STATE_SUBSCRIBERS: Arc<Mutex<HashMap<String, Vec<String>>>> = Arc::new(Mutex::new(HashMap::new()));
}

/// 获取当前应用状态
#[command]
pub async fn get_app_state() -> Result<AppState, String> {
    let state = APP_STATE
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    Ok(state.clone())
}

/// 获取截图状态
#[command]
pub async fn get_capture_state() -> Result<CaptureState, String> {
    let state = APP_STATE
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    Ok(state.capture.clone())
}

/// 更新截图状态
#[command]
pub async fn update_capture_state(
    app_handle: AppHandle,
    new_state: CaptureState,
) -> Result<(), String> {
    {
        let mut state = APP_STATE
            .lock()
            .map_err(|e| format!("Failed to lock state: {}", e))?;
        state.capture = new_state.clone();
        state.last_updated = chrono::Utc::now().timestamp_millis() as u64;
    }

    // 通知订阅者
    notify_state_change(&app_handle, "capture", &new_state).await?;
    println!(
        "[STATE] Capture state updated: {:?}",
        new_state.capture_mode
    );
    Ok(())
}

/// 获取编辑器状态
#[command]
pub async fn get_editor_state() -> Result<EditorState, String> {
    let state = APP_STATE
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    Ok(state.editor.clone())
}

/// 更新编辑器状态
#[command]
pub async fn update_editor_state(
    app_handle: AppHandle,
    new_state: EditorState,
) -> Result<(), String> {
    {
        let mut state = APP_STATE
            .lock()
            .map_err(|e| format!("Failed to lock state: {}", e))?;
        state.editor = new_state.clone();
        state.last_updated = chrono::Utc::now().timestamp_millis() as u64;
    }

    // 通知订阅者
    notify_state_change(&app_handle, "editor", &new_state).await?;
    println!(
        "[STATE] Editor state updated: tool={:?}, editing={}",
        new_state.current_tool, new_state.is_editing
    );
    Ok(())
}

/// 重置截图状态
#[command]
pub async fn reset_capture_state(app_handle: AppHandle) -> Result<(), String> {
    let default_state = CaptureState::default();
    update_capture_state(app_handle, default_state).await?;
    println!("[STATE] Capture state reset");
    Ok(())
}

/// 重置编辑器状态
#[command]
pub async fn reset_editor_state(app_handle: AppHandle) -> Result<(), String> {
    let default_state = EditorState::default();
    update_editor_state(app_handle, default_state).await?;
    println!("[STATE] Editor state reset");
    Ok(())
}

/// 重置所有状态
#[command]
pub async fn reset_all_states(app_handle: AppHandle) -> Result<(), String> {
    {
        let mut state = APP_STATE
            .lock()
            .map_err(|e| format!("Failed to lock state: {}", e))?;
        *state = AppState::default();
    }

    // 通知所有订阅者
    let new_state = get_app_state().await?;
    notify_state_change(&app_handle, "app", &new_state).await?;
    println!("[STATE] All states reset");
    Ok(())
}

/// 开始截图会话
#[command]
pub async fn start_capture_session(
    app_handle: AppHandle,
    mode: String,
    overlay_id: Option<String>,
) -> Result<(), String> {
    let capture_mode = match mode.as_str() {
        "region" => CaptureMode::Region,
        "window" => CaptureMode::Window,
        "fullscreen" => CaptureMode::Fullscreen,
        _ => CaptureMode::None,
    };

    let new_state = CaptureState {
        is_capturing: true,
        capture_mode,
        current_overlay_id: overlay_id,
        selected_region: None,
        selected_window: None,
        capture_timestamp: Some(chrono::Utc::now().timestamp_millis() as u64),
    };

    update_capture_state(app_handle, new_state).await?;
    println!("[STATE] Capture session started: mode={}", mode);
    Ok(())
}

/// 完成截图会话
#[command]
pub async fn complete_capture_session(
    app_handle: AppHandle,
    region: Option<RegionData>,
    window: Option<WindowData>,
) -> Result<(), String> {
    {
        let mut state = APP_STATE
            .lock()
            .map_err(|e| format!("Failed to lock state: {}", e))?;
        state.capture.is_capturing = false;
        state.capture.selected_region = region;
        state.capture.selected_window = window;
        state.capture.current_overlay_id = None;
        state.last_updated = chrono::Utc::now().timestamp_millis() as u64;
    }

    let new_state = get_capture_state().await?;
    notify_state_change(&app_handle, "capture", &new_state).await?;
    println!("[STATE] Capture session completed");
    Ok(())
}

/// 开始编辑会话
#[command]
pub async fn start_editor_session(
    app_handle: AppHandle,
    editor_window_id: String,
    canvas_data: Option<String>,
) -> Result<(), String> {
    let new_state = EditorState {
        is_editing: true,
        current_tool: EditorTool::Arrow,
        canvas_data,
        edit_history: Vec::new(),
        unsaved_changes: false,
        editor_window_id: Some(editor_window_id),
    };

    update_editor_state(app_handle, new_state).await?;
    println!("[STATE] Editor session started");
    Ok(())
}

/// 添加编辑动作到历史
#[command]
pub async fn add_edit_action(
    app_handle: AppHandle,
    action_type: String,
    data: serde_json::Value,
) -> Result<(), String> {
    {
        let mut state = APP_STATE
            .lock()
            .map_err(|e| format!("Failed to lock state: {}", e))?;
        let action = EditAction {
            action_type: action_type.clone(),
            data,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
        };

        state.editor.edit_history.push(action);
        state.editor.unsaved_changes = true;
        state.last_updated = chrono::Utc::now().timestamp_millis() as u64;
    }

    let new_state = get_editor_state().await?;
    notify_state_change(&app_handle, "editor", &new_state).await?;
    println!("[STATE] Edit action added: {}", action_type);
    Ok(())
}

/// 订阅状态变化
#[command]
pub async fn subscribe_to_state_changes(
    window_id: String,
    state_types: Vec<String>,
) -> Result<(), String> {
    let mut subscribers = STATE_SUBSCRIBERS
        .lock()
        .map_err(|e| format!("Failed to lock subscribers: {}", e))?;

    for state_type in state_types {
        let entry = subscribers
            .entry(state_type.clone())
            .or_insert_with(Vec::new);
        if !entry.contains(&window_id) {
            entry.push(window_id.clone());
        }
    }

    println!("[STATE] Window {} subscribed to state changes", window_id);
    Ok(())
}

/// 取消订阅状态变化
#[command]
pub async fn unsubscribe_from_state_changes(window_id: String) -> Result<(), String> {
    let mut subscribers = STATE_SUBSCRIBERS
        .lock()
        .map_err(|e| format!("Failed to lock subscribers: {}", e))?;

    for (_, window_list) in subscribers.iter_mut() {
        window_list.retain(|id| id != &window_id);
    }

    println!(
        "[STATE] Window {} unsubscribed from state changes",
        window_id
    );
    Ok(())
}

/// 通知状态变化
async fn notify_state_change<T: Serialize>(
    app_handle: &AppHandle,
    state_type: &str,
    new_state: &T,
) -> Result<(), String> {
    let subscribers = STATE_SUBSCRIBERS
        .lock()
        .map_err(|e| format!("Failed to lock subscribers: {}", e))?;

    if let Some(window_list) = subscribers.get(state_type) {
        for window_id in window_list {
            if let Some(window) = app_handle.get_webview_window(window_id) {
                let event_name = format!("state-change-{}", state_type);
                if let Err(e) = window.emit(&event_name, new_state) {
                    println!(
                        "[WARNING] Failed to notify window {} of state change: {}",
                        window_id, e
                    );
                }
            }
        }
    }

    Ok(())
}

/// 获取状态统计信息
#[command]
pub async fn get_state_stats() -> Result<serde_json::Value, String> {
    let state = APP_STATE
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    let subscribers = STATE_SUBSCRIBERS
        .lock()
        .map_err(|e| format!("Failed to lock subscribers: {}", e))?;

    let stats = serde_json::json!({
        "session_id": state.session_id,
        "last_updated": state.last_updated,
        "capture_active": state.capture.is_capturing,
        "editor_active": state.editor.is_editing,
        "edit_history_count": state.editor.edit_history.len(),
        "subscriber_count": subscribers.values().map(|v| v.len()).sum::<usize>(),
        "unsaved_changes": state.editor.unsaved_changes
    });

    Ok(stats)
}
