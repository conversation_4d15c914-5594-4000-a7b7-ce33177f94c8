// 截图捕获模块 - 专门处理截图相关功能
use base64::{engine::general_purpose, Engine as _};
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tauri::command;
use xcap::Monitor;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ScreenshotArea {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowCaptureRequest {
    pub window_id: u32,
    pub window_title: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    pub success: bool,
    pub message: String,
    pub path: Option<String>,
    pub base64: Option<String>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub capture_time_ms: Option<u64>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotPreview {
    pub base64: String,
    pub width: u32,
    pub height: u32,
    pub capture_time_ms: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CaptureConfig {
    pub enable_optimized_capture: bool,
    pub default_format: String,
    pub quality: u8,
    pub enable_preview: bool,
    pub max_capture_size: u32,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            enable_optimized_capture: true,
            default_format: "png".to_string(),
            quality: 95,
            enable_preview: true,
            max_capture_size: 4096,
        }
    }
}

/// 捕获指定区域的截图
#[command]
pub async fn capture_region_new(area: ScreenshotArea) -> Result<ScreenshotResult, String> {
    log::info!(
        "[CAPTURE] Starting region capture: x={}, y={}, w={}, h={}",
        area.x, area.y, area.width, area.height
    );
    log::debug!(
        "[DEBUG] Parameter type: ScreenshotArea, size: {} bytes",
        std::mem::size_of_val(&area)
    );

    let start_time = Instant::now();

    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }

    // 使用第一个显示器进行截图
    let monitor = &monitors[0];
    let image = monitor
        .capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;

    // 裁剪指定区域
    let cropped = image::imageops::crop_imm(
        &image,
        area.x as u32,
        area.y as u32,
        area.width as u32,
        area.height as u32,
    )
    .to_image();

    let capture_time = start_time.elapsed().as_millis() as u64;

    // 转换为base64
    let mut buffer = Vec::new();
    cropped
        .write_to(
            &mut std::io::Cursor::new(&mut buffer),
            image::ImageFormat::Png,
        )
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);

    Ok(ScreenshotResult {
        success: true,
        message: "Region captured successfully".to_string(),
        path: None,
        base64: Some(base64_data),
        width: Some(cropped.width()),
        height: Some(cropped.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 捕获全屏截图
#[command(async)]
pub async fn capture_fullscreen(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    log::info!("[CAPTURE] Starting fullscreen capture");

    let start_time = Instant::now();
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }

    // 使用主显示器
    let monitor = &monitors[0];
    let image = monitor
        .capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;

    let capture_time = start_time.elapsed().as_millis() as u64;

    // 转换为base64
    let mut buffer = Vec::new();
    image
        .write_to(
            &mut std::io::Cursor::new(&mut buffer),
            image::ImageFormat::Png,
        )
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);

    // 保存文件（如果指定了路径）
    let saved_path = if let Some(path) = save_path {
        image
            .save(&path)
            .map_err(|e| format!("Failed to save image: {}", e))?;
        Some(path)
    } else {
        None
    };

    Ok(ScreenshotResult {
        success: true,
        message: "Fullscreen captured successfully".to_string(),
        path: saved_path,
        base64: Some(base64_data),
        width: Some(image.width()),
        height: Some(image.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 获取截图配置
#[command(async)]
pub async fn get_capture_config() -> Result<CaptureConfig, String> {
    Ok(CaptureConfig::default())
}

/// 🔧 BUG FIX: 基于坐标的实时窗口截图命令
#[command(async)]
pub async fn capture_window_at_coordinates(x: i32, y: i32) -> Result<ScreenshotResult, String> {
    log::info!(
        "[CAPTURE] 🎯 Starting coordinate-based window capture at ({}, {})",
        x, y
    );

    let start_time = Instant::now();

    // 使用混合截图模块的智能检测功能
    let window_info = match crate::modules::hybrid_screenshot::detect_window_smart(x, y).await {
        Ok(Some(window)) => window,
        Ok(None) => {
            let error_msg = format!("No window found at coordinates ({}, {})", x, y);
            log::error!("[CAPTURE] ❌ {}", error_msg);
            return Err(error_msg);
        }
        Err(e) => {
            let error_msg = format!("Window detection failed: {}", e);
            log::error!("[CAPTURE] ❌ {}", error_msg);
            return Err(error_msg);
        }
    };

    log::info!(
        "[CAPTURE] 🎯 Detected window: {:?} ({}x{} at {},{}) handle={}",
        window_info.title,
        window_info.width,
        window_info.height,
        window_info.x,
        window_info.y,
        window_info.handle
    );

    // 使用xcap获取所有窗口
    let windows = xcap::Window::all().map_err(|e| {
        let error_msg = format!("Failed to get windows: {}", e);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    log::debug!(
        "[CAPTURE] 🎯 Found {} total windows from xcap",
        windows.len()
    );

    // 通过窗口位置和尺寸查找匹配的xcap窗口
    let target_window = windows.iter().find(|w| {
        if let (Ok(w_x), Ok(w_y), Ok(w_width), Ok(w_height)) = (w.x(), w.y(), w.width(), w.height()) {
            // 检查位置和尺寸是否匹配（允许小的误差）
            let x_match = (w_x - window_info.x).abs() <= 5;
            let y_match = (w_y - window_info.y).abs() <= 5;
            let width_match = (w_width as i32 - window_info.width as i32).abs() <= 10;
            let height_match = (w_height as i32 - window_info.height as i32).abs() <= 10;

            if x_match && y_match && width_match && height_match {
                if let Ok(title) = w.title() {
                    log::info!("[CAPTURE] 🎯 Found matching xcap window: '{}' ({}x{} at {},{}) id={:?}",
                             title, w_width, w_height, w_x, w_y, w.id());
                }
                return true;
            }
        }
        false
    }).ok_or_else(|| {
        // 如果通过位置找不到，列出所有可用窗口用于调试
        log::debug!("[CAPTURE] 🎯 Available windows in xcap:");
        for (i, w) in windows.iter().enumerate() {
            if let (Ok(title), Ok(id), Ok(x), Ok(y), Ok(width), Ok(height)) =
                (w.title(), w.id(), w.x(), w.y(), w.width(), w.height()) {
                log::debug!("[CAPTURE] 🎯   {}: id={}, title='{}' ({}x{} at {},{})",
                         i, id, title, width, height, x, y);
            }
        }

        let error_msg = format!("No matching xcap window found for detected window at ({},{}) {}x{}",
                               window_info.x, window_info.y, window_info.width, window_info.height);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    let window_title = target_window.title().unwrap_or_default();
    let window_width = target_window.width().unwrap_or(0);
    let window_height = target_window.height().unwrap_or(0);
    let window_id = target_window.id().unwrap_or(0);

    log::info!(
        "[CAPTURE] 🎯 Using xcap window: '{}' (id={}, {}x{})",
        window_title, window_id, window_width, window_height
    );

    // 使用xcap的Window::capture_image接口捕获窗口
    let image = target_window.capture_image().map_err(|e| {
        let error_msg = format!("Failed to capture window image: {}", e);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    log::info!(
        "[CAPTURE] ✅ Window image captured successfully: {}x{}",
        image.width(),
        image.height()
    );

    // 转换为base64
    let mut buffer = Vec::new();
    image
        .write_to(
        &mut std::io::Cursor::new(&mut buffer),
            image::ImageFormat::Png,
        )
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);
    let capture_time = start_time.elapsed().as_millis() as u64;

    let result = ScreenshotResult {
        success: true,
        message: "Window captured successfully".to_string(),
        path: None,
        base64: Some(base64_data),
        width: Some(image.width()),
        height: Some(image.height()),
        capture_time_ms: Some(capture_time),
    };

    log::info!(
        "[CAPTURE] 🎯 Coordinate-based window capture completed in {}ms",
        capture_time
    );
    Ok(result)
}
