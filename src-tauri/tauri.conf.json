{"$schema": "https://schema.tauri.app/config/2", "productName": "Mecap", "version": "0.1.0", "identifier": "com.mecap.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Mecap - Screenshot Tool", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "alwaysOnTop": false, "center": true}], "security": {"csp": null, "assetProtocol": {"enable": true, "scope": ["$PICTURE/**", "$HOME/Pictures/**", "$TEMP/**"]}}, "macOSPrivateApi": true, "withGlobalTauri": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}}, "plugins": {"fs": {"requireLiteralLeadingDot": false}, "shell": {"open": true}}}