<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Window Highlight</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent !important;
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到子元素 */
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到事件捕获层 */
        }

        .window-highlight {
            position: absolute;
            border: 3px solid #FF6B35;
            background: rgba(255, 107, 53, 0.15);
            border-radius: 6px;
            display: none;
            pointer-events: none;
            transition: all 0.08s ease-out;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.3),
                0 0 20px rgba(255, 107, 53, 0.6),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            z-index: 1000;
            animation: highlightPulse 2s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0%, 100% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.3),
                    0 0 20px rgba(255, 107, 53, 0.6),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.5),
                    0 0 30px rgba(255, 107, 53, 0.8),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
            }
        }

        .window-info {
            position: absolute;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 13px;
            display: none;
            pointer-events: none;
            max-width: 350px;
            min-width: 200px;
            z-index: 1001;
            border: 1px solid rgba(255, 107, 53, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .window-info .title {
            font-weight: 600;
            color: #FF6B35;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.2;
            max-width: 100%;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .window-info .app-name {
            color: #bbb;
            font-size: 12px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .window-info .info-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .window-info .app-icon {
            font-size: 24px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 107, 53, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 53, 0.2);
            flex-shrink: 0;
        }

        .window-info .info-text {
            flex: 1;
            min-width: 0;
        }

        .window-info .dimensions {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 11px;
            color: #888;
            background: rgba(255, 255, 255, 0.05);
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .instructions {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            pointer-events: auto;
        }

        .instructions h3 {
            margin-bottom: 8px;
            color: #FF6B35;
        }

        /* 🔧 PRODUCTION: 模式指示器样式已移除 */

        .detection-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 11px;
            min-width: 200px;
            pointer-events: auto;
        }

        .detection-stats .label {
            color: #FF6B35;
            font-weight: bold;
        }

        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 999;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 107, 53, 0.8);
        }

        .crosshair::before {
            width: 100vw;
            height: 1px;
            top: 0;
            left: -50vw;
        }

        .crosshair::after {
            width: 1px;
            height: 100vh;
            top: -50vh;
            left: 0;
        }

        /* 透明事件捕获层 */
        .event-capture-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            pointer-events: auto;
            z-index: 2000; /* 🔧 CRITICAL FIX: 确保事件捕获层在所有其他元素之上 */
        }

        /* 编辑模式相关样式 */
        .dimming-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1001;
            display: none;
            pointer-events: none;
        }

        .dimming-overlay.active {
            display: block;
        }

        .editing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1003;
            display: none;
        }

        .editing-canvas.active {
            display: block;
            pointer-events: auto;
        }

        .screenshot-container {
            position: absolute;
            border: 3px solid #4CAF50;
            background: transparent;
            border-radius: 6px;
            box-shadow:
                0 0 0 1px rgba(76, 175, 80, 0.3),
                0 0 20px rgba(76, 175, 80, 0.6);
            z-index: 1004;
            display: none;
        }

        .screenshot-container.active {
            display: block;
        }

        /* 🔧 BUG FIX: 编辑模式样式指示器 */
        .screenshot-container.editing-mode {
            border: 3px solid #FF6B35;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.3),
                0 0 20px rgba(255, 107, 53, 0.6);
            position: relative;
        }

        .screenshot-container.editing-mode::before {
            content: '🎨 编辑模式';
            position: absolute;
            top: -30px;
            left: 0;
            background: #FF6B35;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 编辑模式下禁用鼠标交互的视觉提示 */
        .editing-mode-active {
            cursor: not-allowed !important;
        }

        .editing-mode-active * {
            pointer-events: none !important;
        }

        .screenshot-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <!-- 🔧 PRODUCTION: 调试信息已移除，保持界面干净 -->

        <!-- 透明事件捕获层 -->
        <div class="event-capture-layer" id="eventCaptureLayer"></div>

        <!-- 背景变暗层 -->
        <div class="dimming-overlay" id="dimmingOverlay"></div>

        <!-- 编辑模式Canvas -->
        <canvas class="editing-canvas" id="editingCanvas"></canvas>

        <!-- 截图容器 -->
        <div class="screenshot-container" id="screenshotContainer">
            <img id="screenshotImage" alt="Selected window screenshot">
        </div>

        <div class="instructions">
            <h3>🎯 Smart Window Detection</h3>
            <p>Move mouse to highlight windows (with smart filtering)</p>
            <p>Click to select | <strong>ESC</strong> to exit</p>
        </div>

        <!-- 🔧 PRODUCTION: 模式指示器已移除，保持界面干净 -->

        <div class="window-highlight" id="windowHighlight"></div>
        <div class="window-info" id="windowInfo">
            <div class="info-header">
                <div class="app-icon" id="appIcon">🪟</div>
                <div class="info-text">
                    <div class="title">Window Title</div>
                    <div class="app-name">Application Name</div>
                </div>
            </div>
            <div class="dimensions">Position: (0, 0) | Size: 0×0</div>
        </div>

        <div class="crosshair" id="crosshair"></div>

        <!-- 🔧 BUG FIX: 区域选择UI -->
        <div id="regionSelector" style="display: none; position: absolute; border: 2px dashed #00ff00; background: rgba(0, 255, 0, 0.1); z-index: 5000; pointer-events: none;">
            <div style="position: absolute; top: -25px; left: 0; background: rgba(0, 0, 0, 0.8); color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; white-space: nowrap;">
                <span id="regionDimensions">0 x 0</span>
            </div>
        </div>

        <!-- 🔧 PRODUCTION: 调试统计信息已移除，保持界面干净 -->
    </div>

    <!-- Enhanced Toolbar System -->
    <script type="module">
        // Import enhanced toolbar modules
        import { ToolbarPositionManager } from '../src/utils/ToolbarPositionManager.js';
        import { EnhancedQuickActionToolbar, ToolbarIntegrationHelper } from '../src/utils/ToolbarIntegration.js';

        // Make available globally for integration
        window.ToolbarPositionManager = ToolbarPositionManager;
        window.ToolbarIntegration = {
            EnhancedQuickActionToolbar,
            ToolbarIntegrationHelper
        };

        console.log('[FRONTEND] Enhanced toolbar system loaded');
    </script>

    <script>
        // 🔧 CRITICAL FIX: 确保在DOM完全加载后再执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeOverlay);
        } else {
            initializeOverlay();
        }

        // 🔧 LOG REFACTOR: 统一日志系统，使用Tauri log插件
        let logAPI = null;
        let logInitialized = false;

        // 初始化日志API
        async function initializeLogging() {
            try {
                if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.log) {
                    logAPI = window.__TAURI__.log;
                    // 附加控制台输出
                    await logAPI.attachConsole();
                    logInitialized = true;
                    log.info('[FRONTEND] 🔧 Tauri log plugin initialized successfully');
                } else {
                    console.warn('[FRONTEND] ⚠️ Tauri log plugin not available, falling back to console');
                    logInitialized = true; // 标记为已初始化，使用控制台fallback
                }
            } catch (e) {
                console.warn('[FRONTEND] ⚠️ Failed to initialize Tauri log plugin:', e);
                logInitialized = true; // 标记为已初始化，使用控制台fallback
            }
        }

        // 统一日志接口
        const log = {
            trace: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${JSON.stringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.trace(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-TRACE] ${fullMessage}`);
                }
            },
            debug: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${JSON.stringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.debug(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-DEBUG] ${fullMessage}`);
                }
            },
            info: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${JSON.stringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.info(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-INFO] ${fullMessage}`);
                }
            },
            warn: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${JSON.stringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.warn(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.warn(`[FRONTEND-WARN] ${fullMessage}`);
                }
            },
            error: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${JSON.stringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.error(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.error(`[FRONTEND-ERROR] ${fullMessage}`);
                }
            }
        };

        // 🚀 CRITICAL FIX: 在log对象定义后立即设置console重定向
        setTimeout(() => {
            setupConsoleRedirection();
        }, 100);

        // 兼容性函数（逐步迁移）
        function logToBackend(level, message, data = null) {
            switch(level.toUpperCase()) {
                case 'TRACE': log.trace(message, data); break;
                case 'DEBUG': log.debug(message, data); break;
                case 'INFO': log.info(message, data); break;
                case 'WARN': log.warn(message, data); break;
                case 'ERROR': log.error(message, data); break;
                default: log.info(message, data); break;
            }
        }

        async function initializeOverlay() {
            log.info('🚀 Initializing overlay after DOM ready');

            try {
                // 🔧 CRITICAL FIX: 等待Tauri API加载
                if (typeof window.__TAURI__ === 'undefined') {
                    log.info('🚀 Waiting for Tauri API...');
                    setTimeout(initializeOverlay, 100);
                    return;
                }

                // 🔧 LOG REFACTOR: 初始化日志系统
                await initializeLogging();

                log.info('🚀 Tauri API loaded, setting up event listeners');
                setupEventListeners();

            } catch (e) {
                log.error('Initialization failed', e.toString());
            }
        }

        function setupEventListeners() {
            logToBackend('INFO', '🚀 Setting up event listeners');

            // 🔧 CRITICAL FIX: 简化的事件监听器设置
            const eventCaptureLayer = document.getElementById('eventCaptureLayer');
            if (eventCaptureLayer) {
                logToBackend('INFO', '🚀 Event capture layer found, adding listeners');

                // 直接在事件捕获层上添加点击监听器
                eventCaptureLayer.addEventListener('click', function(e) {
                    logToBackend('INFO', '🚨 DIRECT CLICK on event capture layer!');
                    logToBackend('INFO', `🚨 Click at: ${e.clientX}, ${e.clientY}`);
                    handleClick(e);
                }, { passive: false });

                logToBackend('INFO', '🚀 Event listeners added successfully');
            } else {
                logToBackend('ERROR', '❌ Event capture layer not found!');
            }
        }

        // 🔧 PRODUCTION: 调试信息函数 - 仅在开发模式下启用
        function updateDebugInfo(message, color = 'green') {
            // 生产环境下不显示调试信息，保持界面干净
            if (DEBUG_MODE) {
                const debugInfo = document.getElementById('debugInfo');
                if (debugInfo) {
                    debugInfo.textContent = 'DEBUG: ' + message;
                    debugInfo.style.background = color;
                }
            }
        }

        log.info('🚀 JavaScript execution started');
        log.info('🚀 Document ready state: ' + document.readyState);

        // 立即测试基础功能
        log.debug('🧪 Testing basic functionality...');
        log.debug('🧪 Window object available: ' + !!window);
        log.debug('🧪 Document object available: ' + !!document);
        log.debug('🧪 Tauri API available: ' + !!window.__TAURI__);

        // 🔧 CRITICAL FIX: 日志级别系统 - 移到全局作用域
        const DEBUG_MODE = false; // 设为true启用详细调试日志

        // 🚀 LOG UNIFICATION: 延迟console重定向，等待log对象初始化
        function setupConsoleRedirection() {
            if (!DEBUG_MODE && typeof log !== 'undefined') {
                const originalConsole = {
                    log: console.log,
                    error: console.error,
                    warn: console.warn,
                    info: console.info
                };

                // 重写console方法，在生产模式下使用统一日志系统
                console.log = function(message, ...args) {
                    if (typeof message === 'string' && message.includes('[FRONTEND]')) {
                        const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                        if (log && log.debug) {
                            log.debug(cleanMessage, args.length > 0 ? args[0] : undefined);
                        } else {
                            originalConsole.log(message, ...args);
                        }
                    } else {
                        originalConsole.log(message, ...args);
                    }
                };

                console.error = function(message, ...args) {
                    if (typeof message === 'string' && message.includes('[FRONTEND]')) {
                        const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                        if (log && log.error) {
                            log.error(cleanMessage, args.length > 0 ? args[0] : undefined);
                        } else {
                            originalConsole.error(message, ...args);
                        }
                    } else {
                        originalConsole.error(message, ...args);
                    }
                };

                console.warn = function(message, ...args) {
                    if (typeof message === 'string' && message.includes('[FRONTEND]')) {
                        const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                        if (log && log.warn) {
                            log.warn(cleanMessage, args.length > 0 ? args[0] : undefined);
                        } else {
                            originalConsole.warn(message, ...args);
                        }
                    } else {
                        originalConsole.warn(message, ...args);
                    }
                };

                console.log('[LOG] Console redirection setup completed');
            }
        }
        const LOG_LEVELS = {
            ERROR: 0,   // 错误信息，总是显示
            WARN: 1,    // 警告信息，总是显示
            INFO: 2,    // 重要信息，总是显示
            DEBUG: 3,   // 调试信息，DEBUG_MODE时显示
            TRACE: 4    // 跟踪信息（如鼠标移动），DEBUG_MODE时显示
        };
        const CURRENT_LOG_LEVEL = DEBUG_MODE ? LOG_LEVELS.TRACE : LOG_LEVELS.INFO;

        function logWithLevel(level, message, data = null) {
            if (level <= CURRENT_LOG_LEVEL) {
                const prefix = Object.keys(LOG_LEVELS)[level];
                console.log(`[FRONTEND-${prefix}] ${message}`, data || '');
            }
        }

        // 应用状态管理
        const AppState = {
            DETECTING: 'detecting',
            WINDOW_SELECTED: 'selected',
            CAPTURING: 'capturing',
            EDITING: 'editing',
            ERROR: 'error'
        };

        const appStateManager = {
            currentState: AppState.DETECTING,
            selectedWindow: null,
            selectedRegion: null, // 🔧 BUG FIX: 添加区域选择状态
            screenshotData: null,
            editingRegion: null,
            errorTimeout: null, // 用于保存错误恢复的定时器ID

            setState(newState, data = {}) {
                log.info(`[STATE] ${this.currentState} → ${newState}`);

                // 如果存在正在执行的错误恢复定时器，则清除它
                if (this.errorTimeout) {
                    clearTimeout(this.errorTimeout);
                    this.errorTimeout = null;
                }

                const oldState = this.currentState;
                this.currentState = newState;
                Object.assign(this, data);
                this.onStateChange(newState, oldState);

                // 关键修复：如果新状态是ERROR，则在短暂显示错误信息后自动恢复
                if (newState === AppState.ERROR) {
                    log.error("[STATE] An error occurred. Reverting to detection mode in 2 seconds.");
                    this.errorTimeout = setTimeout(() => {
                        this.setState(AppState.DETECTING, {
                            selectedWindow: null,
                            screenshotData: null,
                            editingRegion: null
                        });
                    }, 2000);
                }
            },

            onStateChange(newState, oldState) {
                this.updateUI(newState);
                this.updateEventHandlers(newState, oldState);
            },

            updateUI(state) {
                // 🔧 PRODUCTION: modeIndicator已移除，保持界面干净
                const dimmingOverlay = document.getElementById('dimmingOverlay');
                const editingCanvas = document.getElementById('editingCanvas');
                const screenshotContainer = document.getElementById('screenshotContainer');
                const crosshair = document.getElementById('crosshair');
                const instructions = document.querySelector('.instructions');

                // 关键修复：先将所有UI元素重置为默认状态
                dimmingOverlay.classList.remove('active');
                screenshotContainer.classList.remove('active');
                editingCanvas.classList.remove('active');
                crosshair.style.display = 'none';
                instructions.innerHTML = `<h3>🎯 Smart Window Detection</h3><p>Move mouse to highlight windows (with smart filtering)</p><p>Click to select | <strong>ESC</strong> to exit</p>`;

                // 根据当前状态精确设置UI
                switch (state) {
                    case AppState.DETECTING:
                        // 🔧 PRODUCTION: modeIndicator已移除
                        crosshair.style.display = 'block';
                        break;
                    case AppState.CAPTURING:
                        // 🔧 PRODUCTION: modeIndicator已移除
                        break;
                    case AppState.EDITING:
                        // 🔧 PRODUCTION: modeIndicator已移除
                        dimmingOverlay.classList.add('active');
                        screenshotContainer.classList.add('active');
                        editingCanvas.classList.add('active');
                        break;
                    case AppState.ERROR:
                        // 🔧 PRODUCTION: modeIndicator已移除
                        instructions.innerHTML = `<h3 style="color: red;">Capture Failed</h3><p>Please try again. Reverting automatically...</p>`;
                        crosshair.style.display = 'none'; // 确保错误状态下隐藏十字准星
                        break;
                }
            },

            updateEventHandlers(newState, oldState) {
                // 根据新状态管理窗口检测
                if (newState === AppState.DETECTING) {
                    this.enableWindowDetection();
                } else {
                    this.disableWindowDetection();
                }

                // 根据新状态管理编辑模式
                if (newState === AppState.EDITING) {
                    this.enableEditingMode();
                } else {
                    this.disableEditingMode();
                }
            },

            disableWindowDetection() {
                detectionEnabled = false;
                hideHighlight();
            },

            enableWindowDetection() {
                detectionEnabled = true;
            },

            enableEditingMode() {
                log.info('[STATE] 🎨 Enabling editing mode - disabling mouse interactions');

                // 🔧 BUG FIX: 禁用鼠标交互，防止意外拖拽和绘制
                this.disableMouseInteractions();

                // 显示编辑模式指示器
                this.showEditingModeIndicator();

                // 确保快捷操作工具栏显示
                this.ensureQuickToolbarVisible();
            },

            disableEditingMode() {
                log.info('[STATE] 🎨 Disabling editing mode - re-enabling mouse interactions');

                // 重新启用鼠标交互
                this.enableMouseInteractions();

                // 隐藏编辑模式指示器
                this.hideEditingModeIndicator();

                // 隐藏快捷操作工具栏
                this.hideQuickToolbar();
            },

            disableMouseInteractions() {
                // 禁用事件捕获层的鼠标事件
                if (eventCaptureLayer) {
                    eventCaptureLayer.style.pointerEvents = 'none';
                    log.debug('[STATE] 🚫 Mouse interactions disabled on event capture layer');
                }

                // 设置全局标志
                window.editingModeActive = true;
                window.mouseInteractionsDisabled = true;
            },

            enableMouseInteractions() {
                // 重新启用事件捕获层的鼠标事件
                if (eventCaptureLayer) {
                    eventCaptureLayer.style.pointerEvents = 'auto';
                    log.debug('[STATE] ✅ Mouse interactions re-enabled on event capture layer');
                }

                // 清除全局标志
                window.editingModeActive = false;
                window.mouseInteractionsDisabled = false;
            },

            showEditingModeIndicator() {
                // 在截图容器上添加编辑模式指示器
                const screenshotContainer = document.getElementById('screenshotContainer');
                if (screenshotContainer) {
                    screenshotContainer.classList.add('editing-mode');
                    log.debug('[STATE] 🎨 Editing mode indicator shown');
                }
            },

            hideEditingModeIndicator() {
                const screenshotContainer = document.getElementById('screenshotContainer');
                if (screenshotContainer) {
                    screenshotContainer.classList.remove('editing-mode');
                    log.debug('[STATE] 🎨 Editing mode indicator hidden');
                }
            },

            ensureQuickToolbarVisible() {
                // 确保快捷操作工具栏在编辑模式下可见
                setTimeout(() => {
                    showQuickActionToolbar();
                    log.info('[STATE] 🛠️ Quick action toolbar ensured visible in editing mode');
                }, 100); // 短暂延迟确保DOM更新完成
            },

            hideQuickToolbar() {
                if (quickToolbar) {
                    quickToolbar.style.display = 'none';
                    log.debug('[STATE] 🛠️ Quick action toolbar hidden');
                }
            }
        };

        // 测试状态管理器
        log.debug('🧪 State manager initialized: ' + !!appStateManager);
        log.debug('🧪 Initial state: ' + appStateManager.currentState);
        log.debug('🧪 AppState constants', AppState);

        // 🔧 CRITICAL FIX: Alternative click handling using Tauri WebView API
        log.debug('🔧 Setting up alternative Tauri-based click handling...');

        // Check if we can use Tauri's WebView window API for events
        if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
            console.log('[FRONTEND] 🔧 Tauri WebView API available, setting up window-level event handling');

            try {
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                console.log('[FRONTEND] 🔧 Current window obtained:', !!currentWindow);

                // Listen for window-level events
                currentWindow.listen('tauri://click', (event) => {
                    console.log('[FRONTEND] 🔧 Tauri window click event:', event);
                });

            } catch (error) {
                console.error('[FRONTEND] 🔧 Failed to set up Tauri window events:', error);
            }
        } else {
            console.log('[FRONTEND] 🔧 Tauri WebView API not available, using DOM events only');
        }

        let currentWindowId = null;
        let windows = [];
        let detectionEnabled = true;
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;

        // 🔧 BUG FIX: 保存最后的点击坐标用于截图
        let lastClickX = 0;
        let lastClickY = 0;

        // 窗口选择模式相关变量
        let isWindowSelected = false;
        let selectedWindowId = null;
        let quickToolbar = null;
        let dimOverlay = null;

        // Enhanced toolbar management
        let enhancedToolbar = null;
        let toolbarPositionManager = null;

        const windowHighlight = document.getElementById('windowHighlight');
        const windowInfo = document.getElementById('windowInfo');
        const crosshair = document.getElementById('crosshair');
        // 🔧 PRODUCTION: 调试统计元素已移除，保持界面干净
        // 创建安全的更新函数，避免运行时错误
        function updateDebugElement(id, value) {
            // 🚀 CRITICAL FIX: 静默处理，避免在生产模式下报错
            try {
                if (DEBUG_MODE) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                }
                // 生产模式下静默忽略，不报错
            } catch (error) {
                // 静默处理错误，避免控制台噪音
                if (DEBUG_MODE) {
                    console.warn('[DEBUG] Element not found:', id, error);
                }
            }
        }

        // 🚀 LOG UNIFICATION: 创建统一的console替换机制
        const unifiedConsole = {
            log: (message, ...args) => {
                if (DEBUG_MODE) {
                    console.log(message, ...args);
                } else {
                    // 生产模式下使用统一日志系统
                    const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                    log.debug(cleanMessage, args.length > 0 ? args[0] : undefined);
                }
            },
            error: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.error(cleanMessage, args.length > 0 ? args[0] : undefined);
            },
            warn: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.warn(cleanMessage, args.length > 0 ? args[0] : undefined);
            },
            info: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.info(cleanMessage, args.length > 0 ? args[0] : undefined);
            }
        };

        const eventCaptureLayer = document.getElementById('eventCaptureLayer');

        // 强化事件监听 - 绑定到事件捕获层
        console.log('[FRONTEND] 🎯 Setting up event listeners on capture layer...');
        console.log('[FRONTEND] 🎯 Event capture layer element:', eventCaptureLayer);
        console.log('[FRONTEND] 🎯 Event capture layer style:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🔧 CRITICAL FIX: 验证pointer-events层级修复
        console.log('[FRONTEND] 🔧 Verifying pointer-events hierarchy fix:');
        console.log('[FRONTEND] 🔧   - body pointer-events:', window.getComputedStyle(document.body).pointerEvents);
        console.log('[FRONTEND] 🔧   - overlay-container pointer-events:', window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents);
        console.log('[FRONTEND] 🔧   - event-capture-layer pointer-events:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🚨 CRITICAL TEST: Verify our CSS fixes are applied
        const bodyPointerEvents = window.getComputedStyle(document.body).pointerEvents;
        const containerPointerEvents = window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents;
        const layerPointerEvents = window.getComputedStyle(eventCaptureLayer).pointerEvents;

        if (bodyPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: body still has pointer-events: none!');
        }
        if (containerPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: overlay-container still has pointer-events: none!');
        }
        if (layerPointerEvents !== 'auto') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: event-capture-layer does not have pointer-events: auto!');
        }

        if (bodyPointerEvents === 'auto' && containerPointerEvents === 'auto' && layerPointerEvents === 'auto') {
            console.log('[FRONTEND] ✅ All pointer-events settings are correct!');
        }

        eventCaptureLayer.addEventListener('mousemove', handleMouseMove, { passive: false });
        eventCaptureLayer.addEventListener('click', handleClick, { passive: false });
        eventCaptureLayer.addEventListener('mousedown', handleMouseDown, { passive: false });
        eventCaptureLayer.addEventListener('mouseup', handleMouseUp, { passive: false });

        // 🔧 REMOVED: 删除重复的点击监听器，避免与handleClick冲突

        // 🔧 HIGH PRIORITY FIX: 条件化文档点击测试
        if (DEBUG_MODE) {
            document.addEventListener('click', function(e) {
                console.log('[FRONTEND] 🧪 DOCUMENT CLICK TEST - Click detected on document!');
                console.log('[FRONTEND] 🧪 Click coordinates:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🧪 Target element:', e.target);

                // 🔧 CRITICAL DEBUG: 可视化文档点击反馈
                updateDebugInfo('DOC CLICK! Target: ' + e.target.tagName, 'red');
            }, { passive: false });
        }

        // 🔧 CRITICAL FIX: Test cursor events availability
        console.log('[FRONTEND] 🔧 Testing cursor events availability...');

        // Test if we can receive basic mouse events
        let mouseEventCount = 0;
        document.addEventListener('mousemove', function(e) {
            mouseEventCount++;
            // 🔧 MID PRIORITY FIX: 使用TRACE级别记录鼠标事件计数
            if (mouseEventCount % 50 === 0 && appStateManager.currentState !== AppState.EDITING) {
                logWithLevel(LOG_LEVELS.TRACE, `🔧 Mouse events working - count: ${mouseEventCount} at ${e.clientX}, ${e.clientY}`);
            }
        }, { passive: true });

        // 🔧 CRITICAL FIX: 移除重复的日志级别系统声明（已在全局作用域声明）

        if (DEBUG_MODE) {
            document.addEventListener('click', function(e) {
                console.log('[FRONTEND] 🚨 DOCUMENT CLICK DETECTED!');
                console.log('[FRONTEND] 🚨 Click at:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🚨 Target:', e.target.tagName, e.target.className);
                console.log('[FRONTEND] 🚨 Event phase:', e.eventPhase);
                console.log('[FRONTEND] 🚨 Bubbles:', e.bubbles);
            }, { passive: false, capture: true });
        } // Use capture phase to catch early

        // Test pointer events
        document.addEventListener('pointerdown', function(e) {
            console.log('[FRONTEND] 🔧 POINTER DOWN detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        document.addEventListener('pointerup', function(e) {
            console.log('[FRONTEND] 🔧 POINTER UP detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        // 键盘事件仍然绑定到document
        document.addEventListener('keydown', handleKeyboard, { passive: false });

        console.log('[FRONTEND] 🎯 Event listeners setup completed on capture layer');

        // 测试事件捕获层
        eventCaptureLayer.addEventListener('mouseover', () => {
            console.log('[FRONTEND] 🎯 Mouse entered event capture layer');
        }, { once: true });

        // 🔧 CRITICAL DEBUG: 添加更多事件测试
        eventCaptureLayer.addEventListener('mouseenter', () => {
            console.log('[FRONTEND] 🔧 MOUSE ENTER on event capture layer');
        });

        eventCaptureLayer.addEventListener('mouseleave', () => {
            console.log('[FRONTEND] 🔧 MOUSE LEAVE on event capture layer');
        });

        // 🔧 MID PRIORITY FIX: 移除重复的点击监听器，避免重复日志
        if (DEBUG_MODE) {
            eventCaptureLayer.addEventListener('click', (e) => {
                console.log('[FRONTEND] 🔧 DIRECT CLICK on event capture layer at:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🔧 Event target:', e.target);
                console.log('[FRONTEND] 🔧 Current target:', e.currentTarget);
            }, { capture: true });
        }

        // 🔧 MID PRIORITY FIX: 条件化所有鼠标事件调试日志
        if (DEBUG_MODE) {
            ['mousedown', 'mouseup', 'mousemove'].forEach(eventType => {
                eventCaptureLayer.addEventListener(eventType, (e) => {
                    // 在编辑模式下禁用mousemove日志
                    const shouldLog = eventType !== 'mousemove' ||
                                     (Date.now() - lastLogTime > 1000 && appStateManager.currentState !== AppState.EDITING);

                    if (shouldLog) {
                        console.log(`[FRONTEND] 🔧 ${eventType.toUpperCase()} on event capture layer at:`, e.clientX, e.clientY);
                        if (eventType !== 'mousemove') lastLogTime = Date.now();
                    }
                }, { capture: true });
            });
        }

        // 监听模式切换事件
        if (window.__TAURI__) {
            window.__TAURI__.event.listen('mode-switch', (event) => {
                console.log('[FRONTEND] 🎯 Mode switch event received:', event.payload);
                handleModeSwitch(event.payload);
            });
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => e.preventDefault());

        console.log('[FRONTEND] Event listeners setup completed');

        // 改进的初始化逻辑
        console.log('[FRONTEND] 🔍 Checking overlay initialization state...');
        console.log('[FRONTEND] 🔍 document.hidden:', document.hidden);
        console.log('[FRONTEND] 🔍 document.hasFocus():', document.hasFocus());
        console.log('[FRONTEND] 🔍 document.visibilityState:', document.visibilityState);

        // 🔧 DEBUG: 状态检查（仅开发模式显示）
        updateDebugInfo('STATE CHECK: hidden=' + document.hidden + ' vis=' + document.visibilityState, 'cyan');

        // 检查是否为预初始化状态
        if (document.hidden || document.visibilityState === 'hidden') {
            console.log('[FRONTEND] 🔧 Overlay in pre-initialized/hidden state, setting up activation listeners');
            updateDebugInfo('HIDDEN STATE - Setting up listeners', 'yellow');

            // 监听窗口显示事件
            document.addEventListener('visibilitychange', function() {
                console.log('[FRONTEND] 🎯 Visibility changed - hidden:', document.hidden, 'state:', document.visibilityState);
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Overlay activated via visibility change, starting initialization...');
                    initializeOverlay();
                }
            });

            // 监听窗口焦点事件
            window.addEventListener('focus', function() {
                console.log('[FRONTEND] 🎯 Overlay focused, ensuring initialization...');
                initializeOverlay();
            });

            // 监听窗口显示事件（备用）
            window.addEventListener('load', function() {
                console.log('[FRONTEND] 🎯 Window load event, checking if should initialize...');
                if (!document.hidden) {
                    initializeOverlay();
                }
            });

            // 定期检查窗口状态（防止事件丢失）
            const checkInterval = setInterval(() => {
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Periodic check detected visible state, initializing...');
                    clearInterval(checkInterval);
                    initializeOverlay();
                }
            }, 500);

            // 5秒后清除检查间隔（防止无限检查）
            setTimeout(() => {
                clearInterval(checkInterval);
                console.log('[FRONTEND] 🔧 Periodic visibility check timeout');
            }, 5000);

        } else {
            // 立即初始化
            console.log('[FRONTEND] 🎯 Overlay visible, starting immediate initialization...');
            updateDebugInfo('VISIBLE STATE - Starting immediate init', 'lime');
            initializeOverlay();
        }

        function initializeOverlay() {
            // 🔧 DEBUG: 初始化开始（仅开发模式显示）
            updateDebugInfo('INIT STARTED!', 'blue');

            // 防止重复初始化
            if (window.overlayInitialized) {
                console.log('[FRONTEND] 🔧 Overlay already initialized, skipping...');
                updateDebugInfo('ALREADY INITIALIZED!', 'orange');
                return;
            }

            log.info('🚀 Starting overlay initialization...');
            log.debug('🚀 Current state - hidden: ' + document.hidden + ', visibility: ' + document.visibilityState);
            log.debug('🚀 Tauri API available: ' + !!window.__TAURI__);

            window.overlayInitialized = true;

            // 🔧 DEBUG: 初始化完成（仅开发模式显示）
            updateDebugInfo('OVERLAY INITIALIZED! Ready for clicks.', 'green');

            // 🔧 HIGH PRIORITY FIX: 条件化测试代码，只在开发模式下执行
            if (DEBUG_MODE) {
                console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Setting up test click handler');
                setTimeout(() => {
                    console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Adding test click to body');
                    document.body.addEventListener('click', function(e) {
                        console.log('[FRONTEND] 🧪 BODY CLICK DETECTED!', e.clientX, e.clientY);
                    }, { passive: false });

                    // Test programmatic click
                    console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Simulating programmatic click');
                    const testEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        clientX: 100,
                        clientY: 100
                    });
                    document.body.dispatchEvent(testEvent);
                }, 100);
            }

            try {
                // 确保DOM元素可用
                log.debug('🚀 Checking DOM elements...');
                // 🚀 CRITICAL FIX: 只检查实际存在的核心元素，移除已删除的调试元素
                const requiredElements = ['windowHighlight', 'windowInfo', 'crosshair', 'eventCaptureLayer'];
                for (const elementId of requiredElements) {
                    const element = document.getElementById(elementId);
                    if (!element) {
                        log.error('❌ Required element not found: ' + elementId);
                        return;
                    }
                }
                log.debug('✅ All required DOM elements found');

                // 初始化窗口列表
                log.info('🚀 Loading windows...');
                loadWindows();

                // 检查智能检测API可用性
                log.info('🚀 Checking smart detection API...');
                checkSmartDetectionAPI();

                // 检查macOS权限
                log.info('🚀 Checking macOS permissions...');
                checkMacOSPermissions();

                // 启用检测
                log.info('🚀 Enabling detection...');
                detectionEnabled = true;

                log.info('✅ Overlay initialization completed successfully');

            } catch (error) {
                console.error('[FRONTEND] ❌ Overlay initialization failed:', error);
                window.overlayInitialized = false; // 重置标志，允许重试
            }
        }

        async function loadWindows() {
            try {
                if (window.__TAURI__) {
                    const startTime = performance.now();
                    windows = await window.__TAURI__.core.invoke('list_windows_new');
                    const endTime = performance.now();

                    updateDebugElement('windowCount', windows.length);

                    log.info('Loaded windows: ' + windows.length);
                }
            } catch (error) {
                log.error('Failed to load windows', error);
                windows = [];
                updateDebugElement('windowCount', '0');
            }
        }

        async function checkSmartDetectionAPI() {
            log.info('🔍 Checking smart detection API availability...');
            try {
                if (!window.__TAURI__) {
                    console.error('[FRONTEND] ❌ Tauri API not available');
                    return;
                }

                // 测试智能检测API
                log.debug('🔍 Testing detect_window_smart API...');
                const testResult = await window.__TAURI__.core.invoke('detect_window_smart', {
                    x: 100,
                    y: 100
                });
                log.info('✅ Smart detection API test successful', testResult);
                updateDebugElement('apiUsed', 'Smart ✓');
            } catch (error) {
                log.error('❌ Smart detection API test failed', error);
                log.error('❌ Error details: ' + (error.message || error));
                updateDebugElement('apiUsed', 'Smart ✗');

                // 测试降级API
                try {
                    log.debug('🔍 Testing fallback API...');
                    const fallbackResult = await window.__TAURI__.core.invoke('modules::window::detect_window_under_mouse_realtime', {
                        x: 100,
                        y: 100,
                        exclude_overlay_windows: true
                    });
                    log.info('✅ Fallback API test successful', fallbackResult);
                    updateDebugElement('apiUsed', 'Fallback ✓');
                } catch (fallbackError) {
                    log.error('❌ Fallback API also failed', fallbackError);
                    updateDebugElement('apiUsed', 'All ✗');
                }
            }
        }

        // 节流控制
        let lastDetectionTime = 0;
        let lastLogTime = 0;
        let pendingDetection = null;
        const DETECTION_THROTTLE = 100; // 增加到100ms节流，提高性能
        const LOG_THROTTLE = 1000; // 增加到1000ms日志节流，减少日志噪音

        async function handleMouseMove(event) {
            const now = Date.now();
            const x = event.clientX;
            const y = event.clientY;

            // 🔧 BUG FIX: 在拖拽时显示区域选择UI
            if (isDragging && now - lastLogTime > LOG_THROTTLE) {
                logWithLevel(LOG_LEVELS.DEBUG, `🖱️ Mouse move during drag at: ${x}, ${y}`);
                logWithLevel(LOG_LEVELS.DEBUG, `🖱️ Drag in progress from: ${dragStartX}, ${dragStartY}`);
                lastLogTime = now;

                // 显示区域选择UI
                updateRegionSelector(dragStartX, dragStartY, x, y);
            }

            // 改进的节流机制 - 使用防抖而不是简单节流
            if (pendingDetection) {
                clearTimeout(pendingDetection);
            }

            if (now - lastDetectionTime < DETECTION_THROTTLE) {
                // 设置延迟检测，确保最后一次鼠标移动会被处理
                pendingDetection = setTimeout(async () => {
                    try {
                        await performWindowDetection(x, y);
                    } catch (error) {
                        console.error('[FRONTEND] ❌ Delayed window detection failed:', error);
                    }
                }, DETECTION_THROTTLE);
                return;
            }

            lastDetectionTime = now;
            pendingDetection = null;

            // 🔧 MID PRIORITY FIX: 使用TRACE级别记录鼠标移动
            if (now - lastLogTime > LOG_THROTTLE && appStateManager.currentState !== AppState.EDITING) {
                logWithLevel(LOG_LEVELS.TRACE, `🖱️ Mouse move detected at: ${x}, ${y}`);
                logWithLevel(LOG_LEVELS.TRACE, `🖱️ Detection enabled: ${detectionEnabled}, isDragging: ${isDragging}`);
                lastLogTime = now;
            }

            if (!detectionEnabled && !isDragging) {
                // 使用TRACE级别记录检测禁用信息
                if (appStateManager.currentState !== AppState.EDITING) {
                    logWithLevel(LOG_LEVELS.TRACE, '⚠️ Detection disabled, ignoring mouse move');
                }
                return;
            }

            updateDebugElement('mousePos', `(${x}, ${y})`);

            // 更新十字线位置
            crosshair.style.left = x + 'px';
            crosshair.style.top = y + 'px';

            // 在拖拽过程中跳过窗口检测
            if (isDragging) {
                if (now - lastLogTime > LOG_THROTTLE) {
                    logWithLevel(LOG_LEVELS.DEBUG, '🖱️ Skipping window detection during drag');
                }
                return;
            }

            // 🔧 CRITICAL FIX: 确保窗口检测始终被调用
            // 立即执行窗口检测
            try {
                logWithLevel(LOG_LEVELS.INFO, '🔍 Calling performWindowDetection', {x, y});
                await performWindowDetection(x, y);
            } catch (error) {
                logWithLevel(LOG_LEVELS.ERROR, `❌ Window detection failed: ${error}`);
                hideHighlight();
                updateDebugElement('currentWindow', 'Error: ' + (error.message || error));
                updateDebugElement('apiUsed', 'Error');
                updateDebugElement('processName', 'Error');
            }
        }

        // 本地检测函数已移除，现在使用后端API进行实时检测

        function handleModeSwitch(payload) {
            console.log('[FRONTEND] 🎯 Handling mode switch:', payload);

            if (payload.to === 'region_selection') {
                console.log('[FRONTEND] 🎯 Switching to region selection mode');

                // 隐藏窗口检测相关的UI
                hideHighlight();
                document.querySelector('.instructions h3').textContent = '📐 Region Selection';
                document.querySelector('.instructions p').innerHTML = 'Click and drag to select area<br><strong>ESC</strong> to exit';
                // 🔧 PRODUCTION: mode-indicator已移除

                // 禁用窗口检测
                detectionEnabled = false;

                // 启用区域选择功能
                enableRegionSelection();

                console.log('[FRONTEND] 🎯 Mode switch to region selection completed');
            }
        }

        function enableRegionSelection() {
            console.log('[FRONTEND] 🎯 Enabling region selection functionality');

            // 这里可以添加区域选择的具体实现
            // 目前先显示提示信息
            updateDebugElement('currentWindow', 'Region Selection Mode');
        }

        function getAppIcon(appName) {
            const iconMap = {
                'chrome': '🌐',
                'safari': '🧭',
                'firefox': '🦊',
                'edge': '🌊',
                'finder': '📁',
                'terminal': '⚡',
                'code': '💻',
                'vscode': '💻',
                'xcode': '🔨',
                'photoshop': '🎨',
                'illustrator': '✏️',
                'sketch': '📐',
                'figma': '🎯',
                'slack': '💬',
                'discord': '🎮',
                'zoom': '📹',
                'teams': '👥',
                'mail': '📧',
                'calendar': '📅',
                'notes': '📝',
                'music': '🎵',
                'spotify': '🎶',
                'vlc': '🎬',
                'quicktime': '🎥',
                'preview': '👁️',
                'calculator': '🧮',
                'system preferences': '⚙️',
                'activity monitor': '📊',
                'console': '🖥️',
                'simulator': '📱',
                'docker': '🐳',
                'postman': '📮',
                'git': '🌿',
                'github': '🐙',
                'notion': '📋',
                'obsidian': '🔮',
                'bear': '🐻',
                'typora': '📄'
            };

            const lowerAppName = appName.toLowerCase();

            // 精确匹配
            if (iconMap[lowerAppName]) {
                return iconMap[lowerAppName];
            }

            // 模糊匹配
            for (const [key, icon] of Object.entries(iconMap)) {
                if (lowerAppName.includes(key) || key.includes(lowerAppName)) {
                    return icon;
                }
            }

            // 默认图标
            return '🪟';
        }

        function highlightWindow(window) {
            currentWindowId = window.id;
            console.log('[FRONTEND] Highlighting window:', window.title, 'ID:', window.id);

            // 设置高亮区域
            windowHighlight.style.left = window.x + 'px';
            windowHighlight.style.top = window.y + 'px';
            windowHighlight.style.width = window.width + 'px';
            windowHighlight.style.height = window.height + 'px';
            windowHighlight.style.display = 'block';

            // Sync toolbar position if enhanced toolbar is active
            syncToolbarWithRegion(window);

            // 更新窗口信息
            const titleElement = windowInfo.querySelector('.title');
            const appNameElement = windowInfo.querySelector('.app-name');
            const dimensionsElement = windowInfo.querySelector('.dimensions');
            const appIconElement = windowInfo.querySelector('.app-icon');

            titleElement.textContent = window.title || 'Untitled Window';
            appNameElement.textContent = window.app_name || 'Unknown App';
            dimensionsElement.textContent = `${window.width} × ${window.height} at (${window.x}, ${window.y})`;

            // 设置应用图标（基于应用名称）
            appIconElement.textContent = getAppIcon(window.app_name || '');

            // 智能定位信息面板 - 避免超出屏幕边界
            const screenWidth = window.innerWidth || screen.width;
            const screenHeight = window.innerHeight || screen.height;
            const infoWidth = 350; // 信息卡片最大宽度
            const infoHeight = 120; // 信息卡片估计高度
            const margin = 15;

            let infoX, infoY;

            // 优先在窗口右上角显示
            if (window.x + window.width + margin + infoWidth <= screenWidth) {
                // 右侧有足够空间
                infoX = window.x + window.width + margin;
            } else if (window.x - margin - infoWidth >= 0) {
                // 左侧有足够空间
                infoX = window.x - margin - infoWidth;
            } else {
                // 两侧都没有足够空间，放在屏幕右边缘
                infoX = screenWidth - infoWidth - margin;
            }

            // 垂直位置：优先在窗口顶部对齐
            if (window.y + infoHeight <= screenHeight) {
                infoY = window.y;
            } else if (window.y + window.height - infoHeight >= 0) {
                // 在窗口底部对齐
                infoY = window.y + window.height - infoHeight;
            } else {
                // 放在屏幕顶部
                infoY = margin;
            }

            // 确保不超出边界
            infoX = Math.max(margin, Math.min(infoX, screenWidth - infoWidth - margin));
            infoY = Math.max(margin, Math.min(infoY, screenHeight - infoHeight - margin));

            windowInfo.style.left = infoX + 'px';
            windowInfo.style.top = infoY + 'px';
            windowInfo.style.display = 'block';
        }

        function hideHighlight() {
            currentWindowId = null;
            windowHighlight.style.display = 'none';
            windowInfo.style.display = 'none';
        }

        function syncToolbarWithRegion(region) {
            if (enhancedToolbar && enhancedToolbar.isVisible) {
                // 🔧 BUG FIX: Validate region before syncing
                if (region && region.width > 0 && region.height > 0) {
                    enhancedToolbar.updateRegion(region);
                    log.trace('🛠️ Toolbar position synced with region:', region);
                } else {
                    log.warn('🛠️ Invalid region for toolbar sync:', region);
                }
            }
        }

        function handleClick(event) {
            logToBackend('INFO', `🖱️ Click event detected at: ${event.clientX}, ${event.clientY}`);
            logToBackend('INFO', `🖱️ Current state: ${appStateManager.currentState}`);
            logToBackend('INFO', `🖱️ Selected window:`, appStateManager.selectedWindow);

            // 🔧 CRITICAL FIX: 直接处理窗口选择，不依赖复杂的事件链
            if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                logToBackend('INFO', '🖱️ Handling window selection from click event');
                event.preventDefault(); // 防止其他处理
                event.stopPropagation(); // 防止事件冒泡
                handleWindowSelection();
                return;
            } else {
                logToBackend('WARN', 'Click ignored: Not in DETECTING state or no window selected.');
                logToBackend('WARN', `  - State: ${appStateManager.currentState}`);
                logToBackend('WARN', `  - Window:`, appStateManager.selectedWindow);
            }
        }

        // 处理窗口选择
        async function handleWindowSelection() {
            log.info('🎯 Handling window selection');

            // 关键修复：在函数开始时立即捕获选定的窗口信息
            const windowToCapture = appStateManager.selectedWindow;

            if (!windowToCapture) {
                log.warn('🎯 No window to select');
                return;
            }
            log.debug('🎯 Selected window details', windowToCapture);

            try {
                log.info('🎯 Setting state to CAPTURING');
                appStateManager.setState(AppState.CAPTURING);

                log.info('🎯 Calling captureSelectedWindow with coordinates: ' + lastClickX + ', ' + lastClickY);
                const screenshotResult = await captureSelectedWindow(windowToCapture, lastClickX, lastClickY);

                log.debug('🎯 Screenshot received');

                if (screenshotResult && screenshotResult.success) {
                    log.info('🎯 Screenshot captured successfully');

                    const region = {
                        x: windowToCapture.x,
                        y: windowToCapture.y,
                        width: windowToCapture.width,
                        height: windowToCapture.height
                    };

                    appStateManager.setState(AppState.EDITING, {
                        screenshotData: screenshotResult,
                        editingRegion: region
                    });

                    log.info('🎯 Calling displayScreenshotInEditingMode');
                    displayScreenshotInEditingMode(screenshotResult, region);
                } else {
                    log.error('🎯 Screenshot capture failed', screenshotResult);
                    appStateManager.setState(AppState.ERROR);
                }
            } catch (error) {
                log.error('🎯 Error during window selection', error);
                log.error('🎯 Error type: ' + typeof error);
                log.error('🎯 Error JSON: ' + JSON.stringify(error));
                appStateManager.setState(AppState.ERROR);
            }
        }

        // 🔧 BUG FIX: 捕获选中区域的截图
        async function captureSelectedRegion(region) {
            log.info('🔍 Starting region capture', region);

            try {
                // 检查Tauri API是否可用
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available');
                }

                // 使用区域信息进行截图
                const area = {
                    x: parseFloat(region.x),
                    y: parseFloat(region.y),
                    width: parseFloat(region.width),
                    height: parseFloat(region.height)
                };

                logToBackend('INFO', '🔍 About to call capture_region_new', area);

                const result = await window.__TAURI__.core.invoke('capture_region_new', {
                    area: area
                });

                logToBackend('INFO', '🔍 Region screenshot result received');

                if (result && result.success) {
                    // 进入编辑模式
                    appStateManager.setState(AppState.EDITING, {
                        screenshotData: result,
                        editingRegion: region
                    });

                    log.info('🔍 Calling displayScreenshotInEditingMode');
                    displayScreenshotInEditingMode(result, region);

                    // 隐藏区域选择器
                    const regionSelector = document.getElementById('regionSelector');
                    if (regionSelector) {
                        regionSelector.style.display = 'none';
                    }
                } else {
                    console.error('[FRONTEND] 🔍 Region capture failed:', result);
                    appStateManager.setState(AppState.ERROR);
                }

                return result;
            } catch (error) {
                log.error('🔍 Failed to capture region', error);
                log.error('🔍 Error details: ' + error.message + ' | ' + error.stack);
                logToBackend('ERROR', '🔍 Failed to capture region', error.toString());
                logToBackend('ERROR', '🔍 Error details', `${error.message} | ${error.stack}`);
                throw error;
            }
        }

        // 🔧 BUG FIX: 基于坐标的实时窗口截图
        async function captureSelectedWindow(windowInfo, clickX, clickY) {
            log.info('🎯 Capturing window at coordinates: ' + clickX + ', ' + clickY);

            try {
                // 检查Tauri API是否可用
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available');
                }

                // 🔧 BUG FIX: 使用坐标检测策略，避免ID映射问题
                const coordinateRequest = {
                    x: Math.round(clickX),
                    y: Math.round(clickY)
                };

                log.info('🎯 About to call capture_window_at_coordinates', coordinateRequest);

                // 🚀 BUG FIX: 添加超时处理和进度指示
                const capturePromise = window.__TAURI__.core.invoke('capture_window_at_coordinates', {
                    x: coordinateRequest.x,
                    y: coordinateRequest.y
                });

                // 设置15秒超时（考虑到图像处理优化可能需要更多时间）
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('Screenshot capture timeout after 15 seconds'));
                    }, 15000);
                });

                // 添加进度指示
                const progressIndicator = setTimeout(() => {
                    log.info('🎯 Screenshot processing in progress... (large images may take longer)');
                }, 3000);

                try {
                    const result = await Promise.race([capturePromise, timeoutPromise]);
                    clearTimeout(progressIndicator);
                    log.info('🎯 Screenshot result received');
                    return result;
                } catch (error) {
                    clearTimeout(progressIndicator);
                    throw error;
                }
                return result;
            } catch (error) {
                log.error('🎯 Failed to capture window', error);
                log.error('🎯 Error details: ' + error.message + ' | ' + error.stack);
                logToBackend('ERROR', '🎯 Failed to capture window', error.toString());
                logToBackend('ERROR', '🎯 Error details', `${error.message} | ${error.stack}`);
                throw error;
            }
        }

        // 在编辑模式中显示截图
        function displayScreenshotInEditingMode(screenshotResult, region) {
            log.info('🎯 Displaying screenshot in editing mode');

            const screenshotContainer = document.getElementById('screenshotContainer');
            const screenshotImage = document.getElementById('screenshotImage');

            if (screenshotResult.base64) {
                // 设置截图图片
                // 🚀 PERFORMANCE: 支持JPEG格式，提升加载速度
                screenshotImage.src = `data:image/jpeg;base64,${screenshotResult.base64}`;

                // 设置容器位置和大小
                screenshotContainer.style.left = `${region.x}px`;
                screenshotContainer.style.top = `${region.y}px`;
                screenshotContainer.style.width = `${region.width}px`;
                screenshotContainer.style.height = `${region.height}px`;

                log.debug('🎯 Screenshot displayed at', region);

                // 🔧 BUG FIX: 立即显示快捷操作工具栏
                log.info('🎯 Showing quick action toolbar for editing mode');
                showQuickActionToolbar();

                // 🔧 BUG FIX: 添加编辑模式样式指示器
                screenshotContainer.classList.add('editing-mode');

                // 🔧 BUG FIX: 显示编辑模式提示信息
                showEditingModeInstructions();

                // 将窗口坐标传递给编辑组件（通过全局事件）
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('window-selected', {
                        x: region.x,
                        y: region.y,
                        width: region.width,
                        height: region.height,
                        imageData: screenshotResult.base64
                    });
                    log.debug('🎯 Window coordinates sent to editor', region);
                }

                // 🔧 BUG FIX: 确保鼠标交互已禁用
                log.info('🎯 Ensuring mouse interactions are disabled in editing mode');
                if (window.mouseInteractionsDisabled !== true) {
                    log.warn('🎯 Mouse interactions were not disabled, forcing disable now');
                    appStateManager.disableMouseInteractions();
                }

            } else {
                log.error('🎯 No base64 data in screenshot result');
                // 如果截图失败，回到检测模式
                appStateManager.setState(AppState.DETECTING);
            }
        }

        // 🔧 BUG FIX: 新增编辑模式指导函数
        function showEditingModeInstructions() {
            // 创建或更新编辑模式指导提示
            let instructionOverlay = document.getElementById('editingInstructions');
            if (!instructionOverlay) {
                instructionOverlay = document.createElement('div');
                instructionOverlay.id = 'editingInstructions';
                instructionOverlay.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    z-index: 10000;
                    pointer-events: none;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;
                document.body.appendChild(instructionOverlay);
            }

            instructionOverlay.textContent = '🎨 编辑模式已激活 - 请从工具栏选择编辑操作，鼠标拖拽已禁用';
            instructionOverlay.style.display = 'block';

            // 3秒后自动隐藏指导提示
            setTimeout(() => {
                if (instructionOverlay) {
                    instructionOverlay.style.display = 'none';
                }
            }, 3000);

            log.info('🎨 Editing mode instructions displayed');
        }

        function showSelectedWindow() {
            // 创建暗化遮罩
            if (!dimOverlay) {
                dimOverlay = document.createElement('div');
                dimOverlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    z-index: 999;
                    pointer-events: none;
                `;
                document.body.appendChild(dimOverlay);
            }
            dimOverlay.style.display = 'block';

            // 修改窗口高亮样式为选中状态
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '3px solid #FF6B35';
            windowHighlight.style.zIndex = '1000';
        }

        function showQuickActionToolbar() {
            // Use enhanced toolbar system if available
            if (window.ToolbarIntegration && window.ToolbarPositionManager) {
                showEnhancedToolbar();
                return;
            }

            // Fallback to legacy toolbar
            if (!quickToolbar) {
                createQuickActionToolbar();
            }

            // 定位工具栏到选中窗口的右下角
            const windowRect = windowHighlight.getBoundingClientRect();
            const toolbarX = windowRect.right - 200; // 工具栏宽度约200px
            const toolbarY = windowRect.bottom + 10;

            quickToolbar.style.left = toolbarX + 'px';
            quickToolbar.style.top = toolbarY + 'px';
            quickToolbar.style.display = 'flex';

            log.info('🛠️ Quick action toolbar displayed');
        }

        function showEnhancedToolbar() {
            try {
                // 🔧 BUG FIX: Get region from selected window data instead of DOM element
                let region = null;

                // First try to get region from selected window data
                if (appStateManager.selectedWindow) {
                    region = {
                        x: appStateManager.selectedWindow.x,
                        y: appStateManager.selectedWindow.y,
                        width: appStateManager.selectedWindow.width,
                        height: appStateManager.selectedWindow.height
                    };
                    log.debug('🛠️ Using region from selected window data:', region);
                } else {
                    // Fallback to window highlight element if visible
                    const windowRect = windowHighlight.getBoundingClientRect();
                    if (windowRect.width > 0 && windowRect.height > 0) {
                        region = {
                            x: windowRect.left,
                            y: windowRect.top,
                            width: windowRect.width,
                            height: windowRect.height
                        };
                        log.debug('🛠️ Using region from window highlight element:', region);
                    } else {
                        log.warn('🛠️ No valid region available for toolbar positioning');
                        return;
                    }
                }

                // Validate region dimensions
                if (!region || region.width <= 0 || region.height <= 0) {
                    log.warn('🛠️ Invalid region dimensions for toolbar:', region);
                    return;
                }

                // 🔧 ISSUE 2 FIX: 确保工具栏模块已加载
                if (!window.ToolbarIntegration || !window.ToolbarIntegration.EnhancedQuickActionToolbar) {
                    log.error('🛠️ Enhanced toolbar modules not loaded, falling back to legacy toolbar');
                    showQuickActionToolbar();
                    return;
                }

                // Initialize enhanced toolbar if not exists
                if (!enhancedToolbar) {
                    log.info('🛠️ Creating new enhanced toolbar with drag functionality');
                    enhancedToolbar = new window.ToolbarIntegration.EnhancedQuickActionToolbar({
                        draggable: true,
                        showEditTools: true,
                        showCaptureTools: false,
                        showSaveOptions: true
                    });

                    // 🔧 ISSUE 2 FIX: 确保工具栏正确创建并启用拖拽
                    const toolbarElement = enhancedToolbar.create(region);
                    if (!toolbarElement) {
                        log.error('🛠️ Failed to create toolbar element');
                        return;
                    }

                    // Setup event listeners for toolbar actions
                    document.addEventListener('toolbar-action', handleEnhancedToolbarAction);

                    log.info('🛠️ Enhanced toolbar created with drag functionality enabled');
                } else {
                    // 🔧 ISSUE 2 FIX: 更新现有工具栏位置
                    if (enhancedToolbar.updateRegion) {
                        enhancedToolbar.updateRegion(region);
                    } else if (enhancedToolbar.attachToRegion) {
                        enhancedToolbar.attachToRegion(region, true);
                    }
                }

                // 🔧 ISSUE 2 FIX: 确保工具栏显示
                if (enhancedToolbar.show) {
                    enhancedToolbar.show();
                } else if (enhancedToolbar.element) {
                    enhancedToolbar.element.style.display = 'flex';
                }

                log.info('🛠️ Enhanced toolbar displayed successfully with drag support');
            } catch (error) {
                log.error('🛠️ Failed to show enhanced toolbar:', error);
                // Fallback to legacy toolbar
                showQuickActionToolbar();
            }
        }

        function handleEnhancedToolbarAction(event) {
            const { action, region } = event.detail;
            log.info('🛠️ Enhanced toolbar action:', action);

            switch (action) {
                case 'add_text':
                    handleToolbarAction('text');
                    break;
                case 'draw':
                    handleToolbarAction('brush');
                    break;
                case 'arrow':
                    handleToolbarAction('arrow');
                    break;
                case 'circle':
                    handleToolbarAction('ellipse');
                    break;
                case 'rectangle_select':
                    handleToolbarAction('rectangle');
                    break;
                case 'save':
                    handleToolbarAction('save');
                    break;
                case 'copy':
                    handleToolbarAction('copy');
                    break;
                case 'cancel':
                    handleToolbarAction('close');
                    break;
                default:
                    log.warn('🛠️ Unknown enhanced toolbar action:', action);
            }
        }

        function createQuickActionToolbar() {
            quickToolbar = document.createElement('div');
            quickToolbar.style.cssText = `
                position: fixed;
                display: none;
                flex-direction: row;
                gap: 8px;
                padding: 12px;
                background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
                border: 1px solid rgba(255, 107, 53, 0.3);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(10px);
                z-index: 1002;
                pointer-events: auto;
            `;

            // 创建工具栏按钮
            const actions = [
                { icon: '📐', title: '矩形', action: 'rectangle' },
                { icon: '⭕', title: '椭圆', action: 'ellipse' },
                { icon: '📝', title: '文字', action: 'text' },
                { icon: '🖌️', title: '画笔', action: 'brush' },
                { icon: '➡️', title: '箭头', action: 'arrow' },
                { icon: '🔢', title: '序号', action: 'number' },
                { icon: '📏', title: '直线', action: 'line' },
                { icon: '⚡', title: '虚线', action: 'dashed' },
                { icon: '🔲', title: '马赛克', action: 'mosaic' },
                { icon: '📌', title: '固定', action: 'pin' },
                { icon: '↶', title: '撤销', action: 'undo' },
                { icon: '🎬', title: '录制', action: 'record' },
                { icon: '❌', title: '关闭', action: 'close' },
                { icon: '✅', title: '确认', action: 'confirm' }
            ];

            actions.forEach(actionDef => {
                const button = document.createElement('button');
                button.style.cssText = `
                    width: 36px;
                    height: 36px;
                    border: none;
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                `;

                button.textContent = actionDef.icon;
                button.title = actionDef.title;
                button.onclick = () => handleToolbarAction(actionDef.action);

                // 悬停效果
                button.onmouseenter = () => {
                    button.style.background = 'rgba(255, 107, 53, 0.3)';
                    button.style.transform = 'scale(1.1)';
                };
                button.onmouseleave = () => {
                    button.style.background = 'rgba(255, 255, 255, 0.1)';
                    button.style.transform = 'scale(1)';
                };

                quickToolbar.appendChild(button);
            });

            document.body.appendChild(quickToolbar);
        }

        function handleToolbarAction(action) {
            log.info('🛠️ Toolbar action: ' + action);

            switch (action) {
                case 'close':
                    exitWindowSelection();
                    break;
                case 'confirm':
                    // 🔧 BUG FIX: 使用新的窗口截图函数
                    if (appStateManager.selectedWindow) {
                        captureSelectedWindow(appStateManager.selectedWindow);
                    }
                    break;
                case 'pin':
                    pinSelectedWindow();
                    break;
                default:
                    // 其他编辑工具暂时显示提示
                    console.log('[FRONTEND] 🛠️ Edit tool not implemented yet:', action);
                    break;
            }
        }

        function exitWindowSelection() {
            log.info('🚪 Exiting window selection mode');

            // 隐藏工具栏
            if (quickToolbar) {
                quickToolbar.style.display = 'none';
            }

            // 隐藏增强工具栏
            if (enhancedToolbar) {
                enhancedToolbar.hide();
            }

            // 隐藏暗化遮罩
            if (dimOverlay) {
                dimOverlay.style.display = 'none';
            }

            // 重置窗口高亮样式
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '2px solid #FF6B35';

            // 重置状态
            isWindowSelected = false;
            selectedWindowId = null;

            // 重新启用窗口检测
            detectionEnabled = true;
        }

        // 🔧 BUG FIX: 重命名旧函数，避免与新的窗口截图函数冲突
        async function captureSelectedWindowByRegion() {
            log.warn('📸 [DEPRECATED] Using region-based window capture - this function should not be called');

            if (!appStateManager.selectedWindow) {
                console.error('[FRONTEND] 📸 No window selected for capture');
                logToBackend('ERROR', '📸 No window selected for capture');
                return null;
            }

            try {
                // 构建截图区域参数
                const area = {
                    x: appStateManager.selectedWindow.x,
                    y: appStateManager.selectedWindow.y,
                    width: appStateManager.selectedWindow.width,
                    height: appStateManager.selectedWindow.height
                };

                log.warn('🎯 [DEPRECATED] About to call capture_region_new - should use capture_window_new instead', area);

                const result = await window.__TAURI__.core.invoke('capture_region_new', {
                    area: area
                });

                log.info('🎯 Screenshot result received');
                return result;
            } catch (error) {
                log.error('🎯 Failed to capture window', error.toString());
                log.error('🎯 Error details', `${error.message} | ${error.stack}`);
                throw error;
            }
        }

        async function pinSelectedWindow() {
            console.log('[FRONTEND] 📌 Pinning selected window:', selectedWindowId);
            // TODO: 实现固定功能
        }

        async function handleKeyboard(event) {
            log.debug('🎹 Key pressed: ' + event.key + ', Current state: ' + appStateManager.currentState);

            switch (event.key.toLowerCase()) {
                case 'escape':
                    log.info('🎹 ESC key detected');
                    await handleEscapeKey();
                    break;
                case 'r':
                    // 刷新窗口列表（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        loadWindows();
                    }
                    break;
                case ' ':
                    // 空格键切换到区域选择模式（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        await handleSpacebarPress();
                    }
                    break;
                case 'd':
                    // 切换检测模式（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        detectionEnabled = !detectionEnabled;
                        if (!detectionEnabled) {
                            hideHighlight();
                        }
                    }
                    break;
            }
        }

        async function handleEscapeKey() {
            log.info('🚪 ESC key pressed, current state: ' + appStateManager.currentState);

            // 根据当前状态处理ESC键
            switch (appStateManager.currentState) {
                case AppState.EDITING:
                    // 从编辑模式返回检测模式
                    log.info('🚪 Exiting editing mode');
                    appStateManager.setState(AppState.DETECTING, {
                        selectedWindow: null,
                        screenshotData: null,
                        editingRegion: null
                    });
                    break;
                case AppState.CAPTURING:
                    // 取消截图操作
                    log.info('🚪 Canceling capture operation');
                    appStateManager.setState(AppState.DETECTING);
                    break;
                case AppState.DETECTING:
                default:
                    // 完全退出应用
                    log.info('🚪 Exiting application');
                    await exitApplication();
                    break;
            }
        }

        async function exitApplication() {
            log.info('🚪 Attempting to exit application');
            log.debug('🚪 Tauri available: ' + !!window.__TAURI__);

            try {
                if (window.__TAURI__) {
                    log.info('🚪 Calling exit_capture_completely_direct...');
                    await window.__TAURI__.core.invoke('exit_capture_completely_direct');
                    log.info('🚪 Direct exit command completed successfully');
                } else {
                    log.error('🚪 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🚪 Failed to exit capture directly:', error);
                console.error('[FRONTEND] 🚪 Error details:', error.message || error);
                // 降级处理：直接关闭当前窗口
                try {
                    console.log('[FRONTEND] 🚪 Attempting fallback window close...');
                    await window.__TAURI__.window.getCurrent().close();
                    console.log('[FRONTEND] 🚪 Fallback window close completed');
                } catch (closeError) {
                    console.error('[FRONTEND] 🚪 Failed to close window:', closeError);
                }
            }
        }

        async function handleSpacebarPress() {
            try {
                if (window.__TAURI__) {
                    const overlayId = window.location.hash.substring(1) || 'window_highlight_overlay';
                    await window.__TAURI__.core.invoke('handle_spacebar_press', {
                        window_id: overlayId
                    });
                    console.log('Spacebar handled - switching to region selection mode');
                }
            } catch (error) {
                console.error('Failed to handle spacebar press:', error);
            }
        }

        function handleMouseDown(event) {
            // 🔧 BUG FIX: 在编辑模式下禁用鼠标拖拽操作
            if (appStateManager.currentState === AppState.EDITING) {
                log.info('🚫 Mouse down ignored - editing mode active, mouse interactions disabled');
                event.preventDefault();
                event.stopPropagation();
                return;
            }

            log.debug('🖱️ Mouse down detected at: ' + event.clientX + ', ' + event.clientY);
            log.trace('🖱️ Event target: ' + (event.target.className || event.target.tagName));
            log.trace('🖱️ Event capture layer: ' + (event.target === eventCaptureLayer));
            log.debug('🖱️ Current state: ' + appStateManager.currentState);
            log.trace('🖱️ Selected window', appStateManager.selectedWindow);

            isDragging = true;
            dragStartX = event.clientX;
            dragStartY = event.clientY;

            // 🔧 BUG FIX: 保存点击坐标用于后续截图
            lastClickX = event.clientX;
            lastClickY = event.clientY;

            // 为可能的拖拽操作做准备，但不立即隐藏高亮
            // hideHighlight();

            log.trace('🖱️ Drag state initialized - start position: ' + dragStartX + ', ' + dragStartY);
        }

        function handleMouseUp(event) {
            // 🔧 BUG FIX: 在编辑模式下禁用鼠标拖拽操作
            if (appStateManager.currentState === AppState.EDITING) {
                log.info('🚫 Mouse up ignored - editing mode active, mouse interactions disabled');
                event.preventDefault();
                event.stopPropagation();
                return;
            }

            log.debug('🖱️ Mouse up detected at: ' + event.clientX + ', ' + event.clientY);
            log.trace('🖱️ isDragging state: ' + isDragging);
            log.debug('🖱️ Current state: ' + appStateManager.currentState);
            log.trace('🖱️ Selected window', appStateManager.selectedWindow);

            if (isDragging) {
                const dragEndX = event.clientX;
                const dragEndY = event.clientY;
                const dragDistance = Math.sqrt(
                    Math.pow(dragEndX - dragStartX, 2) + Math.pow(dragEndY - dragStartY, 2)
                );

                log.trace('🖱️ Drag calculation - Start: ' + dragStartX + ', ' + dragStartY +
                         ', End: ' + dragEndX + ', ' + dragEndY +
                         ', Distance: ' + dragDistance + ', Threshold exceeded: ' + (dragDistance > 10));

                if (dragDistance > 10) {
                    log.info('🎯 Drag threshold exceeded! Switching to region selection mode');
                    handleMouseDragStart(dragStartX, dragStartY);

                    // 🔧 BUG FIX: 在拖拽结束时捕获区域截图
                    if (appStateManager.selectedRegion) {
                        captureSelectedRegion(appStateManager.selectedRegion);
                    }
                } else {
                    log.trace('🖱️ Drag distance is small, treating as a click.');
                    // 检查是否在检测模式下，并且有选中的窗口
                    if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                        log.debug('🖱️ Triggering window selection from mouse up.');
                        handleWindowSelection();
                    } else {
                        log.trace('🖱️ Click ignored: Not in DETECTING state or no window selected.');
                    }
                }
            } else {
                console.log('[FRONTEND] 🖱️ Mouse up without drag state');
                // 直接点击处理（非拖拽结束）
                if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                    console.log('[FRONTEND] 🖱️ Handling direct click on window');
                    handleWindowSelection();
                }
            }

            isDragging = false;
            console.log('[FRONTEND] 🖱️ Drag state reset');
        }

        // 独立的窗口检测函数，支持异步调用和错误处理
        async function performWindowDetection(x, y) {
            // 🔧 CRITICAL FIX: 移除重复的状态检查，修复窗口检测
            if (appStateManager.currentState !== AppState.DETECTING) {
                logWithLevel(LOG_LEVELS.DEBUG, `🧠 Skipping window detection - not in detecting state (${appStateManager.currentState})`);
                return;
            }

            try {
                const startTime = performance.now();

                // 🔧 CRITICAL FIX: 使用新的日志系统，确保窗口检测日志始终显示
                logWithLevel(LOG_LEVELS.INFO, `🧠 Performing window detection at: ${x}, ${y}`);
                lastLogTime = Date.now();

                // 🔧 ISSUE 3 FIX: 优先使用智能检测API，它具有更好的z-order处理
                let detectedWindow = null;
                try {
                    // 首先尝试使用智能检测API（具有增强的z-order处理）
                    detectedWindow = await window.__TAURI__.core.invoke('detect_window_smart', {
                        x: x,
                        y: y
                    });

                    if (detectedWindow) {
                        logWithLevel(LOG_LEVELS.INFO, '🧠 Smart detection (enhanced z-order) result:', detectedWindow);
                    } else {
                        logWithLevel(LOG_LEVELS.DEBUG, '🧠 Smart detection found no window at coordinates');
                    }
                } catch (smartError) {
                    logWithLevel(LOG_LEVELS.WARN, `🧠 Smart detection failed: ${smartError.message || smartError}`);

                    // 降级到cursor检测API
                    try {
                        detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_cursor', {
                            x: x,
                            y: y
                        });
                        logWithLevel(LOG_LEVELS.INFO, '🧠 Cursor detection result:', detectedWindow);
                    } catch (cursorError) {
                        logWithLevel(LOG_LEVELS.WARN, `🧠 Cursor detection failed: ${cursorError.message || cursorError}`);

                        // 最后降级到实时检测API
                        try {
                            detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_mouse_realtime', {
                                x: x,
                                y: y,
                                exclude_overlay_windows: true
                            });
                            logWithLevel(LOG_LEVELS.INFO, '🧠 Realtime detection result:', detectedWindow);
                        } catch (fallbackError) {
                            logWithLevel(LOG_LEVELS.ERROR, `🧠 All detection methods failed: ${fallbackError.message || fallbackError}`);
                            return;
                        }
                    }
                }

                const endTime = performance.now();
                const detectionTimeMs = Math.round(endTime - startTime);
                updateDebugElement('detectionTime', `${detectionTimeMs}ms`);

                if (detectedWindow) {
                    const windowTitle = detectedWindow.title || detectedWindow.app_name || 'No title';
                    const windowProcessName = detectedWindow.process_name || detectedWindow.app_name || 'Unknown';

                    if (Date.now() - lastLogTime > LOG_THROTTLE) {
                        log.trace('🧠 Window detected: ' + windowTitle + ', Process: ' + windowProcessName);
                    }

                    // 缓存检测到的窗口信息到状态管理器
                    appStateManager.selectedWindow = detectedWindow;

                    highlightWindow(detectedWindow);
                    updateDebugElement('currentWindow', windowTitle.substring(0, 20) + (windowTitle.length > 20 ? '...' : ''));
                    updateDebugElement('apiUsed', 'Smart');
                    updateDebugElement('processName', windowProcessName.substring(0, 15));

                    // 更新窗口计数
                    updateDebugElement('windowCount', '1');
                } else {
                    if (Date.now() - lastLogTime > LOG_THROTTLE) {
                        log.trace('🧠 No window detected at position');
                    }

                    // 清除缓存的窗口信息
                    appStateManager.selectedWindow = null;

                    hideHighlight();
                    updateDebugElement('currentWindow', 'None');
                    updateDebugElement('apiUsed', 'Smart');
                    updateDebugElement('processName', '-');
                    updateDebugElement('windowCount', '0');
                }
            } catch (error) {
                console.error('[FRONTEND] ❌ Window detection error:', error.message || error);

                // 显示错误状态
                hideHighlight();
                updateDebugElement('currentWindow', 'Error');
                updateDebugElement('apiUsed', 'Error');
                updateDebugElement('processName', 'Error');
                updateDebugElement('detectionTime', 'Error');
            }
        }

        // 🔧 BUG FIX: 更新区域选择UI
        function updateRegionSelector(startX, startY, endX, endY) {
            const regionSelector = document.getElementById('regionSelector');
            const regionDimensions = document.getElementById('regionDimensions');

            if (!regionSelector || !regionDimensions) return;

            // 计算区域位置和大小
            const left = Math.min(startX, endX);
            const top = Math.min(startY, endY);
            const width = Math.abs(endX - startX);
            const height = Math.abs(endY - startY);

            // 更新区域选择器样式
            regionSelector.style.display = 'block';
            regionSelector.style.left = `${left}px`;
            regionSelector.style.top = `${top}px`;
            regionSelector.style.width = `${width}px`;
            regionSelector.style.height = `${height}px`;

            // 更新尺寸显示
            regionDimensions.textContent = `${width} x ${height}`;

            // 记录当前选择的区域
            appStateManager.selectedRegion = {
                x: left,
                y: top,
                width: width,
                height: height
            };

            logWithLevel(LOG_LEVELS.INFO, `🔍 Region selected: ${left},${top} ${width}x${height}`);
        }

        async function handleMouseDragStart(startX, startY) {
            console.log('[FRONTEND] 🎯 handleMouseDragStart called with:', startX, startY);

            try {
                if (window.__TAURI__) {
                    // 🔧 CRITICAL FIX: 使用固定的窗口ID以匹配capabilities配置
                    const overlayId = 'window_highlight_overlay';
                    console.log('[FRONTEND] 🎯 Calling backend with overlay ID:', overlayId);
                    console.log('[FRONTEND] 🎯 Invoking handle_mouse_drag_start...');

                    // 🔧 CRITICAL FIX: 确保参数名称与后端匹配
                    const result = await window.__TAURI__.core.invoke('handle_mouse_drag_start', {
                        window_id: overlayId,  // 正确的参数名称是window_id，不是windowId
                        start_x: startX,
                        start_y: startY
                    });

                    console.log('[FRONTEND] 🎯 Backend response:', result);
                    console.log('[FRONTEND] 🎯 Mouse drag start handled - switching to region selection mode');

                    // 🔧 PRODUCTION: modeIndicator已移除，保持界面干净
                } else {
                    console.error('[FRONTEND] 🎯 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🎯 Failed to handle mouse drag start:', error);
                console.error('[FRONTEND] 🎯 Error details:', error.message || error);
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 检查macOS权限
        async function checkMacOSPermissions() {
            try {
                if (!window.__TAURI__) {
                    console.log('[FRONTEND] 🔍 Tauri API not available, skipping permission check');
                    return;
                }

                console.log('[FRONTEND] 🔍 Checking macOS permissions...');

                // 使用安全的权限检查方式，避免调用受限API
                try {
                    const hasPermission = await window.__TAURI__.core.invoke('check_macos_permissions');

                    if (hasPermission) {
                        console.log('[FRONTEND] ✅ macOS screen recording permission granted');
                        updateStatus('API', 'Smart');
                    } else {
                        console.warn('[FRONTEND] ⚠️ macOS screen recording permission not granted');
                        updateStatus('API', 'Limited');
                        showPermissionWarning();
                    }
                } catch (permissionError) {
                    console.warn('[FRONTEND] ⚠️ Permission check API not available:', permissionError.message || permissionError);
                    // 降级处理：假设有权限，让用户在实际使用时发现问题
                    updateStatus('API', 'Unknown');
                }
            } catch (error) {
                console.error('[FRONTEND] ❌ Failed to check macOS permissions:', error);
                // 如果权限检查失败，可能是非macOS平台
                console.log('[FRONTEND] 🔍 Permission check failed, likely not macOS platform');
                updateStatus('API', 'Unknown');
            }
        }

        // 🔧 CRITICAL FIX: 添加缺失的updateStatus函数
        function updateStatus(type, value) {
            logWithLevel(LOG_LEVELS.INFO, `🔧 Status update: ${type} = ${value}`);
            // 更新UI状态（如果有相关元素）
            const statusElement = document.getElementById(`${type.toLowerCase()}Status`);
            if (statusElement) {
                statusElement.textContent = value;
            }
        }

        // 显示权限警告
        function showPermissionWarning() {
            // 🔧 PRODUCTION: 权限警告功能已简化，调试元素已移除
            log.warn('⚠️ macOS screen recording permission required for full functionality');
        }

        // 定期刷新窗口列表
        setInterval(() => {
            if (detectionEnabled) {
                loadWindows();
            }
        }, 2000);

        // 确保透明度设置
        document.body.style.setProperty('background-color', 'transparent', 'important');
        document.documentElement.style.setProperty('background-color', 'transparent', 'important');

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            log.error('🚨 Global error: ' + event.error);
            log.error('🚨 Error message: ' + event.message);
            log.error('🚨 Error filename: ' + event.filename);
            log.error('🚨 Error line: ' + event.lineno);
        });

        window.addEventListener('unhandledrejection', function(event) {
            log.error('🚨 Unhandled promise rejection: ' + event.reason);
        });

        console.log('[FRONTEND] 🎯 Window highlight overlay loaded');
        console.log('[FRONTEND] 🎯 Body background:', window.getComputedStyle(document.body).backgroundColor);
        console.log('[FRONTEND] 🎯 HTML background:', window.getComputedStyle(document.documentElement).backgroundColor);

        // 立即检查macOS权限（不等待）
        setTimeout(() => {
            checkMacOSPermissions().catch(error => {
                console.error('[FRONTEND] 🚨 macOS permission check failed:', error);
            });
        }, 100);

        // 检查窗口焦点状态
        console.log('[FRONTEND] 🎯 Document has focus:', document.hasFocus());
        console.log('[FRONTEND] 🎯 Active element:', document.activeElement.tagName);
        console.log('[FRONTEND] 🎯 Tauri available:', !!window.__TAURI__);

        // 强制设置焦点到document
        if (!document.hasFocus()) {
            console.log('[FRONTEND] 🎯 Document does not have focus, attempting to focus...');
            window.focus();
            document.body.focus();
        }

        // 测试键盘事件监听
        console.log('[FRONTEND] 🎯 Testing keyboard event listener...');
        console.log('[FRONTEND] 🎯 Event listeners attached to document');

        // 立即测试一个键盘事件
        document.addEventListener('keydown', (e) => {
            console.log('[FRONTEND] 🎯 IMMEDIATE KEY TEST - Key pressed:', e.key);
        }, { once: true });

        setTimeout(() => {
            console.log('[FRONTEND] 🎯 Please press ESC key to test...');
            console.log('[FRONTEND] 🎯 Current focus element:', document.activeElement);
            console.log('[FRONTEND] 🎯 Document visibility:', document.visibilityState);
        }, 1000);
    </script>
</body>
</html>
