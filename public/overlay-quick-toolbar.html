<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Quick Toolbar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toolbar-container {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            min-width: 300px;
        }

        .toolbar-container:hover {
            background: rgba(0, 0, 0, 0.95);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .toolbar-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 40px;
            justify-content: center;
        }

        .toolbar-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .toolbar-btn:active {
            transform: translateY(0);
        }

        .toolbar-btn.primary {
            background: #007AFF;
        }

        .toolbar-btn.primary:hover {
            background: #0056CC;
        }

        .toolbar-btn.success {
            background: #34C759;
        }

        .toolbar-btn.success:hover {
            background: #28A745;
        }

        .toolbar-btn.danger {
            background: #FF3B30;
        }

        .toolbar-btn.danger:hover {
            background: #D70015;
        }

        .toolbar-btn.secondary {
            background: rgba(255, 255, 255, 0.15);
        }

        .toolbar-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .toolbar-separator {
            width: 1px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 4px;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .toolbar-btn .icon {
            font-size: 14px;
        }

        .toolbar-btn .label {
            font-size: 11px;
            opacity: 0.9;
        }

        .toolbar-btn.icon-only {
            padding: 8px;
            min-width: 32px;
        }

        .toolbar-btn.icon-only .label {
            display: none;
        }

        .drag-handle {
            cursor: move;
            padding: 4px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        .drag-handle:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        .toolbar-container.compact {
            padding: 6px 8px;
            min-width: 200px;
        }

        .toolbar-container.compact .toolbar-btn {
            padding: 6px 8px;
            font-size: 11px;
        }

        .toolbar-container.compact .toolbar-btn .icon {
            font-size: 12px;
        }

        .auto-hide-indicator {
            position: absolute;
            top: -20px;
            right: 0;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .auto-hide-indicator.visible {
            opacity: 1;
        }

        .position-indicator {
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid rgba(255, 255, 255, 0.3);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .toolbar-container {
            animation: slideIn 0.3s ease-out;
        }

        @media (max-width: 600px) {
            .toolbar-container {
                min-width: 250px;
                padding: 6px 8px;
            }
            
            .toolbar-btn .label {
                display: none;
            }
            
            .toolbar-btn {
                padding: 8px;
                min-width: 32px;
            }
        }
    </style>
</head>
<body>
    <div class="toolbar-container" id="toolbar">
        <div class="position-indicator"></div>
        <div class="auto-hide-indicator" id="autoHideIndicator">Auto-hide in 5s</div>
        
        <div class="drag-handle" id="dragHandle">⋮⋮</div>
        
        <div class="toolbar-group">
            <button class="toolbar-btn primary" onclick="handleAction('capture_region')" title="Capture Region">
                <span class="icon">📐</span>
                <span class="label">Region</span>
            </button>
            
            <button class="toolbar-btn primary" onclick="handleAction('capture_window')" title="Capture Window">
                <span class="icon">🪟</span>
                <span class="label">Window</span>
            </button>
        </div>
        
        <div class="toolbar-separator"></div>
        
        <div class="toolbar-group">
            <button class="toolbar-btn secondary" onclick="handleAction('edit_screenshot')" title="Edit Screenshot">
                <span class="icon">✏️</span>
                <span class="label">Edit</span>
            </button>
            
            <button class="toolbar-btn secondary" onclick="handleAction('copy_to_clipboard')" title="Copy to Clipboard">
                <span class="icon">📋</span>
                <span class="label">Copy</span>
            </button>
            
            <button class="toolbar-btn success" onclick="handleAction('save_screenshot')" title="Save Screenshot">
                <span class="icon">💾</span>
                <span class="label">Save</span>
            </button>
        </div>
        
        <div class="toolbar-separator"></div>
        
        <div class="toolbar-group">
            <button class="toolbar-btn danger" onclick="handleAction('close_toolbar')" title="Close">
                <span class="icon">❌</span>
                <span class="label">Close</span>
            </button>
        </div>
    </div>

    <script>
        let toolbarConfig = {
            show_capture_tools: true,
            show_edit_tools: true,
            show_save_options: true,
            toolbar_position: 'bottom',
            auto_hide_delay: 5
        };
        
        let parentOverlayId = null;
        let autoHideTimer = null;
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        const toolbar = document.getElementById('toolbar');
        const autoHideIndicator = document.getElementById('autoHideIndicator');
        const dragHandle = document.getElementById('dragHandle');

        // 初始化
        initializeToolbar();

        function initializeToolbar() {
            setupEventListeners();
            startAutoHideTimer();
            
            // 监听配置更新
            if (window.__TAURI__) {
                window.__TAURI__.event.listen('toolbar-config', (event) => {
                    toolbarConfig = event.payload;
                    applyConfig();
                });
                
                window.__TAURI__.event.listen('parent-overlay', (event) => {
                    parentOverlayId = event.payload;
                });
            }
        }

        function applyConfig() {
            // 应用工具栏配置
            const captureGroup = toolbar.querySelector('.toolbar-group:first-child');
            const editGroup = toolbar.querySelector('.toolbar-group:nth-child(3)');
            
            captureGroup.style.display = toolbarConfig.show_capture_tools ? 'flex' : 'none';
            editGroup.style.display = toolbarConfig.show_edit_tools ? 'flex' : 'none';
            
            // 应用位置样式
            if (toolbarConfig.toolbar_position === 'top') {
                toolbar.style.marginTop = '20px';
            } else if (toolbarConfig.toolbar_position === 'bottom') {
                toolbar.style.marginBottom = '20px';
            }
            
            // 重新启动自动隐藏计时器
            if (toolbarConfig.auto_hide_delay > 0) {
                startAutoHideTimer();
            }
        }

        function setupEventListeners() {
            // 拖拽功能
            dragHandle.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
            
            // 鼠标悬停取消自动隐藏
            toolbar.addEventListener('mouseenter', () => {
                clearAutoHideTimer();
                autoHideIndicator.classList.remove('visible');
            });
            
            toolbar.addEventListener('mouseleave', () => {
                if (toolbarConfig.auto_hide_delay > 0) {
                    startAutoHideTimer();
                }
            });
            
            // 键盘快捷键
            document.addEventListener('keydown', handleKeyboard);
        }

        function startDrag(event) {
            isDragging = true;
            const rect = toolbar.getBoundingClientRect();
            dragOffset.x = event.clientX - rect.left;
            dragOffset.y = event.clientY - rect.top;
            toolbar.style.cursor = 'grabbing';
        }

        function drag(event) {
            if (!isDragging) return;
            
            const x = event.clientX - dragOffset.x;
            const y = event.clientY - dragOffset.y;
            
            // 更新工具栏位置
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('update_toolbar_position', {
                    toolbarId: 'current', // 当前工具栏ID
                    x: x,
                    y: y
                }).catch(error => {
                    console.error('Failed to update toolbar position:', error);
                });
            }
        }

        function endDrag() {
            isDragging = false;
            toolbar.style.cursor = 'default';
        }

        function startAutoHideTimer() {
            clearAutoHideTimer();
            
            if (toolbarConfig.auto_hide_delay <= 0) return;
            
            let countdown = toolbarConfig.auto_hide_delay;
            autoHideIndicator.textContent = `Auto-hide in ${countdown}s`;
            autoHideIndicator.classList.add('visible');
            
            autoHideTimer = setInterval(() => {
                countdown--;
                autoHideIndicator.textContent = `Auto-hide in ${countdown}s`;
                
                if (countdown <= 0) {
                    handleAction('close_toolbar');
                }
            }, 1000);
        }

        function clearAutoHideTimer() {
            if (autoHideTimer) {
                clearInterval(autoHideTimer);
                autoHideTimer = null;
            }
        }

        function handleAction(actionType) {
            const action = {
                action_type: actionType,
                data: {},
                timestamp: Date.now()
            };

            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('handle_toolbar_action', {
                    action: action
                }).then(result => {
                    console.log('Toolbar action completed:', result);
                    
                    // 某些操作后关闭工具栏
                    if (['capture_region', 'capture_window', 'close_toolbar'].includes(actionType)) {
                        setTimeout(() => {
                            window.__TAURI__.window.getCurrent().close();
                        }, 100);
                    }
                }).catch(error => {
                    console.error('Failed to handle toolbar action:', error);
                });
            }
        }

        function handleKeyboard(event) {
            switch (event.key.toLowerCase()) {
                case 'escape':
                    handleAction('close_toolbar');
                    break;
                case '1':
                    handleAction('capture_region');
                    break;
                case '2':
                    handleAction('capture_window');
                    break;
                case 'e':
                    handleAction('edit_screenshot');
                    break;
                case 'c':
                    handleAction('copy_to_clipboard');
                    break;
                case 's':
                    handleAction('save_screenshot');
                    break;
            }
        }

        console.log('Quick action toolbar loaded');
    </script>
</body>
</html>
