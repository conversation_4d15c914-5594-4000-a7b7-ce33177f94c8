<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏拖拽和最大化窗口测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .test-section:last-child {
            border-bottom: none;
        }

        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: #FF6B35;
            color: white;
        }

        .btn-primary:hover {
            background: #e55a2b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .test-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .mock-region {
            position: fixed;
            background: rgba(255, 107, 53, 0.2);
            border: 2px solid #FF6B35;
            border-radius: 8px;
            display: none;
            z-index: 1000;
            pointer-events: none;
        }

        .mock-region-label {
            position: absolute;
            top: -30px;
            left: 0;
            background: #FF6B35;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        .instructions h4 {
            margin-bottom: 10px;
            color: #533f03;
        }

        .instructions ul {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔧 工具栏拖拽和最大化窗口测试</h1>
            <p>测试工具栏拖拽功能和最大化窗口时的显示问题</p>
        </div>

        <!-- 拖拽功能测试 -->
        <div class="test-section">
            <h2>问题2: 工具栏拖拽功能测试</h2>
            
            <div class="instructions">
                <h4>测试说明：</h4>
                <ul>
                    <li>点击"测试拖拽功能"按钮创建工具栏</li>
                    <li>查找工具栏左侧的拖拽手柄（⋮⋮）</li>
                    <li>鼠标悬停在拖拽手柄上应该有视觉反馈</li>
                    <li>按住拖拽手柄并移动鼠标来拖动工具栏</li>
                    <li>拖拽时工具栏应该有透明度和缩放效果</li>
                </ul>
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testDragFunctionality()">测试拖拽功能</button>
                <button class="btn btn-secondary" onclick="testDragVisualFeedback()">测试视觉反馈</button>
                <button class="btn btn-secondary" onclick="clearToolbars()">清除工具栏</button>
            </div>
            
            <div id="drag-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- 最大化窗口测试 -->
        <div class="test-section">
            <h2>问题1: 最大化窗口工具栏显示测试</h2>
            
            <div class="instructions">
                <h4>测试说明：</h4>
                <ul>
                    <li>点击"测试最大化窗口"按钮模拟最大化窗口截图</li>
                    <li>工具栏应该显示在屏幕顶部（因为下方没有空间）</li>
                    <li>工具栏应该始终可见，不被其他元素遮挡</li>
                    <li>工具栏应该有足够高的z-index确保在最上层</li>
                </ul>
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testMaximizedWindow()">测试最大化窗口</button>
                <button class="btn btn-secondary" onclick="testFullscreenWindow()">测试全屏窗口</button>
                <button class="btn btn-secondary" onclick="testConstrainedSpace()">测试空间受限</button>
            </div>
            
            <div id="maximized-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <!-- Mock regions for testing -->
    <div id="mockRegion1" class="mock-region">
        <div class="mock-region-label">普通窗口</div>
    </div>
    <div id="mockRegion2" class="mock-region">
        <div class="mock-region-label">最大化窗口</div>
    </div>

    <script type="module">
        import { ToolbarPositionManager } from '../src/utils/ToolbarPositionManager.js';
        import { EnhancedQuickActionToolbar } from '../src/utils/ToolbarIntegration.js';

        let testToolbar = null;
        let positionManager = null;

        // 初始化
        positionManager = new ToolbarPositionManager();

        // 拖拽功能测试
        window.testDragFunctionality = function() {
            const result = document.getElementById('drag-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.textContent = '正在测试拖拽功能...';

            try {
                // 清除现有工具栏
                if (testToolbar) {
                    testToolbar.destroy();
                }

                // 创建新工具栏
                testToolbar = new EnhancedQuickActionToolbar({
                    draggable: true,
                    showEditTools: true,
                    showSaveOptions: true
                });

                const testRegion = {
                    x: 300,
                    y: 300,
                    width: 400,
                    height: 200
                };

                // 显示模拟区域
                const mockRegion = document.getElementById('mockRegion1');
                mockRegion.style.display = 'block';
                mockRegion.style.left = testRegion.x + 'px';
                mockRegion.style.top = testRegion.y + 'px';
                mockRegion.style.width = testRegion.width + 'px';
                mockRegion.style.height = testRegion.height + 'px';

                testToolbar.create(testRegion);
                testToolbar.show();

                // 检查拖拽手柄
                setTimeout(() => {
                    const dragHandle = testToolbar.element?.querySelector('.toolbar-drag-handle');
                    if (dragHandle) {
                        result.className = 'test-result success';
                        result.textContent = `✅ 拖拽功能测试成功！\n找到拖拽手柄，请尝试拖动工具栏。\n拖拽手柄样式: ${dragHandle.style.cssText}`;
                    } else {
                        result.className = 'test-result error';
                        result.textContent = `❌ 拖拽功能测试失败：未找到拖拽手柄`;
                    }
                }, 500);

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 拖拽功能测试失败: ${error.message}`;
            }
        };

        window.testDragVisualFeedback = function() {
            const result = document.getElementById('drag-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.textContent = '正在测试视觉反馈...';

            try {
                if (!testToolbar || !testToolbar.element) {
                    throw new Error('请先运行"测试拖拽功能"');
                }

                const dragHandle = testToolbar.element.querySelector('.toolbar-drag-handle');
                if (!dragHandle) {
                    throw new Error('未找到拖拽手柄');
                }

                // 模拟鼠标悬停
                dragHandle.dispatchEvent(new MouseEvent('mouseenter'));
                
                setTimeout(() => {
                    const computedStyle = window.getComputedStyle(dragHandle);
                    result.className = 'test-result success';
                    result.textContent = `✅ 视觉反馈测试成功！\n悬停透明度: ${computedStyle.opacity}\n背景色: ${computedStyle.backgroundColor}\n变换: ${computedStyle.transform}`;
                }, 200);

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 视觉反馈测试失败: ${error.message}`;
            }
        };

        window.testMaximizedWindow = function() {
            const result = document.getElementById('maximized-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.textContent = '正在测试最大化窗口...';

            try {
                // 清除现有工具栏
                if (testToolbar) {
                    testToolbar.destroy();
                }

                // 创建新工具栏
                testToolbar = new EnhancedQuickActionToolbar({
                    draggable: true,
                    showEditTools: true,
                    showSaveOptions: true
                });

                // 模拟最大化窗口（占满整个视口）
                const maximizedRegion = {
                    x: 0,
                    y: 0,
                    width: window.innerWidth,
                    height: window.innerHeight
                };

                // 显示模拟区域
                const mockRegion = document.getElementById('mockRegion2');
                mockRegion.style.display = 'block';
                mockRegion.style.left = '0px';
                mockRegion.style.top = '0px';
                mockRegion.style.width = '100vw';
                mockRegion.style.height = '100vh';
                mockRegion.style.background = 'rgba(255, 107, 53, 0.1)';

                testToolbar.create(maximizedRegion);
                testToolbar.show();

                setTimeout(() => {
                    if (testToolbar.element) {
                        const toolbarRect = testToolbar.element.getBoundingClientRect();
                        const zIndex = window.getComputedStyle(testToolbar.element).zIndex;
                        
                        result.className = 'test-result success';
                        result.textContent = `✅ 最大化窗口测试成功！\n工具栏位置: (${Math.round(toolbarRect.left)}, ${Math.round(toolbarRect.top)})\nz-index: ${zIndex}\n工具栏应该显示在屏幕顶部且可见`;
                    } else {
                        result.className = 'test-result error';
                        result.textContent = `❌ 最大化窗口测试失败：工具栏元素不存在`;
                    }
                }, 500);

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 最大化窗口测试失败: ${error.message}`;
            }
        };

        window.testFullscreenWindow = function() {
            const result = document.getElementById('maximized-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.textContent = '正在测试全屏窗口...';

            try {
                if (testToolbar) {
                    testToolbar.destroy();
                }

                testToolbar = new EnhancedQuickActionToolbar({
                    draggable: true,
                    showEditTools: true,
                    showSaveOptions: true
                });

                // 全屏窗口
                const fullscreenRegion = {
                    x: 0,
                    y: 0,
                    width: window.innerWidth,
                    height: window.innerHeight
                };

                testToolbar.create(fullscreenRegion);
                testToolbar.show();

                setTimeout(() => {
                    if (testToolbar.element) {
                        const toolbarRect = testToolbar.element.getBoundingClientRect();
                        result.className = 'test-result success';
                        result.textContent = `✅ 全屏窗口测试成功！\n工具栏位置: (${Math.round(toolbarRect.left)}, ${Math.round(toolbarRect.top)})\n工具栏应该强制显示在顶部`;
                    }
                }, 500);

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 全屏窗口测试失败: ${error.message}`;
            }
        };

        window.testConstrainedSpace = function() {
            const result = document.getElementById('maximized-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.textContent = '正在测试空间受限情况...';

            try {
                if (testToolbar) {
                    testToolbar.destroy();
                }

                testToolbar = new EnhancedQuickActionToolbar({
                    draggable: true,
                    showEditTools: true,
                    showSaveOptions: true
                });

                // 接近底部的窗口
                const constrainedRegion = {
                    x: 100,
                    y: window.innerHeight - 50,
                    width: window.innerWidth - 200,
                    height: 40
                };

                testToolbar.create(constrainedRegion);
                testToolbar.show();

                setTimeout(() => {
                    if (testToolbar.element) {
                        const toolbarRect = testToolbar.element.getBoundingClientRect();
                        result.className = 'test-result success';
                        result.textContent = `✅ 空间受限测试成功！\n工具栏位置: (${Math.round(toolbarRect.left)}, ${Math.round(toolbarRect.top)})\n工具栏应该显示在窗口上方`;
                    }
                }, 500);

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 空间受限测试失败: ${error.message}`;
            }
        };

        window.clearToolbars = function() {
            if (testToolbar) {
                testToolbar.destroy();
                testToolbar = null;
            }
            
            // 隐藏模拟区域
            document.getElementById('mockRegion1').style.display = 'none';
            document.getElementById('mockRegion2').style.display = 'none';
            
            // 清除结果
            document.getElementById('drag-result').style.display = 'none';
            document.getElementById('maximized-result').style.display = 'none';
        };
    </script>
</body>
</html>
