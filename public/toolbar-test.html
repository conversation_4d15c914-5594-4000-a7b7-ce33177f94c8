<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Toolbar Position Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: #FF6B35;
            color: white;
        }

        .btn-primary:hover {
            background: #e55a2b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status {
            margin-left: auto;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status.idle {
            background: #e9ecef;
            color: #6c757d;
        }

        .status.running {
            background: #fff3cd;
            color: #856404;
        }

        .status.complete {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .results {
            padding: 30px;
        }

        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #FF6B35;
        }

        .summary-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
            color: #FF6B35;
        }

        .summary-card p {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
        }

        .test-result {
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .test-result:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: 600;
            color: #495057;
        }

        .test-details {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .test-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .test-status.pass {
            background: #d4edda;
            color: #155724;
        }

        .test-status.fail {
            background: #f8d7da;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FF6B35, #F7931E);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-entry.info {
            color: #63b3ed;
        }

        .log-entry.success {
            color: #68d391;
        }

        .log-entry.error {
            color: #fc8181;
        }

        .log-entry.warn {
            color: #fbd38d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🛠️ Toolbar Position Test Suite</h1>
            <p>Comprehensive testing for Mecap's enhanced toolbar positioning system</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="runTests">Run All Tests</button>
            <button class="btn btn-secondary" id="runBasicTests">Basic Tests Only</button>
            <button class="btn btn-secondary" id="clearResults">Clear Results</button>
            <div class="status idle" id="status">Ready</div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="results" id="results" style="display: none;">
            <div class="summary" id="summary"></div>
            <div class="test-results" id="testResults"></div>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script type="module">
        import { runToolbarPositionTests } from '../src/utils/ToolbarPositionTest.js';

        class TestRunner {
            constructor() {
                this.isRunning = false;
                this.setupEventListeners();
            }

            setupEventListeners() {
                document.getElementById('runTests').addEventListener('click', () => this.runAllTests());
                document.getElementById('runBasicTests').addEventListener('click', () => this.runBasicTests());
                document.getElementById('clearResults').addEventListener('click', () => this.clearResults());
            }

            async runAllTests() {
                if (this.isRunning) return;

                this.isRunning = true;
                this.updateStatus('running', 'Running Tests...');
                this.showResults();
                this.clearLog();

                try {
                    this.log('info', 'Starting comprehensive toolbar position tests...');
                    
                    const report = await runToolbarPositionTests();
                    
                    this.displayResults(report);
                    this.updateStatus('complete', 'Tests Complete');
                    this.log('success', `Tests completed: ${report.summary.passed}/${report.summary.total} passed`);
                } catch (error) {
                    this.updateStatus('error', 'Test Failed');
                    this.log('error', `Test execution failed: ${error.message}`);
                } finally {
                    this.isRunning = false;
                }
            }

            async runBasicTests() {
                // Simplified version for quick testing
                this.log('info', 'Running basic tests only...');
                // Implementation would run subset of tests
            }

            updateStatus(type, text) {
                const status = document.getElementById('status');
                status.className = `status ${type}`;
                status.textContent = text;
            }

            showResults() {
                document.getElementById('results').style.display = 'block';
            }

            clearResults() {
                document.getElementById('results').style.display = 'none';
                document.getElementById('summary').innerHTML = '';
                document.getElementById('testResults').innerHTML = '';
                this.clearLog();
                this.updateStatus('idle', 'Ready');
            }

            displayResults(report) {
                this.displaySummary(report.summary);
                this.displayTestResults(report.results);
                this.updateProgress(100);
            }

            displaySummary(summary) {
                const summaryEl = document.getElementById('summary');
                summaryEl.innerHTML = `
                    <div class="summary-card">
                        <h3>${summary.total}</h3>
                        <p>Total Tests</p>
                    </div>
                    <div class="summary-card">
                        <h3>${summary.passed}</h3>
                        <p>Passed</p>
                    </div>
                    <div class="summary-card">
                        <h3>${summary.failed}</h3>
                        <p>Failed</p>
                    </div>
                    <div class="summary-card">
                        <h3>${summary.successRate}</h3>
                        <p>Success Rate</p>
                    </div>
                `;
            }

            displayTestResults(results) {
                const resultsEl = document.getElementById('testResults');
                resultsEl.innerHTML = results.map(result => `
                    <div class="test-result">
                        <div>
                            <div class="test-name">${result.name}</div>
                            <div class="test-details">${result.details}</div>
                        </div>
                        <div class="test-status ${result.passed ? 'pass' : 'fail'}">
                            ${result.passed ? 'PASS' : 'FAIL'}
                        </div>
                    </div>
                `).join('');
            }

            updateProgress(percent) {
                document.getElementById('progressFill').style.width = `${percent}%`;
            }

            log(type, message) {
                const logEl = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                const entry = document.createElement('div');
                entry.className = `log-entry ${type}`;
                entry.textContent = `[${timestamp}] ${message}`;
                logEl.appendChild(entry);
                logEl.scrollTop = logEl.scrollHeight;
            }

            clearLog() {
                document.getElementById('log').innerHTML = '';
            }
        }

        // Initialize test runner
        new TestRunner();
    </script>
</body>
</html>
