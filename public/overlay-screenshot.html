<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Screenshot Overlay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent;
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .selection-area {
            position: absolute;
            border: 2px solid #007AFF;
            background: rgba(0, 122, 255, 0.1);
            display: none;
            pointer-events: none;
        }

        .selection-handles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #007AFF;
            border: 1px solid white;
            border-radius: 50%;
            display: none;
        }

        .handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
        .handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
        .handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
        .handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
        .handle-n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
        .handle-s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
        .handle-w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
        .handle-e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

        .toolbar {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 8px;
            display: none;
            gap: 8px;
            align-items: center;
        }

        .toolbar-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .toolbar-btn:hover {
            background: #0056CC;
        }

        .toolbar-btn.cancel {
            background: #FF3B30;
        }

        .toolbar-btn.cancel:hover {
            background: #D70015;
        }

        .coordinates-display {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
            display: none;
        }

        .instructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
        }

        .instructions h2 {
            margin-bottom: 10px;
            color: #007AFF;
        }

        .instructions p {
            margin-bottom: 8px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <div class="instructions" id="instructions">
            <h2>📸 Screenshot Mode</h2>
            <p>Click and drag to select an area</p>
            <p>Press <strong>ESC</strong> to cancel</p>
            <p>Press <strong>Enter</strong> to capture full screen</p>
        </div>

        <div class="selection-area" id="selectionArea">
            <div class="selection-handles handle-nw"></div>
            <div class="selection-handles handle-ne"></div>
            <div class="selection-handles handle-sw"></div>
            <div class="selection-handles handle-se"></div>
            <div class="selection-handles handle-n"></div>
            <div class="selection-handles handle-s"></div>
            <div class="selection-handles handle-w"></div>
            <div class="selection-handles handle-e"></div>
        </div>

        <div class="toolbar" id="toolbar">
            <button class="toolbar-btn" onclick="captureSelection()">Capture</button>
            <button class="toolbar-btn" onclick="copyToClipboard()">Copy</button>
            <button class="toolbar-btn cancel" onclick="cancelSelection()">Cancel</button>
        </div>

        <div class="coordinates-display" id="coordinates">
            Position: (0, 0) | Size: 0×0
        </div>
    </div>

    <script>
        let isSelecting = false;
        let startX = 0, startY = 0;
        let currentX = 0, currentY = 0;
        let selectionArea = document.getElementById('selectionArea');
        let toolbar = document.getElementById('toolbar');
        let coordinates = document.getElementById('coordinates');
        let instructions = document.getElementById('instructions');

        // 鼠标事件处理
        document.addEventListener('mousedown', startSelection);
        document.addEventListener('mousemove', updateSelection);
        document.addEventListener('mouseup', endSelection);

        // 键盘事件处理
        document.addEventListener('keydown', handleKeyboard);

        function startSelection(event) {
            if (event.target.closest('.toolbar') || event.target.closest('.selection-handles')) {
                return;
            }

            isSelecting = true;
            startX = event.clientX;
            startY = event.clientY;
            currentX = startX;
            currentY = startY;

            instructions.style.display = 'none';
            coordinates.style.display = 'block';
            
            updateSelectionArea();
        }

        function updateSelection(event) {
            if (!isSelecting) return;

            currentX = event.clientX;
            currentY = event.clientY;

            updateSelectionArea();
            updateCoordinates();
        }

        function endSelection(event) {
            if (!isSelecting) return;

            isSelecting = false;
            
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            if (width > 10 && height > 10) {
                showToolbar();
            } else {
                cancelSelection();
            }
        }

        function updateSelectionArea() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            selectionArea.style.left = left + 'px';
            selectionArea.style.top = top + 'px';
            selectionArea.style.width = width + 'px';
            selectionArea.style.height = height + 'px';
            selectionArea.style.display = 'block';
        }

        function updateCoordinates() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            coordinates.textContent = `Position: (${left}, ${top}) | Size: ${width}×${height}`;
        }

        function showToolbar() {
            const rect = selectionArea.getBoundingClientRect();
            toolbar.style.left = (rect.left + rect.width / 2 - 75) + 'px';
            toolbar.style.top = (rect.bottom + 10) + 'px';
            toolbar.style.display = 'flex';
        }

        function captureSelection() {
            const rect = selectionArea.getBoundingClientRect();
            const area = {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height
            };

            // 调用Tauri命令捕获区域
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('capture_region_new', { area })
                    .then(result => {
                        console.log('Screenshot captured:', result);
                        closeOverlay();
                    })
                    .catch(error => {
                        console.error('Failed to capture screenshot:', error);
                    });
            }
        }

        function copyToClipboard() {
            // TODO: 实现复制到剪贴板功能
            console.log('Copy to clipboard not yet implemented');
        }

        function cancelSelection() {
            selectionArea.style.display = 'none';
            toolbar.style.display = 'none';
            coordinates.style.display = 'none';
            instructions.style.display = 'block';
        }

        function handleKeyboard(event) {
            switch (event.key) {
                case 'Escape':
                    closeOverlay();
                    break;
                case 'Enter':
                    captureFullScreen();
                    break;
            }
        }

        function captureFullScreen() {
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('capture_fullscreen')
                    .then(result => {
                        console.log('Fullscreen captured:', result);
                        closeOverlay();
                    })
                    .catch(error => {
                        console.error('Failed to capture fullscreen:', error);
                    });
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 初始化
        console.log('Screenshot overlay loaded');
    </script>
</body>
</html>
