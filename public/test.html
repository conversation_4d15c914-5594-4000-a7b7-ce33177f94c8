<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Window</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            box-sizing: border-box;
        }
        .test-content {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h2>🧪 Test Window</h2>
        <p>This is a test window for property validation.</p>
        <p><strong>Transparent:</strong> ✓</p>
        <p><strong>Always on Top:</strong> ✓</p>
        <p><strong>No Decorations:</strong> ✓</p>
    </div>
</body>
</html>
