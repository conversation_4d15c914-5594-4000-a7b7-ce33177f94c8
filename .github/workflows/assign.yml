name: 'Auto Assign'

on:
  pull_request_target:
    types: [opened]

jobs:
  auto_assign:
    runs-on: ubuntu-latest
    steps:
      - name: Auto-assign PR to author
        if: ${{ github.event.pull_request.user.type != 'Bot' }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        # The gh cli currently does not consider the pr author an assignable user if they aren't part of the org
        run: gh api -X POST /repos/tauri-apps/tauri-docs/issues/${{ github.event.pull_request.number }}/assignees -f "assignees[]=${{ github.event.pull_request.user.login }}"
        #run: gh pr edit ${{ github.event.pull_request.number }} --add-assignee ${{ github.event.pull_request.user.login }} -R ${{ github.repository }}

      - name: Auto-assign Renovate PRs
        if: ${{ github.event.pull_request.user.type == 'Bot' && github.event.pull_request.user.login == 'renovate[bot]' }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        # The gh cli currently does not consider the pr author an assignable user if they aren't part of the org
        run: gh api -X POST /repos/tauri-apps/tauri-docs/issues/${{ github.event.pull_request.number }}/assignees -f "assignees[]=vasfvitor"
