name: 'Pull Request Labeler'

on:
  pull_request_target:

jobs:
  add_labels:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    env:
      NETLIFY_BUILD_KEY: ${{ secrets.NETLIFY_BUILD_KEY }}
    steps:
      - id: label-pr
        uses: actions/labeler@v4
        with:
          repo-token: '${{ secrets.GITHUB_TOKEN }}'
      # Trigger prod netlify re-build to refresh i18n dashboard
      - name: Rebuild i18n Dashboard
        if: contains(steps.label-pr.outputs.all-labels, 'i18n')
        run: curl -X POST -d {} https://api.netlify.com/build_hooks/$NETLIFY_BUILD_KEY
