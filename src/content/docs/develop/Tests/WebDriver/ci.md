---
title: Continuous Integration
description: WebDriver Testing
sidebar:
  order: 21
i18nReady: true
---

Utilizing Linux and some programs to create a fake display, it is possible to run [WebDriver] tests with
[`tauri-driver`] on your CI. The following example uses the [WebdriverIO] example we [previously built together] and
GitHub Actions.

This means the following assumptions:

1. The Tauri application is in the repository root and the binary builds when running `cargo build --release`.
2. The [WebDriverIO] test runner is in the `webdriver/webdriverio` directory and runs when `yarn test` is used in that
   directory.

The following is a commented GitHub Actions workflow file at `.github/workflows/webdriver.yml`

```yaml
# run this action when the repository is pushed to
on: [push]

# the name of our workflow
name: WebDriver

jobs:
  # a single job named test
  test:
    # the display name of the test job
    name: WebDriverIO Test Runner

    # we want to run on the latest linux environment
    runs-on: ubuntu-22.04

    # the steps our job runs **in order**
    steps:
      # checkout the code on the workflow runner
      - uses: actions/checkout@v4

      # install system dependencies that <PERSON><PERSON> needs to compile on Linux.
      # note the extra dependencies for `tauri-driver` to run which are: `webkit2gtk-driver` and `xvfb`
      - name: Tauri dependencies
        run: |
          sudo apt update && sudo apt install -y \
            libwebkit2gtk-4.1-dev \
            build-essential \
            curl \
            wget \
            file \
            libxdo-dev \
            libssl-dev \
            libayatana-appindicator3-dev \
            librsvg2-dev \
            webkit2gtk-driver \
            xvfb

      - name: Setup rust-toolchain stable
        id: rust-toolchain
        uses: dtolnay/rust-toolchain@stable

      # we run our rust tests before the webdriver tests to avoid testing a broken application
      - name: Cargo test
        run: cargo test

      # build a release build of our application to be used during our WebdriverIO tests
      - name: Cargo build
        run: cargo build --release

      # install the latest stable node version at the time of writing
      - name: Node 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'

      # install our Node.js dependencies with Yarn
      - name: Yarn install
        run: yarn install --frozen-lockfile
        working-directory: webdriver/webdriverio

      # install the latest version of `tauri-driver`.
      # note: the tauri-driver version is independent of any other Tauri versions
      - name: Install tauri-driver
        run: cargo install tauri-driver --locked

      # run the WebdriverIO test suite.
      # we run it through `xvfb-run` (the dependency we installed earlier) to have a fake
      # display server which allows our application to run headless without any changes to the code
      - name: WebdriverIO
        run: xvfb-run yarn test
        working-directory: webdriver/webdriverio
```

[previously built together]: /develop/tests/webdriver/example/webdriverio/
[webdriver]: https://www.w3.org/TR/webdriver/
[`tauri-driver`]: https://crates.io/crates/tauri-driver
[webdriverio]: https://webdriver.io/
