---
title: Develop
description: Core concepts for developing with <PERSON><PERSON>.
sidebar:
  label: Overview
i18nReady: true
---

import CommandTabs from '@components/CommandTabs.astro';

Now that you have [everything set up](/start/), you are ready to run your application using Tauri.

If you are using an UI framework or JavaScript bundler you likely have access to a development server
that will speed up your development process, so if you haven't configured your app's dev URL and script
that starts it, you can do so via the [devUrl](/reference/config/#devurl) and
[beforeDevCommand](/reference/config/#beforedevcommand) config values:

```json title=tauri.conf.json
{
  "build": {
    "devUrl": "http://localhost:3000",
    "beforeDevCommand": "npm run dev"
  }
}
```

:::note

Every framework has its own development tooling. It is outside of the scope of this document to cover them all or stay up to date.

Please refer to your framework's documentation to learn more and determine the correct values to be configured.

:::

Otherwise if you are not using a UI framework or module bundler you can point <PERSON><PERSON> to your frontend source code
and the Tauri CLI will start a development server for you:

```json title=tauri.conf.json
{
  "build": {
    "frontendDist": "./src"
  }
}
```

Note that in this example the `src` folder must include a `index.html` file along any other assets loaded by your frontend.

:::caution[Plain/Vanilla Dev Server Security]

The built-in Tauri development server does not support mutual authentication
or encryption. You should never use it for development on untrusted networks.
See the [development server security considerations](/security/lifecycle/#development-server)
for a more detailed explanation.

:::

### Developing Your Desktop Application

To develop your application for desktop, run the `tauri dev` command.

<CommandTabs
  npm="npm run tauri dev"
  yarn="yarn tauri dev"
  pnpm="pnpm tauri dev"
  deno="deno task tauri dev"
  bun="bun tauri dev"
  cargo="cargo tauri dev"
/>

The first time you run this command, the Rust package manager may need **several minutes** to download and build all the required packages.
Since they are cached, subsequent builds are much faster, as only your code needs rebuilding.

Once Rust has finished building, the webview opens, displaying your web app.
You can make changes to your web app, and if your tooling supports it, the webview should update automatically, just like a browser.

#### Opening the Web Inspector

You can open the Web Inspector to debug your application by performing a right-click on the webview and clicking "Inspect" or
using the `Ctrl + Shift + I` shortcut on Windows and Linux or `Cmd + Option + I` shortcut on macOS.

### Developing your Mobile Application

Developing for mobile is similar to how desktop development works, but you must run `tauri android dev` or `tauri ios dev` instead:

<CommandTabs
  npm="npm run tauri [android|ios] dev"
  yarn="yarn tauri [android|ios] dev"
  pnpm="pnpm tauri [android|ios] dev"
  deno="deno task tauri [android|ios] dev"
  bun="bun tauri [android|ios] dev"
  cargo="cargo tauri [android|ios] dev"
/>

The first time you run this command, the Rust package manager may need **several minutes** to download and build all the required packages.
Since they are cached, subsequent builds are much faster, as only your code needs rebuilding.

#### Development Server

The development server on mobile works similarly to the desktop one, but if you are trying to run on a physical iOS device,
you must configure it to listen to a particular address provided by the Tauri CLI, defined in the `TAURI_DEV_HOST` environment variable.
This address is either a public network address (which is the default behavior) or the actual iOS device TUN address - which is more secure, but currently
needs Xcode to connect to the device.

To use the iOS device's address you must open Xcode before running the dev command and ensure your device
is connected via network in the Window > Devices and Simulators menu.
Then you must run `tauri ios dev --force-ip-prompt` to select the iOS device address (a IPv6 address ending with **::2**).

To make your development server listen on the correct host to be accessible by the iOS device you must tweak its configuration
to use the `TAURI_DEV_HOST` value if it has been provided. Here is an example configuration for Vite:

```js
import { defineConfig } from 'vite';

const host = process.env.TAURI_DEV_HOST;

// https://vitejs.dev/config/
export default defineConfig({
  clearScreen: false,
  server: {
    host: host || false,
    port: 1420,
    strictPort: true,
    hmr: host
      ? {
          protocol: 'ws',
          host,
          port: 1421,
        }
      : undefined,
  },
});
```

Check your framework's setup guide for more information.

:::note
Projects created with [create-tauri-app](https://github.com/tauri-apps/create-tauri-app) configures
your development server for mobile dev out of the box.
:::

#### Device Selection

By default the mobile dev command tries to run your application in a connected device,
and fallbacks to prompting you to select a simulator to use.
To define the run target upfront you can provide the device or simulator name as argument:

<CommandTabs
  npm="npm run tauri ios dev 'iPhone 15'"
  yarn="yarn tauri ios dev 'iPhone 15'"
  pnpm="pnpm tauri ios dev 'iPhone 15'"
  deno="deno task tauri ios dev 'iPhone 15'"
  bun="bun tauri ios dev 'iPhone 15'"
  cargo="cargo tauri ios dev 'iPhone 15'"
/>

#### Using Xcode or Android Studio

Alternatively you can choose to use Xcode or Android Studio to develop your application.
This can help you troubleshoot some development issues by using the IDE instead of the command line tools.
To open the mobile IDE instead of running on a connected device or simulator, use the `--open` flag:

<CommandTabs
  npm="npm run tauri [android|ios] dev --open"
  yarn="yarn tauri [android|ios] dev --open"
  pnpm="pnpm tauri [android|ios] dev --open"
  deno="deno task tauri [android|ios] dev --open"
  bun="bun tauri [android|ios] dev --open"
  cargo="cargo tauri [android|ios] dev --open"
/>

:::note
If you intend on running the application on a physical iOS device you must also provide the `--host` argument
and your development server must use the `process.env.TAURI_DEV_HOST` value as host.
See your framework's setup guide for more information.

<CommandTabs
  npm="npm run tauri [android|ios] dev --open --host"
  yarn="yarn tauri [android|ios] dev --open --host"
  pnpm="pnpm tauri [android|ios] dev --open --host"
  deno="deno task tauri [android|ios] dev --open --host"
  bun="bun tauri [android|ios] dev --open --host"
  cargo="cargo tauri [android|ios] dev --open --host"
/>
:::

:::caution
To use Xcode or Android Studio the Tauri CLI process **must** be running and **cannot** be killed.
It is recommended to use the `tauri [android|ios] dev --open` command and keep the process alive until you close the IDE.
:::

#### Opening the Web Inspector

- iOS

  Safari must be used to access the Web Inspector for your iOS application.

  Open the Safari on your Mac machine, choose **Safari > Settings** in the menu bar, click **Advanced**, then select **Show features for web developers**.

  If you are running on a physical device you must enable **Web Inspector** in **Settings > Safari > Advanced**.

  After following all steps you should see a **Develop** menu in Safari, where you will find the connected devices and applications to inspect.
  Select on your device or simulator and click on **localhost** to open the Safari Developer Tools window.

- Android

  The inspector is enabled by default for Android emulators, but you must enable it for physical devices.
  Connect your Android device to the computer, open the **Settings** app in the Android device, select **About**, scroll to Build Number and tap that 7 times.
  This will enable Developer Mode for your Android device and the **Developer Options** settings.

  To enable application debugging on your device you must enter the **Developer Options** settings, toggle on the developer options switch
  and enable **USB Debugging**.

  :::note
  Each Android distribution has its own way to enable the Developer Mode, please check your manufacturer's documentation for more information.
  :::

  The Web Inspector for Android is powered by Google Chrome's DevTools and can be accessed by navigating to `chrome://inspect` in the Chrome browser.
  Your device or emulator should appear in the remote devices list if your Android application is running, and you can open the developer tools
  by clicking **inspect** on your device.

#### Troubleshooting

1. Error running build script on Xcode

Tauri hooks into the iOS Xcode project by creating a build phase that executes the Tauri CLI to compile the Rust source
as a library that is loaded at runtime. The build phase is executed on the Xcode process context, so it might not be able
to use shell modifications such as PATH additions, so be careful when using tools such as Node.js version managers which may not be compatible.

2. Network permission prompt on first iOS app execution

On the first time you execute `tauri ios dev` you might see iOS prompting you for permission to find and connect
to devices on your local network. This permission is required because to access your development server from an iOS device,
we must expose it in the local network. To run your app in your device you must click Allow and restart your application.

### Reacting to Source Code Changes

Similarly to how your webview reflects changes in real time,
Tauri watches your Rust files for changes so when you modify any of them your application is automatically rebuilt and restarted.

You can disable this behavior by using the `--no-watch` flag on the `tauri dev` command.

To restrict the files that are watched for changes you can create a `.taurignore` file in the src-tauri folder.
This file works just like a regular Git ignore file, so you can ignore any folder or file:

```filename=.taurignore
build/
src/generated/*.rs
deny.toml
```

### Using the Browser DevTools

Tauri's APIs only work in your app window, so once you start using them you won't be able to open your frontend in your system's browser anymore.

If you prefer using your browser's developer tooling, you must configure [tauri-invoke-http](https://github.com/tauri-apps/tauri-invoke-http)
to bridge Tauri API calls through a HTTP server.

### Source Control

In your project repository, you **SHOULD** commit the `src-tauri/Cargo.lock` along with the `src-tauri/Cargo.toml` to git
because Cargo uses the lockfile to provide deterministic builds. As a result, it is recommended that all applications check in
their `Cargo.lock`. You **SHOULD NOT** commit the `src-tauri/target` folder or any of its contents.
