---
title: Tauri 2.0
description: The cross-platform app building toolkit
i18nReady: true
editUrl: false
lastUpdated: false
template: splash
tableOfContents: false
prev: false
next: false
hero:
  tagline: Create small, fast, secure, cross-platform applications
  image:
    file: ../../assets/logo-outline.svg
  actions:
    - text: Get Started
      link: /start/
      icon: right-arrow
      variant: primary
    - text: Tauri 1.0 Documentation
      link: https://v1.tauri.app
      icon: external
      variant: minimal
---

import { Card, CardGrid, LinkCard } from '@astrojs/starlight/components';
import Cta from '@fragments/cta.mdx';

<div class="hero-bg">
  <div class="bg-logo"></div>
  <div class="bg-grad"></div>
  <div class="bg-grad-red"></div>
</div>

<div class="lp-cta-card">
  <Card title="Create a Project" icon="rocket">
    <Cta />
  </Card>
</div>

<CardGrid>
  <Card title="Frontend Independent" icon="rocket">
    Bring your existing web stack to <PERSON><PERSON> or start that new dream project.
    <PERSON><PERSON> supports any frontend framework so you don't need to change your
    stack.
  </Card>
  <Card title="Cross Platform" icon="rocket">
    Build your app for Linux, macOS, Windows, Android and iOS - all from a
    single codebase.
  </Card>
  <Card title="Inter-Process Communication" icon="rocket">
    Write your frontend in JavaScript, application logic in Rust, and integrate
    deep into the system with Swift and Kotlin.
  </Card>
  <Card title="Maximum Security" icon="rocket">
    Front-of-mind for the Tauri Team that drives our highest priorities and
    biggest innovations.
  </Card>
  <Card title="Minimal Size" icon="rocket">
    By using the OS's native web renderer, the size of a Tauri app can be little
    as 600KB.
  </Card>
  <Card title="Powered by Rust" icon="rocket">
    With performance and security at the center, Rust is the language for the
    next generation of apps.
  </Card>
</CardGrid>
