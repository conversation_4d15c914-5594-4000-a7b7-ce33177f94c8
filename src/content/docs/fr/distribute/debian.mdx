---
title: Debian
i18nReady: true
sidebar:
  order: 1
---

## Debian

La version de base du paquet Debian généré par le bundler de Tauri, contient tout ce dont vous avez besoin pour distribuer votre application sur les distributions Linux basées sur Debian. Il définit les icônes de votre application, génère un fichier Desktop et spécifie les dépendances `libwebkit2gtk-4.1` et `libgtk-3-0`, avec `libappindicator3-1` si votre application utilise la zone de notification du système.

### Custom Files

Tauri expose quelques configurations pour le paquet Debian au cas où vous auriez besoin de plus de contrôle.

Si votre application dépend de dépendances système supplémentaires, vous pouvez les spécifier dans `tauri.conf.json > bundle > linux > deb`.

Pour inclure des fichiers personnalisés dans le paquet Debian, vous pouvez fournir une liste de fichiers ou de dossiers dans `tauri.conf.json > bundle > linux > deb > files`. L'objet de configuration fait correspondre le chemin dans le paquet Debian au chemin du fichier sur votre système de fichiers, relatif au fichier `tauri.conf.json`. Voici un exemple de configuration :

```json
{
  // ...autre configurations
  "bundle": {
    "linux": {
      "deb": {
        "files": {
          "/usr/share/README.md": "../README.md", // copie le fichier README.md dans /usr/share/README.md
          "/usr/share/assets": "../assets/" // copies toutes le dossier assets dans /usr/share/assets
        }
      }
    }
  }
}
```
