---
title: Liste des Fonctionnalités & Cas d'Utilisations
i18nReady: true
---

import FeaturesList from '@components/list/Features.astro';
import CommunityList from '@components/list/Community.astro';

Tauri est conçu dans une optique d'extensibilité. Sur cette page, vous trouverez :

- **[Fonctionnalités](#fonctionnalités)** : Les caractéristiques et fonctionnalités intégrées nativement à Tauri
- **[Ressources de la communauté](#ressources-de-la-communauté)** : Plus de plugins et de cas d'utilisations développés par la communauté Tauri

{/* TODO: Search bar component that syncs/filters across this whole list */}

## Fonctionnalités

<FeaturesList />

## Ressources de la communauté

<CommunityList />

:::tip[Vous avez quelque chose d'intéressant à partager ?]

Si vous avez créé un plugin, un guide ou un cas d'utilisation, nous aimerions l'inclure ici ! [Soumettez une demande de tirage (pull request)](https://github.com/tauri-apps/tauri-docs/pulls) pour l'ajouter à cette page.

:::
