---
title: Learn
sidebar:
  order: 0
  label: Overview
i18nReady: true
---

import { Card, CardGrid, LinkCard } from '@astrojs/starlight/components';
import AwesomeTauri from '@components/AwesomeTauri.astro';
import BookItem from '@components/BookItem.astro';
import RoseRustBook from '@assets/learn/community/HTML_CSS_JavaScript_and_Rust_for_Beginners_A_Guide_to_Application_Development_with_Tauri.png';

The Learning category is intended to provide end-to-end learning experiences on a Tauri related topic.

These tutorials will guide you through a specific topic and help you apply knowledge from the guides and reference documentation.

For security related topics, you can learn about the permissions system. You will get practical insight into how to use it, extend it, and write your own permissions.

<CardGrid>
  <LinkCard
    title="Using Plugin Permissions"
    href="/learn/security/using-plugin-permissions/"
  />
  <LinkCard
    title="Capabilities for Different Windows and Platforms"
    href="/learn/security/capabilities-for-windows-and-platforms/"
  />
  <LinkCard
    title="Writing Plugin Permissions"
    href="/learn/security/writing-plugin-permissions/"
  />
</CardGrid>

To learn how to write your own splash screen or use a node.js sidecar, check out:

<CardGrid>
  <LinkCard title="Splashcreen" href="/learn/splashscreen/" />
  <LinkCard title="Node.js as a Sidecar" href="/learn/sidecar-nodejs/" />
</CardGrid>

## More Resources

This section contains learning resources created by the Community that are not hosted on this website.

<LinkCard
  title="Have something to share?"
  description="Open a pull request to show us your amazing resource."
  href="https://github.com/tauri-apps/awesome-tauri/pulls"
/>

### Books

<BookItem
  image={RoseRustBook}
  title="HTML, CSS, JavaScript, and Rust for Beginners: A Guide to Application Development with Tauri"
  alt="HTML, CSS, JavaScript, and Rust for Beginners Book Cover"
  author="James Alexander Rose"
  links={[
    {
      preText: 'Paperback on Amazon:',
      text: 'Buy Here',
      url: 'https://www.amazon.com/dp/B0DR6KZVVW',
    },
    {
      preText: 'Free PDF version:',
      text: 'Download (PDF 4MB)',
      url: '/assets/learn/community/HTML_CSS_JavaScript_and_Rust_for_Beginners_A_Guide_to_Application_Development_with_Tauri.pdf',
    },
  ]}
/>

### Guides & Tutorials

<AwesomeTauri section="guides-no-official-no-video" />

#### Video Guides

<AwesomeTauri section="guides-no-official-only-video" />
