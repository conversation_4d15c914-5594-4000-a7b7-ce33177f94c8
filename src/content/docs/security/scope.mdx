---
title: Command Scopes
sidebar:
  order: 3
i18nReady: true
---

A scope is a granular way to define (dis)allowed behavior of a Tauri command.

Scopes are categorized into `allow` or `deny` scopes, where `deny` always
supersedes the `allow` scope.

The scope type needs be of any [`serde`](https://docs.rs/serde/latest/serde/) serializable type.
These types are plugin-specific in general. For scoped commands implemented in a Tauri application
the scope type needs to be defined in the application and then enforced in the command implementation.

For instance, the [`Fs`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/fs) plugin allows you to use scopes to allow or deny certain directories and files
and the [`http`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/http) plugin uses scopes to filter URLs that are allowed to be reached.

The scope is passed to the command and handling or properly enforcing is implemented
by the command itself.

:::caution

Command developers need to ensure that there are no scope bypasses possible.
The scope validation implementation should be audited to ensure correctness.

:::

## Examples

These examples are taken from the [`Fs`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/fs) plugin permissions:

The scope type in this plugin for all commands is a string,
which contains a [`glob`](https://docs.rs/glob/latest/glob/) compatible path.

```toml title="plugins/fs/permissions/autogenerated/base-directories/applocaldata.toml"
[[permission]]
identifier = "scope-applocaldata-recursive"
description = '''
This scope recursive access to the complete `$APPLOCALDATA` folder,
including sub directories and files.
'''

[[permission.scope.allow]]
path = "$APPLOCALDATA/**"
```

```toml title="plugins/fs/permissions/deny-webview-data.toml"
[[permission]]
identifier = "deny-webview-data-linux"
description = '''
This denies read access to the
`$APPLOCALDATA` folder on linux as the webview data and
configuration values are stored here.
Allowing access can lead to sensitive information disclosure and
should be well considered.
'''
platforms = ["linux"]

[[scope.deny]]
path = "$APPLOCALDATA/**"

[[permission]]
identifier = "deny-webview-data-windows"
description = '''
This denies read access to the
`$APPLOCALDATA/EBWebView` folder on windows as the webview data and
configuration values are stored here.
Allowing access can lead to sensitive information disclosure and
should be well considered.
'''
platforms = ["windows"]

[[scope.deny]]
path = "$APPLOCALDATA/EBWebView/**"
```

The above scopes can be used to allow access to the `APPLOCALDATA` folder, while
preventing access to the `EBWebView` subfolder on windows, which contains sensitive webview data.

These can merged into a set, which reduces duplicate configuration and makes it more
understandable for anyone looking into the application configuration.

First the deny scopes are merged into `deny-default`:

```toml title="plugins/fs/permissions/deny-default.toml"
[[set]]
identifier = "deny-default"
description = '''
This denies access to dangerous Tauri relevant files and
folders by default.
'''
permissions = ["deny-webview-data-linux", "deny-webview-data-windows"]
```

Afterwards deny and allow scopes are merged:

```toml
[[set]]
identifier = "scope-applocaldata-reasonable"
description = '''
This scope set allows access to the `APPLOCALDATA` folder and
subfolders except for linux,
while it denies access to dangerous Tauri relevant files and
folders by default on windows.
'''
permissions = ["scope-applocaldata-recursive", "deny-default"]
```

These scopes can be either used for all commands, by extending the global scope of the plugin,
or for only selected commands when they are used in combination with a enabled command inside a permission.

Reasonable read only file access to files in the `APPLOCALDATA` could look like this:

```toml
[[set]]
identifier = "read-files-applocaldata"
description = '''
This set allows file read access to the `APPLOCALDATA` folder and
subfolders except for linux,
while it denies access to dangerous Tauri relevant files and
folders by default on windows.'''
permissions = ["scope-applocaldata-reasonable", "allow-read-file"]
```

These examples only highlight the scope functionality itself. Each plugin or application developer
needs to consider reasonable combinations of scope depending on their use cases.
