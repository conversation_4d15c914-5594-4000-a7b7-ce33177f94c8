---
title: Core Concepts
sidebar:
  order: 0
  label: Overview
i18nReady: true
---

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

<PERSON><PERSON> has a variety of topics that are considered to be core concepts, things any developer should be aware of when developing their applications. Here's a variety of topics that you should get more intimately familiar with if you want to get the most out of the framework.

<CardGrid>
  <LinkCard
    title="Tauri Architecture"
    href="/concept/architecture/"
    description="Architecture and ecosystem."
  />
  <LinkCard
    title="Inter-Process Communication (IPC)"
    href="/concept/inter-process-communication/"
    description="The inner workings on the IPC."
  />
  <LinkCard
    title="Security"
    href="/security/"
    description="How <PERSON><PERSON> enforces security practices."
  />
  <LinkCard
    title="Process Model"
    href="/concept/process-model/"
    description="Which processes <PERSON><PERSON> manages and why."
  />
  <LinkCard
    title="App Size"
    href="/concept/size/"
    description="How to make your app as small as possible."
  />
</CardGrid>
