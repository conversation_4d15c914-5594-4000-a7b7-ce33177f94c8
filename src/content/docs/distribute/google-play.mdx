---
title: Google Play
sidebar:
  order: 1
i18nReady: true
---

import CommandTabs from '@components/CommandTabs.astro';

Google Play is the Android app distribution service maintained by Google.

This guide covers the requirements for publishing your Android app on Google Play.
:::note
<PERSON><PERSON> uses an Android Studio project under the hood, so any official practice for
building and publishing Android apps also apply to your app.
See the [official documentation] for more information.
:::

## Requirements

To distribute Android apps in the Play Store you must create a [Play Console] developer account.

Additionally, you must setup [code signing].

See the [release checklist] for more information.

## Changing App Icon

After running `tauri android init` to setup the Android Studio project, you can use the `tauri icon` command to update the app icons.

<CommandTabs
  npm="npm run tauri icon /path/to/app-icon.png"
  yarn="yarn tauri icon /path/to/app-icon.png"
  pnpm="pnpm tauri icon /path/to/app-icon.png"
  deno="deno task tauri icon /path/to/app-icon.png"
  bun="bun tauri icon /path/to/app-icon.png"
  cargo="cargo tauri icon /path/to/app-icon.png"
/>

## Setting up

Once you've created a Play Console developer account, you need to register your app on the Google [Play Console] website. It will guide you through all the required forms and setup tasks.

## Build

You can build an Android App Bundle (AAB) to upload to Google Play by running the following command:

<CommandTabs
  npm="npm run tauri android build -- --aab"
  yarn="yarn tauri android build --aab"
  pnpm="pnpm tauri android build --aab"
  deno="deno task tauri android build --aab"
  bun="bun tauri android build --aab"
  cargo="cargo tauri android build --aab"
/>

Tauri derives the version code from the value defined in [`tauri.conf.json > version`] (`versionCode = major*1000000 + minor*1000 + patch`).
You can set a custom version code in the [`tauri.conf.json > bundle > android > versionCode`] configuration
if you need a different version code scheme e.g. sequential codes:

```json title="tauri.conf.json" ins={4}
{
  "bundle": {
    "android": {
      "versionCode": 100
    }
  }
}
```

### Build APKs

The AAB format is the recommended bundle file to upload to Google Play, but it is also possible to generate APKs
that can be used for testing or distribution outside the store.
To compile APKs for your app you can use the `--apk` argument:

<CommandTabs
  npm="npm run tauri android build -- --apk"
  yarn="yarn tauri android build --apk"
  pnpm="pnpm tauri android build --apk"
  deno="deno task tauri android build --apk"
  bun="bun tauri android build --apk"
  cargo="cargo tauri android build --apk"
/>

### Architecture selection

By default Tauri builds your app for all supported architectures (aarch64, armv7, i686 and x86_64).
To only compile for a subset of targets, you can use the `--target` argument:

<CommandTabs
  npm="npm run tauri android build -- --aab --target aarch64 --target armv7"
  yarn="yarn tauri android build --aab --target aarch64 --target armv7"
  pnpm="pnpm tauri android build --aab --target aarch64 --target armv7"
  deno="deno task tauri android build --aab --target aarch64 --target armv7"
  bun="bun tauri android build --aab --target aarch64 --target armv7"
  cargo="cargo tauri android build --aab --target aarch64 --target armv7"
/>

### Separate bundles per architecture

By default the generated AAB and APK is universal, containing all supported targets.
To generate individual bundles per target, use the `--split-per-abi` argument.
:::note
This is only useful for testing or distribution outside Google Play, as it reduces the file size but is less convenient to upload. Google Play handles the supported architectures for you.
:::

<CommandTabs
  npm="npm run tauri android build -- --apk --split-per-abi"
  yarn="yarn tauri android build --apk --split-per-abi"
  pnpm="pnpm tauri android build --apk --split-per-abi"
  deno="deno task tauri android build --apk --split-per-abi"
  bun="bun tauri android build --apk --split-per-abi"
  cargo="cargo tauri android build --apk --split-per-abi"
/>

### Changing the minimum supported Android version

The minimum supported Android version for Tauri apps is Android 7.0 (codename Nougat, SDK 24).

There are some techniques to use newer Android APIs while still supporting older systems.
See the [Android documentation](https://developer.android.com/training/basics/supporting-devices/platforms#version-codes) for more information.

If your app must execute on a newer Android version, you can configure [`tauri.conf.json > bundle > android > minSdkVersion`]:

```json title="tauri.conf.json" ins={4}
{
  "bundle": {
    "android": {
      "minSdkVersion": 28
    }
  }
}
```

## Upload

After building your app and generating the Android App Bundle file,
which can be found in `gen/android/app/build/outputs/bundle/universalRelease/app-universal-release.aab`,
you can now create a new release and upload it in the Google Play Console.

The first upload must be made manually in the website so it can verify your app signature and bundle identifier.
Tauri currently does not offer a way to automate the process of creating Android releases,
which must leverage the [Google Play Developer API](https://developers.google.com/android-publisher/api-ref/rest),
but it is a work in progress.

[official documentation]: https://developer.android.com/distribute
[Play Console]: https://play.google.com/console/developers
[code signing]: /distribute/sign/android/
[release checklist]: https://play.google.com/console/about/guides/releasewithconfidence/
[`tauri.conf.json > version`]: /reference/config/#version
[Google Play Developer API]: https://developers.google.com/android-publisher/api-ref/rest
