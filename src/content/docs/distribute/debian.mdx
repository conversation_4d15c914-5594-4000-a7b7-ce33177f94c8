---
title: Debian
sidebar:
  order: 1
i18nReady: true
---

{/* Is this about building .deb files, creating PPAs, or adding something to the debian/ubuntu repos? */}

## Debian

The stock Debian package generated by the <PERSON>ri bundler has everything you need to ship your application to Debian-based Linux distributions, defining your application's icons, generating a Desktop file, and specifying the dependencies `libwebkit2gtk-4.1-0` and `libgtk-3-0`, along with `libappindicator3-1` if your app uses the system tray.

### Custom Files

Tauri exposes a few configurations for the Debian package in case you need more control.

If your app depends on additional system dependencies you can specify them in `tauri.conf.json > bundle > linux > deb`.

To include custom files in the Debian package, you can provide a list of files or folders in `tauri.conf.json > bundle > linux > deb > files`. The configuration object maps the path in the Debian package to the path to the file on your filesystem, relative to the `tauri.conf.json` file. Here's an example configuration:

```json
{
  "bundle": {
    "linux": {
      "deb": {
        "files": {
          "/usr/share/README.md": "../README.md", // copies the README.md file to /usr/share/README.md
          "/usr/share/assets": "../assets/" // copies the entire assets directory to /usr/share/assets
        }
      }
    }
  }
}
```
