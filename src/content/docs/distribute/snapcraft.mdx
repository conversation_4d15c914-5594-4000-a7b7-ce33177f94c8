---
title: Snapcraft
sidebar:
  order: 1
i18nReady: true
---

## Prerequisites

import { Tabs, TabItem, Card } from '@astrojs/starlight/components';

**1. Install `snap`**

{/* prettier-ignore */}
<Tabs syncKey="distro">
  <TabItem label="Debian">
    ```shell
    sudo apt install snapd
    ```
  </TabItem>
  <TabItem label="Arch">
    ```shell
    sudo pacman -S --needed git base-devel
    git clone https://aur.archlinux.org/snapd.git
    cd snapd
    makepkg -si
    sudo systemctl enable --now snapd.socket
    sudo systemctl start snapd.socket
    sudo systemctl enable --now snapd.apparmor.service
    ```
  </TabItem>
  <TabItem label="Fedora">
    ```shell
    sudo dnf install snapd
    # Enable classic snap support
    sudo ln -s /var/lib/snapd/snap /snap
    ```

    Reboot your system afterwards.

  </TabItem>
</Tabs>

**2. Install a base snap**

```shell
sudo snap install core22
```

**3. Install `snapcraft`**

```shell
sudo snap install snapcraft --classic
```

## Configuration

1. Create an UbuntuOne account.
2. Go to the [Snapcraft](https://snapcraft.io) website and register an App name.
3. Create a snapcraft.yaml file in your projects root.
4. Adjust the names in the snapcraft.yaml file.

```yaml
name: appname
base: core22
version: '0.1.0'
summary: Your summary # 79 char long summary
description: |
  Your description

grade: stable
confinement: strict

layout:
  /usr/lib/$SNAPCRAFT_ARCH_TRIPLET/webkit2gtk-4.1:
    bind: $SNAP/usr/lib/$SNAPCRAFT_ARCH_TRIPLET/webkit2gtk-4.1

apps:
  appname:
    command: usr/bin/appname
    desktop: usr/share/applications/appname.desktop
    extensions: [gnome]
    #plugs:
    #  - network
    # Add whatever plugs you need here, see https://snapcraft.io/docs/snapcraft-interfaces for more info.
    # The gnome extension already includes [ desktop, desktop-legacy, gsettings, opengl, wayland, x11, mount-observe, calendar-service ]
    #  - single-instance-plug # add this if you're using the single-instance plugin
    #slots:
    # Add the slots you need to expose to other snaps
    #  - single-instance-plug # add this if you're using the single-instance plugin

# Add these lines only if you're using the single-instance plugin
# Check https://v2.tauri.app/plugin/single-instance/ for details
#slots:
#  single-instance:
#    interface: dbus
#    bus: session
#    name: org.net_mydomain_MyApp.SingleInstance # Remember to change net_mydomain_MyApp to your app ID with "_" instead of "." and "-"
#
#plugs:
#  single-instance-plug:
#    interface: dbus
#    bus: session
#    name: org.net_mydomain_MyApp.SingleInstance # Remember to change net_mydomain_MyApp to your app ID with "_" instead of "." and "-"

package-repositories:
  - type: apt
    components: [main]
    suites: [noble]
    key-id: 78E1918602959B9C59103100F1831DDAFC42E99D
    url: http://ppa.launchpad.net/snappy-dev/snapcraft-daily/ubuntu

parts:
  build-app:
    plugin: dump
    build-snaps:
      - node/20/stable
      - rustup/latest/stable
    build-packages:
      - libwebkit2gtk-4.1-dev
      - build-essential
      - curl
      - wget
      - file
      - libxdo-dev
      - libssl-dev
      - libayatana-appindicator3-dev
      - librsvg2-dev
      - dpkg
    stage-packages:
      - libwebkit2gtk-4.1-0
      - libayatana-appindicator3-1
    source: .
    override-build: |
      set -eu
      npm install
      npm run tauri build -- --bundles deb
      dpkg -x src-tauri/target/release/bundle/deb/*.deb $SNAPCRAFT_PART_INSTALL/
      sed -i -e "s|Icon=appname|Icon=/usr/share/icons/hicolor/32x32/apps/appname.png|g" $SNAPCRAFT_PART_INSTALL/usr/share/applications/appname.desktop
```

### Explanation

- The `name` variable defines the name of your app and is required to be set to the name that you have registered earlier.
- The `base` variable defines which core you are using.
- The `version` variable defines the version, and should be updated with each change to the source repository.
- The `apps` section allows you to expose the desktop and binary files to allow the user to run your app.
- The `package-repositories` section allows you to add a package repository to help you satisfy your dependencies.
- `build-packages`/`build-snaps` defines the build dependencies for your snap.
- `stage-packages`/`stage-snaps` defines the runtime dependencies for your snap.
- The `override-pull` section runs a series of commands before the sources are pulled.
- `craftctl default` performs the default pull commands.
- The `organize` section moves your files to the proper directories so that the binary and desktop file can be exposed to the `apps` sections.

## Building

```sh
sudo snapcraft
```

## Testing

{/* TODO: This seems to be wrong */}

```shell
snap run your-app
```

## Releasing Manually

```shell
snapcraft login # Login with your UbuntuOne credentials
snapcraft upload --release=stable mysnap_latest_amd64.snap
```

## Building automatically

1. On your apps developer page click on the `builds` tab.
2. Click `login with github`.
3. Enter in your repository's details.
