---
title: CrabNebula Cloud
i18nReady: true
---

## Distributing with CrabNebula Cloud

[CrabNebula](https://crabnebula.dev) is an official Tauri partner providing services and tooling for Tauri applications.
The [CrabNebula Cloud](https://crabnebula.dev/cloud/) is a platform for application distribution that seamlessly integrates with the Tauri updater.

The Cloud offers a Content Delivery Network (CDN) that is capable of shipping your application installers and updates globally while being cost effective and exposing download metrics.

With the CrabNebula Cloud service it is simple to implement multiple release channels, download buttons for your application website and more.

Setting up your Tauri app to use the Cloud is easy: all you need to do is to sign in to the [Cloud website] using your GitHub account, create your organization and application and install its CLI to create a release and upload the Tauri bundles. Additionally, a [GitHub Action] is provided to simplify the process of using the CLI on GitHub workflows.

For more information, see the [CrabNebula Cloud documentation].

[GitHub Action]: https://github.com/crabnebula-dev/cloud-release/
[Cloud website]: https://web.crabnebula.cloud/
[CrabNebula Cloud documentation]: https://docs.crabnebula.dev/cloud/
