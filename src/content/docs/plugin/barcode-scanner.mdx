---
title: Barcode Scanner
description: Allows your mobile application to use the camera to scan QR codes, EAN-13 and other types of barcodes.
plugin: barcode-scanner
i18nReady: true
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

Allows your mobile application to use the camera to scan QR codes, EAN-13 and other kinds of barcodes.

## Supported Platforms

<Compatibility plugin={frontmatter.plugin} />

## Setup

Install the barcode-scanner plugin to get started.

<Tabs>
  <TabItem label="Automatic">

    Use your project's package manager to add the dependency:

    {' '}

    <CommandTabs
      npm="npm run tauri add barcode-scanner"
      yarn="yarn run tauri add barcode-scanner"
      pnpm="pnpm tauri add barcode-scanner"
      deno="deno task tauri add barcode-scanner"
      bun="bun tauri add barcode-scanner"
      cargo="cargo tauri add barcode-scanner"
    />

  </TabItem>
  <TabItem label="Manual">
    <Steps>

      1.  Run the following command in the `src-tauri` folder to add the plugin to the project's dependencies in `Cargo.toml`:

          ```sh frame=none
          cargo add tauri-plugin-barcode-scanner --target 'cfg(any(target_os = "android", target_os = "ios"))'
          ```

      2.  Modify `lib.rs` to initialize the plugin:

          ```rust title="src-tauri/src/lib.rs" ins={5-6}
          #[cfg_attr(mobile, tauri::mobile_entry_point)]
          pub fn run() {
              tauri::Builder::default()
                  .setup(|app| {
                      #[cfg(mobile)]
                      app.handle().plugin(tauri_plugin_barcode_scanner::init());
                      Ok(())
                  })
                  .run(tauri::generate_context!())
                  .expect("error while running tauri application");
          }
          ```

      3.  Install the JavaScript Guest bindings using your preferred JavaScript package manager:

          <CommandTabs
              npm="npm install @tauri-apps/plugin-barcode-scanner"
              yarn="yarn add @tauri-apps/plugin-barcode-scanner"
              pnpm="pnpm add @tauri-apps/plugin-barcode-scanner"
              deno="deno add npm:@tauri-apps/plugin-barcode-scanner"
              bun="bun add @tauri-apps/plugin-barcode-scanner"
          />

    </Steps>

  </TabItem>
</Tabs>

## Configuration

On iOS the barcode scanner plugin requires the `NSCameraUsageDescription` information property list value, which should describe why your app needs to use the camera.

In the `src-tauri/Info.ios.plist` file, add the following snippet:

```xml title=src-tauri/Info.ios.plist
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>NSCameraUsageDescription</key>
		<string>Read QR codes</string>
	</dict>
</plist>
```

## Usage

The barcode scanner plugin is available in JavaScript.

```javascript
import { scan, Format } from '@tauri-apps/plugin-barcode-scanner';
// when using `"withGlobalTauri": true`, you may use
// const { scan, Format } = window.__TAURI__.barcodeScanner;

// `windowed: true` actually sets the webview to transparent
// instead of opening a separate view for the camera
// make sure your user interface is ready to show what is underneath with a transparent element
scan({ windowed: true, formats: [Format.QRCode] });
```

## Permissions

By default all potentially dangerous plugin commands and scopes are blocked and cannot be accessed. You must modify the permissions in your `capabilities` configuration to enable these.

See the [Capabilities Overview](/security/capabilities/) for more information and the [step by step guide](/learn/security/using-plugin-permissions/) to use plugin permissions.

```json title="src-tauri/capabilities/mobile.json"
{
  "$schema": "../gen/schemas/mobile-schema.json",
  "identifier": "mobile-capability",
  "windows": ["main"],
  "platforms": ["iOS", "android"],
  "permissions": ["barcode-scanner:allow-scan", "barcode-scanner:allow-cancel"]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
