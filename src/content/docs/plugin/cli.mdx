---
title: Command Line Interface (CLI)
description: Parse arguments from the command line interface.
plugin: cli
i18nReady: true
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

<PERSON><PERSON> enables your app to have a CLI through [clap](https://github.com/clap-rs/clap), a robust command line argument parser. With a simple CLI definition in your `tauri.conf.json` file, you can define your interface and read its argument matches map on JavaScript and/or Rust.

## Supported Platforms

<Compatibility plugin={frontmatter.plugin} />

- Windows
  - Due to an OS limitation, production apps are not able to write text back to the calling console by default. Please check out [tauri#8305](https://github.com/tauri-apps/tauri/issues/8305#issuecomment-1826871949) for a workaround.{/* TODO: Inline the instructions into this guide */}

## Setup

Install the CLI plugin to get started.

<Tabs>
	<TabItem label="Automatic">

    	Use your project's package manager to add the dependency:

    	<CommandTabs
            npm="npm run tauri add cli"
            yarn="yarn run tauri add cli"
            pnpm="pnpm tauri add cli"
            deno="deno task tauri add cli"
            bun="bun tauri add cli"
            cargo="cargo tauri add cli"
    	/>

  	</TabItem>
    <TabItem label="Manual">
    	<Steps>

        1. Run the following command in the `src-tauri` folder to add the plugin to the project's dependencies in `Cargo.toml`:

            ```sh frame=none
            cargo add tauri-plugin-cli --target 'cfg(any(target_os = "macos", windows, target_os = "linux"))'
            ```

    		2.	Modify `lib.rs` to initialize the plugin:

            ```rust title="src-tauri/src/lib.rs" ins={5-6}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                tauri::Builder::default()
                    .setup(|app| {
                        #[cfg(desktop)]
                        app.handle().plugin(tauri_plugin_cli::init());
                        Ok(())
                    })
                    .run(tauri::generate_context!())
                    .expect("error while running tauri application");
            }
            ```

    		3.	Install the JavaScript Guest bindings using your preferred JavaScript package manager:

            <CommandTabs
                npm="npm install @tauri-apps/plugin-cli"
                yarn="yarn add @tauri-apps/plugin-cli"
                pnpm="pnpm add @tauri-apps/plugin-cli"
                deno="deno add npm:@tauri-apps/plugin-cli"
                bun="bun add @tauri-apps/plugin-cli"
            />

    	</Steps>
    </TabItem>

</Tabs>

## Base Configuration

Under `tauri.conf.json`, you have the following structure to configure the interface:

```json title="src-tauri/tauri.conf.json"
{
  "plugins": {
    "cli": {
      "description": "Tauri CLI Plugin Example",
      "args": [
        {
          "short": "v",
          "name": "verbose",
          "description": "Verbosity level"
        }
      ],
      "subcommands": {
        "run": {
          "description": "Run the application",
          "args": [
            {
              "name": "debug",
              "description": "Run application in debug mode"
            },
            {
              "name": "release",
              "description": "Run application in release mode"
            }
          ]
        }
      }
    }
  }
}
```

:::note

All JSON configurations here are just samples, many other fields have been omitted for the sake of clarity.

:::

## Adding Arguments

The `args` array represents the list of arguments accepted by its command or subcommand.

{/* TODO: List available configuration */}

### Positional Arguments

A positional argument is identified by its position in the list of arguments. With the following configuration:

```json title="src-tauri/tauri.conf.json"
{
  "args": [
    {
      "name": "source",
      "index": 1,
      "takesValue": true
    },
    {
      "name": "destination",
      "index": 2,
      "takesValue": true
    }
  ]
}
```

Users can run your app as `./app tauri.txt dest.txt` and the arg matches map will define `source` as `"tauri.txt"` and `destination` as `"dest.txt"`.

### Named Arguments

A named argument is a (key, value) pair where the key identifies the value. With the following configuration:

```json title="tauri-src/tauri.conf.json"
{
  "args": [
    {
      "name": "type",
      "short": "t",
      "takesValue": true,
      "multiple": true,
      "possibleValues": ["foo", "bar"]
    }
  ]
}
```

Users can run your app as `./app --type foo bar`, `./app -t foo -t bar` or `./app --type=foo,bar` and the arg matches map will define `type` as `["foo", "bar"]`.

### Flag Arguments

A flag argument is a standalone key whose presence or absence provides information to your application. With the following configuration:

```json title="tauri-src/tauri.conf.json"
{
  "args": [
    {
      "name": "verbose",
      "short": "v"
    }
  ]
}
```

Users can run your app as `./app -v -v -v`, `./app --verbose --verbose --verbose` or `./app -vvv` and the arg matches map will define `verbose` as `true`, with `occurrences = 3`.

## Subcommands

Some CLI applications have additional interfaces as subcommands. For instance, the `git` CLI has `git branch`, `git commit` and `git push`. You can define additional nested interfaces with the `subcommands` array:

```json title="tauri-src/tauri.conf.json"
{
  "cli": {
    ...
    "subcommands": {
      "branch": {
        "args": []
      },
      "push": {
        "args": []
      }
    }
  }
}
```

Its configuration is the same as the root application configuration, with the `description`, `longDescription`, `args`, etc.

## Usage

The CLI plugin is available in both JavaScript and Rust.

<Tabs syncKey="lang">
  <TabItem label="JavaScript">

```javascript
import { getMatches } from '@tauri-apps/plugin-cli';
// when using `"withGlobalTauri": true`, you may use
// const { getMatches } = window.__TAURI__.cli;

const matches = await getMatches();
if (matches.subcommand?.name === 'run') {
  // `./your-app run $ARGS` was executed
  const args = matches.subcommand.matches.args;
  if (args.debug?.value === true) {
    // `./your-app run --debug` was executed
  }
  if (args.release?.value === true) {
    // `./your-app run --release` was executed
  }
}
```

  </TabItem>
  <TabItem label="Rust">

```rust title="src-tauri/src/lib.rs" {1, 6-18}
use tauri_plugin_cli::CliExt;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
   tauri::Builder::default()
       .plugin(tauri_plugin_cli::init())
       .setup(|app| {
           match app.cli().matches() {
               // `matches` here is a Struct with { args, subcommand }.
               // `args` is `HashMap<String, ArgData>` where `ArgData` is a struct with { value, occurrences }.
               // `subcommand` is `Option<Box<SubcommandMatches>>` where `SubcommandMatches` is a struct with { name, matches }.
               Ok(matches) => {
                   println!("{:?}", matches)
               }
               Err(_) => {}
           }
           Ok(())
       })
       .run(tauri::generate_context!())
       .expect("error while running tauri application");
}
```

  </TabItem>
</Tabs>

## Permissions

By default all potentially dangerous plugin commands and scopes are blocked and cannot be accessed. You must modify the permissions in your `capabilities` configuration to enable these.

See the [Capabilities Overview](/security/capabilities/) for more information and the [step by step guide](/learn/security/using-plugin-permissions/) to use plugin permissions.

```json title="src-tauri/capabilities/default.json" ins={6}
{
  "$schema": "../gen/schemas/desktop-schema.json",
  "identifier": "main-capability",
  "description": "Capability for the main window",
  "windows": ["main"],
  "permissions": ["cli:default"]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
