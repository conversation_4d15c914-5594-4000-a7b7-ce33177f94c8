---
title: El libro de Tauri
i18nReady: true
---

:::note[Actualización del progreso]

Estamos trabajando activamente en la autoría y redacción del Libro de Tauri. Hemos sufrido retrasos debido al inmenso crecimiento de Tauri, pero recientemente hemos vuelto a dar prioridad a este proyecto. Aunque todavía no tenemos detalles sobre los plazos de publicación, puedes estar atento a esta página para estar al día.

Nos gustaría pedir disculpas por este retraso más allá de la fecha de publicación originalmente comunicada. Si has donado a través de GitHub Sponsors u Open Collective y deseas solicitar un reembolso, puedes hacerlo a través de Open Collective: [Contacta con Tauri en Open Collective](https://opencollective.com/tauri/contact).

:::

### Visión general

El Libro de Tauri te guiará a través de la historia de Tauri y las decisiones de diseño que hemos tomado. También hablará en profundidad de por qué la privacidad, la seguridad y la sostenibilidad son discusiones importantes y fundamentales que puedes aplicar a cualquier proyecto de software moderno.

Los temas incluidos son:

- El método y el razonamiento que hay detrás del diseño de Tauri.
- Las opciones que tienes al construir con Tauri.
- Que no tienes que elegir entre enviar rápido y ser sostenible y responsable.
- Por qué elegimos el lenguaje Rust como capa de enlace y aplicación para Tauri.
- Por qué es importante una revisión binaria.

### Historia

En 2020, la fabricación de aplicaciones nativas se ha vuelto más fácil y accesible que nunca. Sin embargo, tanto los principiantes como los desarrolladores experimentados se enfrentan a decisiones difíciles en un panorama de seguridad y privacidad que cambia rápidamente. Esto es especialmente cierto en el entorno semiconfiable de los dispositivos de usuario.

Tauri elimina las conjeturas de la ecuación, ya que fue diseñado desde cero para adoptar nuevos paradigmas de desarrollo seguro y flexibilidad creativa que aprovechan las características del lenguaje Rust y te permite construir una aplicación utilizando cualquier framework de frontend que desees. Descubre cómo puedes diseñar, construir, auditar y desplegar aplicaciones nativas diminutas, rápidas, robustas y seguras para las principales plataformas de escritorio y móviles, todo ello a partir exactamente del mismo código base y en un tiempo récord, sin necesidad siquiera de conocer el lenguaje de programación Rust.

Los autores y cofundadores de Tauri, Daniel y Lucas, te llevan en un viaje de la teoría a la ejecución, durante el cual aprenderás por qué se construyó Tauri y cómo funciona bajo el capó. Junto con los puntos de vista de los invitados que se especializan en código abierto, DevOps, seguridad y arquitectura empresarial, este libro también presenta discusiones filosóficas con formato de discurso y puntos de vista de sostenibilidad de código abierto de los que sus aplicaciones de próxima generación se beneficiarán - y sus usuarios se beneficiarán.
