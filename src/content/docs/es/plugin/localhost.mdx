---
title: Localhost
description: Usa un servidor localhost en aplicaciones en producción.
plugin: localhost
i18nReady: true
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';

<PluginLinks plugin={frontmatter.plugin} showJsLinks={false} />

Expone los recursos de tu aplicación a través de un servidor localhost en lugar de usar el protocolo personalizado por defecto.

:::caution
Este plugin conlleva riesgos de seguridad considerables y solo debes usarlo si sabes lo que estás haciendo. Si tienes dudas, utiliza la implementación del protocolo personalizado por defecto.
:::

## Plataformas compatibles

<Compatibility plugin={frontmatter.plugin} />

## Configuración

Instala el plugin localhost para comenzar.

<Tabs>
    <TabItem label="Automático">

        Usa el gestor de paquetes de tu proyecto para añadir la dependencia:

        <CommandTabs npm="npm run tauri add localhost"
        yarn="yarn run tauri add localhost"
        pnpm="pnpm tauri add localhost"
        deno="deno task tauri add localhost"
        bun="bun tauri add localhost"
        cargo="cargo tauri add localhost" />

    </TabItem>
    <TabItem label="Manual">

        <Steps>

        1.  Ejecuta el siguiente comando en la carpeta `src-tauri` para añadir el plugin a las dependencias del proyecto en `Cargo.toml`:

            ```sh frame=none
            cargo add tauri-plugin-localhost
            ```

        2.  Modifica `lib.rs` para inicializar el plugin:

            ```rust title="src-tauri/src/lib.rs" ins={4}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                tauri::Builder::default()
                    .plugin(tauri_plugin_localhost::Builder::new().build())
                    .run(tauri::generate_context!())
                    .expect("error al ejecutar la aplicación tauri");
            }
            ```

        </Steps>

</TabItem>
</Tabs>

## Uso

El plugin localhost está disponible en Rust.

```rust title="src-tauri/src/lib.rs" {4} {7-14}
use tauri::{webview::WebviewWindowBuilder, WebviewUrl};

pub fn run() {
  let port: u16 = 9527;

  tauri::Builder::default()
      .plugin(tauri_plugin_localhost::Builder::new(port).build())
      .setup(move |app| {
          let url = format!("http://localhost:{}", port).parse().unwrap();
          WebviewWindowBuilder::new(app, "main".to_string(), WebviewUrl::External(url))
              .title("Ejemplo de Localhost")
              .build()?;
          Ok(())
      })
      .run(tauri::generate_context!())
      .expect("error al ejecutar la aplicación tauri");
}
```
