---
title: Nuxt
i18nReady: true
tableOfContents:
  minHeadingLevel: 2
  maxHeadingLevel: 5
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';

Nuxt es un meta framework para Vue. Aprende más sobre Nuxt en https://nuxt.com. Esta guía es válida para Nuxt 3.11.

## Checklist

- Usa SSG estableciendo `ssr: false`. Tauri no soporta soluciones basadas en el servidor.
- Usa `process.env.TAURI_DEV_HOST` como dirección IP del servidor de desarrollo cuando se configure para ejecutarse en dispositivos físicos con iOS.
- Usa `dist/` como `distDir` en `tauri.conf.json`.
- Compila usando `nuxi generate`.
- (Opcional): Deshabilita la telemetría estableciendo `telemetry: false` en `nuxt.config.ts`.

## Ejemplo de Configuración

<Steps>

1.  ##### Actualiza la configuración de Tauri:

          <Tabs>

    <TabItem label="npm">

        ```json
        // tauri.conf.json
        {
          "build": {
            "beforeDevCommand": "npm run dev",
            "beforeBuildCommand": "npm run generate",
            "devUrl": "http://localhost:3000",
            "frontendDist": "../dist"
          }
        }
        ```

              </TabItem>
              <TabItem label="yarn">

        ```json
        // tauri.conf.json
        {
          "build": {
            "beforeDevCommand": "yarn dev",
            "beforeBuildCommand": "yarn generate",
            "devUrl": "http://localhost:3000",
            "frontendDist": "../dist"
          }
        }
        ```

              </TabItem>
              <TabItem label="pnpm">

        ```json
        // tauri.conf.json
        {
          "build": {
            "beforeDevCommand": "pnpm dev",
            "beforeBuildCommand": "pnpm generate",
            "devUrl": "http://localhost:3000",
            "frontendDist": "../dist"
          }
        }
        ```

              </TabItem>
              <TabItem label="deno">

        ```json
        // tauri.conf.json
        {
          "build": {
            "beforeDevCommand": "deno task dev",
            "beforeBuildCommand": "deno task generate",
            "devUrl": "http://localhost:3000",
            "frontendDist": "../dist"
          }
        }
        ```

              </TabItem>

          </Tabs>

2.  ##### Actualiza la configuración de Nuxt:

    ```ts
    export default defineNuxtConfig({
      // (opcional) Habilita las herramientas de desarrollo de Nuxt
      devtools: { enabled: true },
      // Habilita SSG
      ssr: false,
      // Permite que el servidor de desarrollo sea detectable por otros dispositivos al ejecutarse en dispositivos físicos con iOS
      devServer: { host: process.env.TAURI_DEV_HOST || 'localhost' },
      vite: {
        // Mejor soporte para la salida de Tauri CLI
        clearScreen: false,
        // Habilita las variables de entorno
        // Las variables de entorno adicionales se pueden encontrar en
        // https://v2.tauri.app/reference/environment-variables/
        envPrefix: ['VITE_', 'TAURI_'],
        server: {
          // Tauri requiere un puerto consistente
          strictPort: true,
        },
      },
    });
    ```

</Steps>
