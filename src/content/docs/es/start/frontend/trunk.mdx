---
title: Trunk
i18nReady: true
tableOfContents:
  minHeadingLevel: 2
  maxHeadingLevel: 5
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';

Trunk es una herramienta de empaquetado de aplicaciones web WASM para Rust. Aprende más sobre Trunk en https://trunkrs.dev. Esta guía es válida para Trunk 0.17.5.

## Checklist

- Usa SSG, Tauri no soporta oficialmente soluciones basadas en el servidor.
- Usa `serve.ws_protocol = "ws"` para que el websocket de recarga rápida pueda conectarse correctamente para el desarrollo móvil.
- Habilita `withGlobalTauri` para asegurarte de que las APIs de Tauri están disponibles en la variable `window.__TAURI__` y pueden ser importadas usando `wasm-bindgen`.

## Ejemplo de Configuración

<Steps>

1. ##### Actualiza la configuración de Tauri

   ```json
   // tauri.conf.json
   {
     "build": {
       "beforeDevCommand": "trunk serve",
       "beforeBuildCommand": "trunk build",
       "devUrl": "http://localhost:8080",
       "frontendDist": "../dist"
     },
     "app": {
       "withGlobalTauri": true
     }
   }
   ```

1. ##### Actualiza la configuración de Trunk

   ```toml
   # Trunk.toml
   [watch]
   ignore = ["./src-tauri"]

   [serve]
   ws_protocol = "ws"
   ```

</Steps>
