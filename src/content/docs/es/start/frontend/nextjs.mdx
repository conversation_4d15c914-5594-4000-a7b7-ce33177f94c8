---
title: Next.js
i18nReady: true
tableOfContents:
  collapseLevel: 1
  minHeadingLevel: 2
  maxHeadingLevel: 5
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';

Next.js es un meta framework para React. Aprende más sobre Next.js en https://nextjs.org. Esta guía es válida para Next.js 14.2.3.

## Checklist

- Usa exports estáticos estableciendo `output: 'export'`. Tauri no soporta soluciones basadas en el servidor.
- Usa el directorio `out` como `frontendDist` en `tauri.conf.json`.

## Ejemplo de Configuración

<Steps>

1.  ##### Actualiza la configuración de Tauri

    <Tabs>

      <TabItem label="npm">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "npm run dev",
        "beforeBuildCommand": "npm run build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="yarn">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "yarn dev",
        "beforeBuildCommand": "yarn build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="pnpm">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "pnpm dev",
        "beforeBuildCommand": "pnpm build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="deno">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "deno task dev",
        "beforeBuildCommand": "deno task build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    </Tabs>

2.  ##### Actualiza la configuración de Next.js

    ```ts
    // next.conf.mjs
    const isProd = process.env.NODE_ENV === 'production';

    const internalHost = process.env.TAURI_DEV_HOST || 'localhost';

    /** @type {import('next').NextConfig} */
    const nextConfig = {
      // Asegurate de que Next.js usa SSG en lugar de SSR
      // https://nextjs.org/docs/pages/building-your-application/deploying/static-exports
      output: 'export',
      // Nota: Esta función es necesaria para usar el componente Image de Next.js en modo SSG.
      // Consulta https://nextjs.org/docs/messages/export-image-api para ver algunas soluciones alternativas.
      images: {
        unoptimized: true,
      },
      // Configura assetPrefix; de lo contrario, el servidor no resolverá correctamente tus recursos.
      assetPrefix: isProd ? undefined : `http://${internalHost}:3000`,
    };

    export default nextConfig;
    ```

3.  ##### Actualiza la configuración de package.json

        ```json
        "scripts": {
          "dev": "next dev",
          "build": "next build",
          "start": "next start",
          "lint": "next lint",
          "tauri": "tauri"
        }
        ```

</Steps>
