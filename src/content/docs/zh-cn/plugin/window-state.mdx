---
title: 窗口状态
description: 保持窗口大小和位置。
plugin: window-state
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

保存窗口位置和大小，并在应用程序重新打开时恢复它们。

## 支持的平台

<Compatibility plugin={frontmatter.plugin} />

## 设置

请安装窗口状态插件。

<Tabs>
    <TabItem label="自动">

    使用项目的包管理器来添加依赖。

    <CommandTabs
        npm="npm run tauri add window-state"
        yarn="yarn run tauri add window-state"
        pnpm="pnpm tauri add window-state"
        deno="deno task tauri add window-state"
        bun="bun tauri add window-state"
        cargo="cargo tauri add window-state"
    />

    </TabItem>
    <TabItem label="手动">
        <Steps>

        1. 在 `src-tauri` 文件夹中运行以下命令，将插件添加到 `Cargo.toml` 中的项目依赖项中。

            ```sh frame=none
            cargo add tauri-plugin-window-state --target 'cfg(any(target_os = "macos", windows, target_os = "linux"))'
            ```

        2. 修改 `lib.rs` 来初始化插件。

            ````rust title="src-tauri/src/lib.rs" ins={4}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                tauri::Builder::default()
                    .setup(|app| {
                        #[cfg(desktop)]
                        app.handle().plugin(tauri_plugin_window_state::Builder::default().build());
                        Ok(())
                    })
                    .run(tauri::generate_context!())
                    .expect("error while running tauri application");
            }
            ```

        3. 使用你喜欢的 JavaScript 包管理器安装 JavaScript Guest 绑定。

            <CommandTabs
                npm="npm install @tauri-apps/plugin-window-state"
                yarn="yarn add @tauri-apps/plugin-window-state"
                pnpm="pnpm add @tauri-apps/plugin-window-state"
                deno="deno add npm:@tauri-apps/plugin-window-state"
                bun="bun add @tauri-apps/plugin-window-state"
            />

        </Steps>

    </TabItem>

</Tabs>

## 用法

添加窗口状态插件后，应用程序关闭时所有窗口的状态都会被记住，并且在下次启动时会恢复到之前的状态。

你也可以在 JavaScript 和 Rust 中访问窗口状态插件。

### JavaScript

你可以使用 `saveWindowState` 手动保存窗口状态：

```javascript
import { saveWindowState, StateFlags } from '@tauri-apps/plugin-window-state';

saveWindowState(StateFlags.ALL);
```

同样，你可以手动从磁盘恢复窗口的状态：

```javascript
import {
  restoreStateCurrent,
  StateFlags,
} from '@tauri-apps/plugin-window-state';

restoreStateCurrent(StateFlags.ALL);
```

### Rust

你可以使用由 `AppHandleExt` 特征暴露的 `save_window_state()` 方法：

```rust
use tauri_plugin_window_state::{AppHandleExt, StateFlags};

// `tauri::AppHandle` 现在有了以下额外的方法
app.save_window_state(StateFlags::all()); // 将所有打开窗口的状态保存到磁盘
```

同样，你可以使用 `WindowExt` 特征暴露的 `restore_state()` 方法从磁盘手动恢复窗口的状态：

```rust
use tauri_plugin_window_state::{WindowExt, StateFlags};

// 所有的 `Window` 类型现在都有以下额外的方法
window.restore_state(StateFlags::all()); // 将从磁盘恢复窗口的状态
```

## 权限

默认情况下，所有具有潜在危险的插件命令和范围都会被阻止且无法访问。您必须修改 `capabilities` 文件夹中的配置来启用它们。

参见[能力概览](/zh-cn/security/capabilities/)以获取更多信息，以及插件的[分步导览](/zh-cn/learn/security/using-plugin-permissions/)来调整插件权限。

```json title="src-tauri/capabilities/default.json" ins={4-5}
{
  "permissions": [
    ...,
    "window-state:default",
  ]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
