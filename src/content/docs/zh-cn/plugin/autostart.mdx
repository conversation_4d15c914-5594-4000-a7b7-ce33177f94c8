---
title: 自动启动
description: 在系统启动时自动启动应用程序。
plugin: autostart
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

在系统启动时自动启动应用程序。

## 支持的平台

<Compatibility plugin={frontmatter.plugin} />

## 设置

从安装 autostart 插件开始。

<Tabs>
  <TabItem label="自动">

使用项目的包管理器来添加依赖。

{' '}

<CommandTabs
  npm="npm run tauri add autostart"
  yarn="yarn run tauri add autostart"
  pnpm="pnpm tauri add autostart"
  deno="deno task tauri add autostart"
  bun="bun tauri add autostart"
  cargo="cargo tauri add autostart"
/>

  </TabItem>
  <TabItem label="手动">
    <Steps>
    1. 在 `src-tauri` 文件夹中运行以下命令，将插件添加到 `Cargo.toml` 中的项目依赖项中。

        ```sh frame=none
        cargo add tauri-plugin-autostart --target 'cfg(any(target_os = "macos", windows, target_os = "linux"))'
        ```

    2. 修改 `lib.rs` 来初始化插件。

        ```rust title="src-tauri/src/lib.rs" ins={5-6}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                tauri::Builder::default()
                    .setup(|app| {
                        #[cfg(desktop)]
                        app.handle().plugin(tauri_plugin_autostart::init(tauri_plugin_autostart::MacosLauncher::LaunchAgent, Some(vec!["--flag1", "--flag2"]) /* 传递给应用程序的任意数量的参数 */));
                        Ok(())
                    })
                    .run(tauri::generate_context!())
                    .expect("error while running tauri application");
            }
        ```

    3. 你可以使用你喜欢的 JavaScript 包管理器来安装 JavaScript Guest 绑定。

        <CommandTabs
            npm="npm install @tauri-apps/plugin-autostart"
            yarn="yarn add @tauri-apps/plugin-autostart"
            pnpm="pnpm add @tauri-apps/plugin-autostart"
            deno="deno add npm:@tauri-apps/plugin-autostart"
            bun="bun add @tauri-apps/plugin-autostart"
        />
    </Steps>

  </TabItem>
</Tabs>

## 用法

autostart 插件有 JavaScript 和 Rust 两种版本。

<Tabs>
  <TabItem label="JavaScript">

```javascript
import { enable, isEnabled, disable } from '@tauri-apps/plugin-autostart';

// 启用 autostart
await enable();
// 检查 enable 状态
console.log(`registered for autostart? ${await isEnabled()}`);
// 禁用 autostart
disable();
```

  </TabItem>
  <TabItem label="Rust">

```rust
use tauri_plugin_autostart::MacosLauncher;
use tauri_plugin_autostart::ManagerExt;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_autostart::init(
            MacosLauncher::LaunchAgent,
            Some(vec!["--flag1", "--flag2"]),
        ))
        .setup(|app| {
            // 获取自动启动管理器
            let autostart_manager = app.autolaunch();
            // 启用 autostart
            let _ = autostart_manager.enable();
            // 检查 enable 状态
            println!("registered for autostart? {}", autostart_manager.is_enabled().unwrap());
            // 禁用 autostart
            let _ = autostart_manager.disable();
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

  </TabItem>
</Tabs>

## 权限

默认情况下，所有具有潜在危险的插件命令和范围都会被阻止且无法访问。您必须修改 `capabilities` 文件夹中的配置来启用它们。

参见[能力概览](/zh-cn/security/capabilities/)以获取更多信息，以及插件的[分步导览](/zh-cn/learn/security/using-plugin-permissions/)来调整插件权限。

```json title="src-tauri/capabilities/default.json"
{
  "permissions": [
    ...,
    "autostart:allow-enable",
    "autostart:allow-disable",
    "autostart:allow-is-enabled"
  ]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
