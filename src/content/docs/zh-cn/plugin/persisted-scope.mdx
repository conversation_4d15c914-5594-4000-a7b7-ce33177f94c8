---
title: 持久化作用域（Persisted Scope）
description: 将运行时作用域的更改持久化到文件系统上。
plugin: persisted-scope
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';

<PluginLinks plugin={frontmatter.plugin} showJsLinks={false} />

保存文件系统和资源作用域，并在应用重新打开时恢复它们。

## 支持的平台

- Windows
- Linux
- macOS

## 设置

请安装 persistent-scope 插件。

:::note
目前需要手动安装设置 persistent-scope 插件。
:::

1. 在你的 `Cargo.toml` 文件中添加以下内容来安装插件。

```toml title="src-tauri/Cargo.toml"
[dependencies]
tauri-plugin-persisted-scope = "2.0.0"
# 或者使用 Git：
tauri-plugin-persisted-scope = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v2" }
```

2. 修改 `lib.rs` 来初始化插件。

```rust title="src-tauri/src/lib.rs" {3}
fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_persisted_scope::init())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

## 用法

安装后，插件将自动保存和恢复文件系统和资源范围。
