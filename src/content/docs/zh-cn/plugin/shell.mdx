---
title: Shell
description: 访问系统 shell 来生成子进程。
plugin: shell
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Steps, Tabs, TabItem } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

访问系统 shell。允许你生成子进程。

## 支持的平台

<Compatibility plugin={frontmatter.plugin} />

## 指定打开应用

如果你正在查找 `shell.open` 接口的文档，请查看新的[指定打开应用插件](../opener/)。

## 设置

请从安装 shell 插件开始。

<Tabs>
	<TabItem label="自动" >
		使用项目的包管理器来添加依赖。

    	{ ' ' }

    	<CommandTabs
    		npm="npm run tauri add shell"
    		yarn="yarn run tauri add shell"
    		pnpm="pnpm tauri add shell"
            deno="deno task tauri add shell"
    		bun="bun tauri add shell"
    		cargo="cargo tauri add shell"
    	/>
    </TabItem>
    <TabItem label = "手动">
    	<Steps>
    	1. 在 `src-tauri` 目录下，运行下面的命令。将此插件添加到项目的 `Cargo.toml` 文件的 `dependencies` 字段中。

    		```sh frame=none
            cargo add tauri-plugin-shell
            ```

    	2. 修改 `lib.rs` 来初始化插件。

    		```rust title="src-tauri/src/lib.rs" ins={4}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                  	tauri::Builder::default()
                      	.plugin(tauri_plugin_shell::init())
                      	.run(tauri::generate_context!())
                      	.expect("error while running tauri application");
              }
            ```

    	3. 使用你喜欢的 JavaScript 包管理器安装 JavaScript Guest 绑定。

    		<CommandTabs
    			npm = "npm install @tauri-apps/plugin-shell"
    			yarn = "yarn add @tauri-apps/plugin-shell"
    			pnpm = "pnpm add @tauri-apps/plugin-shell"
                deno = "deno add npm:@tauri-apps/plugin-shell"
    			bun="bun add @tauri-apps/plugin-shell"
    		/>
    	</Steps>
    </TabItem>

</Tabs>

## 用法

Shell 插件有 JavaScript 和 Rust 两种版本。

<Tabs syncKey="lang">
	<TabItem label="JavaScript" >

```javascript
import { Command } from '@tauri-apps/plugin-shell';
// 当设置 `"withGlobalTauri": true`, 你可以使用
// const { Command } = window.__TAURI__.shell;

let result = await Command.create('exec-sh', [
  '-c',
  "echo 'Hello World!'",
]).execute();
console.log(result);
```

    </TabItem>
    <TabItem label = "Rust" >

```rust
use tauri_plugin_shell::ShellExt;

let shell = app_handle.shell();
let output = tauri::async_runtime::block_on(async move {
	shell
		.command("echo")
		.args(["Hello from Rust!"])
		.output()
		.await
		.unwrap()
});
if output.status.success() {
	println!("Result: {:?}", String::from_utf8(output.stdout));
} else {
	println!("Exit with code: {}", output.status.code().unwrap());
}
```

    </TabItem>

</Tabs>

## 权限

默认情况下，所有具有潜在危险的插件命令和范围都会被阻止且无法访问。您必须修改 `capabilities` 文件夹中的配置来启用它们。

参见[能力概览](/zh-cn/security/capabilities/)以获取更多信息，以及插件的[分步导览](/zh-cn/learn/security/using-plugin-permissions/)来调整插件权限。

```json title="src-tauri/capabilities/default.json" ins={6-23}
{
  "$schema": "../gen/schemas/desktop-schema.json",
  "identifier": "main-capability",
  "description": "Capability for the main window",
  "windows": ["main"],
  "permissions": [
    {
      "identifier": "shell:allow-execute",
      "allow": [
        {
          "name": "exec-sh",
          "cmd": "sh",
          "args": [
            "-c",
            {
              "validator": "\\S+"
            }
          ],
          "sidecar": false
        }
      ]
    }
  ]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
