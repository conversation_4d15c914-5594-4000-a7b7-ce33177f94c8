---
title: 操作系统信息
description: 查看操作系统信息。
plugin: os
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';
import PluginPermissions from '@components/PluginPermissions.astro';

<PluginLinks plugin={frontmatter.plugin} />

使用操作系统信息插件读取操作系统信息。

## Supported Platforms

<Compatibility plugin={frontmatter.plugin} />

## 设置

安装操作系统信息插件开始。

<Tabs>
    <TabItem label="自动">

    使用项目的包管理器来添加依赖：

    <CommandTabs npm="npm run tauri add os"
        yarn="yarn run tauri add os"
        pnpm="pnpm tauri add os"
        deno="deno task tauri add os"
        bun="bun tauri add os"
        cargo="cargo tauri add os" />

    </TabItem>
    <TabItem label="手动">
        <Steps>
        1. 在 `src-tauri` 文件夹中运行以下命令，将插件添加到 `Cargo.toml` 中的项目依赖项中。

            ```sh frame=none
            cargo add tauri-plugin-os
            ```
        2. 修改 lib.rs 来初始化插件。

            ```rust title="src-tauri/src/lib.rs" ins={4}
            #[cfg_attr(mobile, tauri::mobile_entry_point)]
            pub fn run() {
                tauri::Builder::default()
                    .plugin(tauri_plugin_os::init())
                    .run(tauri::generate_context!())
                    .expect("error while running tauri application");
            }
            ```

        3. 如果你想在 JavaScript 中使用，还需要安装 npm 包。

            <CommandTabs
                npm="npm install @tauri-apps/plugin-os"
                yarn="yarn add @tauri-apps/plugin-os"
                pnpm="pnpm add @tauri-apps/plugin-os"
                deno="deno add npm:@tauri-apps/plugin-os"
                bun="bun add @tauri-apps/plugin-os"
            />

        </Steps>
    </TabItem>

</Tabs>

## 用法

通过这个插件，您可以查询当前操作系统的多个信息。请参阅 [JavaScript API](/zh-cn/reference/javascript/os/) 或 [Rust API](https://docs.rs/tauri-plugin-os/) 参考资料中的所有可用函数。

{/* TODO: Link to which language to use, frontend vs. backend guide when it's made */}

#### 示例：操作系统平台

`platform` 返回一个描述使用的特定操作系统的字符串。该值在编译时设置。可能的值有 `linux`、`macos`、`ios`、`freebsd`、`dragonfly`、`netbsd`、`openbsd`、`solaris`、`android`、`windows`。

<Tabs syncKey="lang">
<TabItem label="JavaScript">

```javascript
import { platform } from '@tauri-apps/plugin-os';
// 当设置 `"withGlobalTauri": true` 时，你可以用
// const { platform } = window.__TAURI__.os;

const currentPlatform = platform();
console.log(currentPlatform);
// 将 "windows" 输出到控制台
```

</TabItem>
<TabItem label="Rust">

```rust
let platform = tauri_plugin_os::platform();
println!("Platform: {}", platform);
// 将 "windows" 输出到终端
```

</TabItem>
</Tabs>

## 权限

默认情况下，所有具有潜在危险的插件命令和范围都会被阻止且无法访问。您必须修改 `capabilities` 文件夹中的配置来启用它们。

参见[能力概览](/zh-cn/security/capabilities/)以获取更多信息，以及插件的[分步导览](/zh-cn/learn/security/using-plugin-permissions/)来调整插件权限。

```json title="src-tauri/capabilities/default.json" ins={4}
{
  "permissions": [
    ...,
    "os:default"
  ]
}
```

<PluginPermissions plugin={frontmatter.plugin} />
