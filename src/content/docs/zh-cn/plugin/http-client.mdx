---
title: HTTP 客户端
description: 访问用 Rust 编写的 HTTP 客户端。
plugin: http
---

import PluginLinks from '@components/PluginLinks.astro';
import Compatibility from '@components/plugins/Compatibility.astro';

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';

<PluginLinks plugin={frontmatter.plugin} />

使用 HTTP 插件发起 HTTP 请求。

## 支持的平台

<Compatibility plugin={frontmatter.plugin} />

## 设置

请安装 http 插件。

<Tabs>
    <TabItem label="自动">

    使用项目的包管理器来添加依赖：

    <CommandTabs npm="npm run tauri add http"
      yarn="yarn run tauri add http"
      pnpm="pnpm tauri add http"
      bun="bun tauri add http"
      cargo="cargo tauri add http" />

    </TabItem>
    <TabItem label="手动">

    1. 运行 `cargo add tauri-plugin-http` 命令，将插件添加到项目的 `cargo.toml` 依赖中。

    2. 修改 lib.rs 来初始化插件。

    ```rust ins={6}
    // lib.rs
    #[cfg_attr(mobile, tauri::mobile_entry_point)]
    pub fn run() {
        tauri::Builder::default()
            // Initialize the plugin
            .plugin(tauri_plugin_http::init())
            .run(tauri::generate_context!())
            .expect("error while running tauri application");
    }
    ```

    3. 如果你想用 JavaScript 发送 http 请求，还需要安装 npm 包。

    <CommandTabs
        npm="npm install @tauri-apps/plugin-http"
        yarn="yarn add @tauri-apps/plugin-http"
        pnpm="pnpm add @tauri-apps/plugin-http"
        bun="bun add @tauri-apps/plugin-http"
    />

    </TabItem>

</Tabs>

## 用法

http 插件既有 JavaScript API 版本，也有 Rust [reqwest](https://docs.rs/reqwest/) 重新导出的版本。

### JavaScript

<Steps>

1. 配置允许访问的 URL

   ```json
   //src-tauri/capabilities/base.json
   {
     "permissions": [
       {
         "identifier": "http:default",
         "allow": [{ "url": "https://*.tauri.app" }],
         "deny": [{ "url": "https://private.tauri.app" }]
       }
     ]
   }
   ```

   更多信息，请参阅[权限概述](/zh-cn/security/permissions/)的文档。

2. 发送请求

   ```javascript
   import { fetch } from '@tauri-apps/plugin-http';

   // Send a GET request
   const response = await fetch('http://my.api.host/data.json', {
     method: 'GET',
   });
   console.log(response.status); // e.g. 200
   console.log(response.statusText); // e.g. "OK"
   ```

   :::note
   当前的 `fetch` 方法是一个 Rust 后端 API。 它试图与 [`fetch` Web API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) 尽可能接近和兼容。
   :::

</Steps>

### Rust

在 Rust 中，你可以利用插件重新导出的 `reqwest` 包。更多细节请参考 [reqwest 文档](https://docs.rs/reqwest/)。

```rust
use tauri_plugin_http::reqwest;

let res = reqwest::get("http://my.api.host/data.json").await;
println!("{:?}", res.status()); // e.g. 200
println!("{:?}", res.text().await); // e.g Ok("{ Content }")
```
