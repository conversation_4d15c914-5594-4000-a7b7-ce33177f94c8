---
title: Tauri 之书
i18nReady: true
---

:::note[进度更新]

我们正在积极地创作和撰写《Tauri 之书》。由于 Tauri 的巨大增长，我们遇到了延迟，但最近重新确定了这个项目的优先级。虽然我们还没有发布时间表的细节，但你可以关注这个页面的更新。

我们很抱歉这篇文章推迟了原定的出版日期。如果您通过 GitHub 赞助商或 Open Collective 进行了捐赠，并希望请求退款，您可以通过 Open Collective：[联系 Open Collective 上的 Tauri](https://opencollective.com/tauri/contact)。

:::

### 概述

Tauri 之书将引导您通过 Tauri 的历史和我们所做的设计决策。它还将深入讨论为什么隐私、安全和可持续性是重要的和基本的讨论，你可以应用到任何现代软件项目。

主题包括：

- Tauri 设计背后的方法和推理
- 使用 Tauri 构建时的选项
- 你不必在快速交付和可持续和负责任之间进行选择
- 为什么我们选择 Rust 语言作为 Tauri 的绑定和应用层
- 为什么二元审查很重要

### 历史

到 2020 年，原生应用程序的制造已经变得比以往任何时候都更容易和更容易获得。尽管如此，初学者和经验丰富的开发人员都面临着快速变化的安全和隐私环境中的艰难选择。在用户设备的半可信环境中尤其如此。

Tauri 消除了猜测，因为它从一开始就被设计成采用 Rust 语言的新范式和创造性灵活性，并允许您使用任何前端框架构建应用程序。了解如何为主要的桌面和移动平台设计、构建、审计和部署小型、快速、健壮和安全的原生应用程序，所有这些都可以在完全相同的代码库中在记录的时间内完成，甚至不需要了解 Rust 编程语言。

作者和 Tauri 的创始人 Daniel 和 Lucas 将带领您从理论到实践的旅程，期间您将了解为什么要构建 Tauri 以及它在内部是如何工作的。。本书结合了开源、DevOps、安全和企业架构方面的客座见解，还展示了话语式的哲学讨论和开源可持续性观点，你的下一代应用程序将从中受益——你的用户也将从中受益。
