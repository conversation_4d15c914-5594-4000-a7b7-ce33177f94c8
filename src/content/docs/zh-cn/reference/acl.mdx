---
title: 访问控制列表
---

每个 Tauri [命令](/zh-cn/develop/calling-rust/)都向 JavaScript 层公开一个 Rust 函数，该函数可以使用操作系统 API 执行任意操作，默认情况下，该层使用 Web 标准进行沙箱化。
如果攻击者可以执行访问 Tauri 命令的代码，这将给您的应用程序带来风险。

为了缓解这种情况，Tauri 使用访问控制列表（Access Control List；ACL）来限制插件命令的访问。默认情况下，所有插件命令都被阻止，无法访问。若要启用对应用程序所需命令的访问，必须定义需要访问的权限列表。

## 功能

功能将权限集合与相关的窗口列表进行分组，以便您可以对单窗口和多窗口应用程序配置访问控制。

您可以将功能定义为 `src-tauri/capabilities` 目录中的 JSON 或 TOML 文件。

下面的 JSON 定义了一个功能，该功能为核心插件和 `window.setTitle` API启用默认功能。

```json title="src-tauri/capabilities/default.json"
{
  "$schema": "../gen/schemas/desktop-schema.json",
  "identifier": "main-capability",
  "description": "Capability for the main window",
  "windows": ["main"],
  "permissions": [
    "core:path:default",
    "core:event:default",
    "core:window:default",
    "core:app:default",
    "core:resources:default",
    "core:menu:default",
    "core:tray:default",
    "core:window:allow-set-title"
  ]
}
```

### 目标平台

通过定义 platforms 数组，功能可以是特定于平台的。
默认情况下，该功能适用于所有目标，但您可以选择 linux、macOS、windows、iOS 和 android 目标的一部分。

例如，让我们为桌面定义一个功能。注意，它启用了只能在桌面端使用的插件权限：

```json title="src-tauri/capabilities/desktop.json"
{
  "$schema": "../gen/schemas/desktop-schema.json",
  "identifier": "desktop-capability",
  "windows": ["main"],
  "platforms": ["linux", "macOS", "windows"],
  "permissions": ["global-shortcut:allow-register"]
}
```

并定义移动能力。注意，它启用了只能在移动设备上使用的插件的权限：

```json title="src-tauri/capabilities/mobile.json"
{
  "$schema": "../gen/schemas/mobile-schema.json",
  "identifier": "mobile-capability",
  "windows": ["main"],
  "platforms": ["iOS", "android"],
  "permissions": [
    "nfc:allow-scan",
    "biometric:allow-authenticate",
    "barcode-scanner:allow-scan"
  ]
}
```

### JSON 模式

Tauri 生成一个 JSON 模式，包含应用程序可用的所有权限，这样你就可以在 IDE 中实现自动补全。
要使用模式，请将 `$schema` 属性设置为 `capabilities/schemas` 目录中的模式之一，这些模式是特定于平台的。
通常你会将其设置为 `../gen/schemas/desktop-schema.json` 或 `../gen/schemas/mobile-schema.json`，不过你也可以为特定的目标平台定义功能。

在上面的示例中，我们为桌面端和移动端定义了特定的功能，并引用了相关的 JSON 模式。让我们定义一个仅适用于 Linux 的功能：

```json title="src-tauri/capabilities/linux.json"
{
  "$schema": "../gen/schemas/linux-schema.json",
  "identifier": "linux-capability",
  "windows": ["main"],
  "platforms": ["iOS", "android"],
  "permissions": ["dbus::call"]
}
```

### 多窗口

`windows` 属性定义了应该引用权限列表的窗口列表。您可以使用通配模式来引用动态窗口标签。

下面的示例将该功能与 `main` 窗口和任何以 `editor-` 为前缀的标签窗口连接起来。

```json title="src-tauri/capabilities/mobile.json"
{
  "windows": ["main", "editor-*"],
  "permissions": [
    ...
  ]
}
```

### 作用域

作用域进一步限制命令。它们的格式是特定于插件的，但是 Tauri 提供了允许和拒绝某些命令操作的原语，通常限制命令输入。
例如，`fs` 插件允许你使用作用域来允许或拒绝某些目录和文件，`http` 插件使用作用域来过滤允许访问的 url。

让我们研究一些官方插件的例子：

- 允许任何 `fs` 命令访问应用程序数据目录：

```json title="src-tauri/capabilities/base.json"
{
  "permissions": [
    {
      "identifier": "fs:scope",
      "allow": [{ "path": "$APPDATA" }, { "path": "$APPDATA/**" }]
    }
  ]
}
```

:::note
`fs:scope` 是 fs 插件定义的空权限，只是为了让您定义全局范围。
:::

- 允许 `fs` `rename` 命令访问主目录：

```json title="src-tauri/capabilities/base.json"
{
  "permissions": [
    {
      "identifier": "fs:allow-rename",
      "allow": [{ "path": "$HOME/**" }]
    }
  ]
}
```

- 配置 `http` 插件的作用域：

```json title="src-tauri/capabilities/base.json"
{
  "permissions": [
    {
      "identifier": "http:default",
      "allow": [{ "url": "https://*.tauri.app" }],
      "deny": [{ "url": "https://private.tauri.app" }]
    }
  ]
}
```

### 远程访问

默认情况下，每个功能只适用于应用程序的嵌入式内容（无论是你的 `devUrl` 还是 `frontendDist` 前端）。
如果你想允许远程 URL 访问 Tauri 命令(这很危险，需要仔细配置)，你可以使用 `context` 属性。

在下面的例子中，我们将该功能与任何 `tauri.app` 子域名关联。

```json title="src-tauri/capabilities/remote.json"
{
  "remote": {
    "urls": ["https://*.tauri.app"]
  },
  "local": false,
  "permissions": [
    ...
  ]
}
```
