---
title: 前置要求
i18nReady: true
---

import { Tabs, TabItem, Card } from '@astrojs/starlight/components';

为了开始使用 Tauri 构建项目，你首先需要安装一些依赖项：

1. [系统依赖项](#系统依赖项)
2. [Rust](#rust)
3. [移动端配置](#移动端配置) （仅在针对移动设备进行开发时才需要）

## 系统依赖项

点击链接开始配置，适用于你使用的操作系统：

- [Linux](#linux) （特定发行版请参考下文）
- [macOS Catalina (10.15) 或更新](#macos)
- [Windows 7 或更新](#windows)

### Linux

Tauri 在 Linux 上进行开发需要各种系统依赖项。这些可能会有所不同，具体取决于你的发行版，但我们在下面提供了一些流行的发行版来帮助你进行设置。

<Tabs syncKey="distro">
  <TabItem label="Debian">

```sh
sudo apt update
sudo apt install libwebkit2gtk-4.1-dev \
  build-essential \
  curl \
  wget \
  file \
  libxdo-dev \
  libssl-dev \
  libayatana-appindicator3-dev \
  librsvg2-dev
```

  </TabItem>
  <TabItem label="Arch">

```sh
sudo pacman -Syu
sudo pacman -S --needed \
  webkit2gtk-4.1 \
  base-devel \
  curl \
  wget \
  file \
  openssl \
  appmenu-gtk-module \
  libappindicator-gtk3 \
  librsvg \
  xdotool
```

  </TabItem>
  <TabItem label="Fedora">

```sh
sudo dnf check-update
sudo dnf install webkit2gtk4.1-devel \
  openssl-devel \
  curl \
  wget \
  file \
  libappindicator-gtk3-devel \
  librsvg2-devel \
  libxdo-devel
sudo dnf group install "c-development"
```

  </TabItem>
  <TabItem label="Gentoo">

```sh
sudo emerge --ask \
  net-libs/webkit-gtk:4.1 \
  dev-libs/libappindicator \
  net-misc/curl \
  net-misc/wget \
  sys-apps/file
```

  </TabItem>
  <TabItem label="openSUSE">

```sh
sudo zypper up
sudo zypper in webkit2gtk3-devel \
  libopenssl-devel \
  curl \
  wget \
  file \
  libappindicator3-1 \
  librsvg-devel
sudo zypper in -t pattern devel_basis
```

  </TabItem>
  <TabItem label="Alpine">
```sh
sudo apk add \
  build-base \
  webkit2gtk \
  curl \
  wget \
  file \
  openssl \
  libayatana-appindicator-dev \
  librsvg
```
  </TabItem>
  <TabItem label="NixOS">

:::note
这也会为你安装 Rust 和 Node.js 以及 `cargo-tauri` CLI，因此你可以跳过下面的步骤。
:::

使用 `nix-shell`：

```nix
let
  pkgs = import <nixpkgs> { };
in
pkgs.mkShell {
  nativeBuildInputs = with pkgs; [
    pkg-config
    gobject-introspection
    cargo
    cargo-tauri
    nodejs
  ];

  buildInputs = with pkgs;[
    at-spi2-atk
    atkmm
    cairo
    gdk-pixbuf
    glib
    gtk3
    harfbuzz
    librsvg
    libsoup_3
    pango
    webkitgtk_4_1
    openssl
  ];
}
```

  </TabItem>
</Tabs>

如果你的发行版未包含在上面，那么你可能需要查阅 [Awesome Tauri on GitHub](https://github.com/tauri-apps/awesome-tauri#guides) 以获知是否已有指南被创建。

下一步：[下载并安装 Rust](#rust)

### macOS

Tauri 使用 [Xcode](https://developer.apple.com/cn/xcode/resources/) 以及各种 macOS 和 iOS 开发依赖项。

从以下位置之一下载并安装 Xcode：

- [Mac App Store](https://apps.apple.com/cn/app/xcode/id497799835?mt=12)
- [Apple Developer 网站](https://developer.apple.com/cn/xcode/resources/).

请务必在安装后启动 Xcode，以使它完成设置。

<details>
<summary>仅针对桌面目标进行开发？</summary>
如果你只打算开发桌面应用程序而不针对 iOS，那么你可以改为安装 Xcode 命令行工具：

```sh
xcode-select --install
```

</details>

下一步：[下载并安装 Rust](#rust)

### Windows

Tauri 使用 Microsoft C++ 生成工具进行开发以及 Microsoft Edge WebView2。这两者都是在 Windows 上进行开发所必需的。

按照以下步骤安装所需的依赖项。

#### Microsoft C++ 生成工具

1. 下载 [Microsoft C++ 生成工具](https://visualstudio.microsoft.com/zh-hans/visual-cpp-build-tools/) 安装程序并打开它以开始安装。
2. 在安装过程中，选中“使用 C++ 的桌面开发”选项。

![Visual Studio C++ 生成工具 安装程序 截图](./visual-studio-build-tools-installer.png)

下一步：[下载并安装 WebView2](#webview2).

#### WebView2

:::tip
WebView 2 已安装在 Windows 10（从版本 1803 开始）和更高版本的 Windows 上。如果你正在这些版本之一上进行开发，则可以跳过此步骤，并直接转到 [下载并安装 Rust](#rust)。
:::

Tauri 使用 Microsoft Edge WebView2 在 Windows 上呈现内容。

通过访问[下载 WebView2 运行时](https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/#download)安装 WebView2。下载并安装“常青独立安装程序（Evergreen Bootstrapper）”。

下一步：[下载并安装 Rust](#rust)

## Rust

Tauri 使用 [Rust](https://www.rust-lang.org/zh-CN/) 构建并需要它进行开发。使用以下方法之一安装 Rust。你可以在 https://www.rust-lang.org/zh-CN/tools/install 查看更多安装方法。

<Tabs syncKey="OS">
  <TabItem label="Linux and macOS" class="content">

使用 [`rustup`](https://github.com/rust-lang/rustup) 安装：

```sh
curl --proto '=https' --tlsv1.2 https://sh.rustup.rs -sSf | sh
```

:::tip[安全提示]
我们已经审核了这个 bash 脚本，它做了它所说的应该做的事情。尽管如此，在盲目地使用脚本之前，先看一看总是明智的。

以下是作为纯文本的脚本文件：[rustup.sh](https://sh.rustup.rs/)
:::

  </TabItem>
  <TabItem label="Windows">

前往 https://www.rust-lang.org/zh-CN/tools/install 下载 `rustup`。

或者，你可以在 PowerShell 中使用 `winget` 安装 rustup：

```powershell
winget install --id Rustlang.Rustup
```

:::caution[默认使用 MSVC 工具链]

为了完全支持 Tauri 和 [`trunk`](https://trunkrs.dev/) 等工具，请确保在安装程序对话框中的 `default host triple` 选择 MSVC Rust 工具链。根据你的系统，它应该是 `x86_64-pc-windows-msvc`、`i686-pc-windows-msvc` 或 `aarch64-pc-windows-msvc`。

如果你已安装 Rust，你可以通过运行以下命令来确保安装正确的工具链：

```powershell
rustup default stable-msvc
```

:::

  </TabItem>
</Tabs>

**请务必重新启动终端（在某些情况下重新启动系统）以使更改生效。**

下一步：如果你想要在 Android 或 iOS 上开发应用，前往[移动端配置](#移动端配置)。或者，如果你想使用 JavaScript 前端框架，前往[安装 Node](#nodejs)。否则，前往[创建新项目](/zh-cn/start/create-project/)。

## Node.js

:::note[JavaScript 生态系统]
仅当你打算使用 JavaScript 前端框架时
:::

1. 访问 [Node.js 网站](https://nodejs.org/zh-cn)，下载并安装长期支持版本（LTS）。

2. 运行以下命令以检查 Node 是否成功安装：

```sh
node -v
# v20.10.0
npm -v
# 10.2.3
```

重要的是，重新启动终端以确保它能够识别新安装的内容。在某些情况下，您可能需要重新启动计算机。

虽然 npm 是 Node.js 的默认包管理器，但你也可以使用其他包管理器，比如 pnpm 或 yarn。如果你想启用这些包管理器，可以在终端中运行 `corepack enable`。这一步是可选的，只有在您想使用 npm 以外的包管理器时才需要。

下一步：[移动端配置](#移动端配置)或者[创建新项目](/zh-cn/start/create-project/)。

## 移动端配置

如果你想将应用适配到 Android 或 iOS，则需要安装一些其他依赖项：

- [Android](#android)
- [iOS](#ios)

### Android

1. 从 Android Developers 网站下载并安装 [Android Studio](https://developer.android.com/studio?hl=zh-cn)。
2. 设置 `JAVA_HOME` 环境变量：

{/* TODO: Can this be done in the 4th step? */}

<Tabs syncKey="prereqs">
<TabItem label="Linux">

```sh
export JAVA_HOME=/opt/android-studio/jbr
```

</TabItem>
<TabItem label="macOS">

```sh
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
```

</TabItem>
<TabItem label="Windows">

```ps
[System.Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Android\Android Studio\jbr", "User")
```

</TabItem>
</Tabs>
3. 使用 Android Studio 中的 SDK Manager 安装以下内容：

- Android SDK Platform
- Android SDK Platform-Tools
- NDK (Side by side)
- Android SDK Build-Tools
- Android SDK Command-line Tools

在 SDK Manager 中选择“Show Package Details”可以安装旧版本软件包。非必要时不要安装旧版本，因为它们可能引入兼容性问题或安全风险。

4. 配置 `ANDROID_HOME` 和 `NDK_HOME` 环境变量：

<Tabs syncKey="prereqs">
<TabItem label="Linux">

```sh
export ANDROID_HOME="$HOME/Android/Sdk"
export NDK_HOME="$ANDROID_HOME/ndk/$(ls -1 $ANDROID_HOME/ndk)"
```

</TabItem>
<TabItem label="macOS">

```sh
export ANDROID_HOME="$HOME/Library/Android/sdk"
export NDK_HOME="$ANDROID_HOME/ndk/$(ls -1 $ANDROID_HOME/ndk)"
```

</TabItem>
<TabItem label="Windows">

```ps
[System.Environment]::SetEnvironmentVariable("ANDROID_HOME", "$env:LocalAppData\Android\Sdk", "User")
$VERSION = Get-ChildItem -Name "$env:LocalAppData\Android\Sdk\ndk"
[System.Environment]::SetEnvironmentVariable("NDK_HOME", "$env:LocalAppData\Android\Sdk\ndk\$VERSION", "User")
```

:::tip
PowerShell 在重新启动或注销之前不会应用新设置的环境变量，但是你可以通过运行以下命令来刷新当前会话窗口：

```ps
[System.Environment]::GetEnvironmentVariables("User").GetEnumerator() | % { Set-Item -Path "Env:\$($_.key)" -Value $_.value }
```

:::

</TabItem>

</Tabs>

5. 使用 `rustup` 添加 Android 编译目标：

```sh
rustup target add aarch64-linux-android armv7-linux-androideabi i686-linux-android x86_64-linux-android
```

下一步：[配置 iOS](#ios)或[创建新项目](/zh-cn/start/create-project/)。

### iOS

:::caution[仅 macOS]
iOS 开发需要 Xcode，并且仅在 macOS 上可用。确保你在 [macOS 系统依赖项部分](#macos)中安装了 Xcode 而不是 Xcode 命令行工具。
:::

1. 在终端中使用 `rustup` 将 iOS 添加为编译目标：

```sh
rustup target add aarch64-apple-ios x86_64-apple-ios aarch64-apple-ios-sim
```

2. 安装 [Homebrew](https://brew.sh/zh-cn/)：

```sh
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

3. 使用 Homebrew 安装 [Cocoapods](https://cocoapods.org)：

```sh
brew install cocoapods
```

下一步：[创建新项目](/zh-cn/start/create-project/)。

## 故障排除

如果你在安装过程中遇到任何问题，请务必查看[故障诊断指南](/zh-cn/develop/debug/)或联系 [Tauri Discord](https://discord.com/invite/tauri) 以寻求帮助。

<Card title="下一步" icon="rocket">

现在，你已经安装了所有前置要求，你可以准备好[创建你的第一个 Tauri 应用程序](/zh-cn/start/create-project/)！

</Card>
