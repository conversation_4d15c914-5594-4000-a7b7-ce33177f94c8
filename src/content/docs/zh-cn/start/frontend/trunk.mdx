---
title: Trunk
i18nReady: true
tableOfContents:
  minHeadingLevel: 2
  maxHeadingLevel: 5
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';

Trunk 是一个用于 Rust 的 WASM 网络应用程序打包工具。要了解更多关于 Trunk 的信息，请访问 https://trunkrs.dev 。本指南适用于 Trunk 0.17.5 版本。

## 检查清单

- 使用 SSG。Tauri 不支持基于服务端的解决方案。
- 使用 `ws_protocol = "ws"` 以便进行移动开发时热重载 websocket 可以正常连接。
- 启用 `withGlobalTauri` 以确保 Tauri API 在使用 `window.__TAURI__` 变量时可用，并且可以使用 `wasm-bindgen` 导入。

## 示例配置

<Steps>

1. 更新 Tauri 配置：

   ```json
   // tauri.conf.json
   {
     "build": {
       "beforeDevCommand": "trunk serve",
       "beforeBuildCommand": "trunk build",
       "devUrl": "http://localhost:8080",
       "frontendDist": "../dist"
     },
     "app": {
       "withGlobalTauri": true
     }
   }
   ```

2. 更新 Trunk 配置：

   ```toml
   # Trunk.toml
   [watch]
   ignore = ["./src-tauri"]

   [serve]
   ws_protocol = "ws"
   ```

</Steps>
