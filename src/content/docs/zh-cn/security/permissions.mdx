---
title: 权限
sidebar:
  order: 2
i18nReady: true
---

权限（Permissions）是对命令所具备的明确特权（explicit privileges）的具体描述。

```toml
[[permission]]
identifier = "my-identifier"
description = "此处填写带来的影响等等"
commands.allow = [
    "read_file"
]

[[scope.allow]]
my-scope = "$HOME/*"

[[scope.deny]]
my-scope = "$HOME/secret"
```

该功能允许在 Tauri 应用程序的前端访问特定命令，通过将作用域（Scopes）映射到命令并定义可启用的命令列表。
权限机制既可启用/禁用命令，也可定义作用域，或同时执行两种操作。

权限可以按新的标识符（Identifier）进行分组，形成`权限集（Permission set）`。这种机制允许将以下两类权限进行组合：

1. 范围相关权限（如数据访问范围）
2. 命令相关权限（如执行特定操作的权限）
   同时，还能将操作系统层面的特定权限归类为更易于使用的集合。

作为插件开发者，您可以为所有对外暴露的命令提供多个预定义且命名清晰的权限。

作为应用开发者，您可以扩展现有插件权限或为自定义命令定义新权限。
这些权限可被分组或组合成权限集（Permission Set），以便复用或简化后续的主配置文件配置。

## 权限标识符

权限标识符（Permissions identifier）用于确保权限可被复用，并具有唯一的名称。

:::tip

此处 **name** 指插件的 crate 名称（不含 `tauri-plugin-` 前缀），
用于命名空间隔离以降低命名冲突风险；当涉及应用程序自身权限引用时无需使用该前缀。

:::

- `<name>:default` 表明该权限是插件或应用程序的默认设置
- `<name>:<command-name>` 表示该权限仅适用于单个命令（或独立命令）。

插件标识符在编译时将自动添加 `tauri-plugin-` 前缀，无需手动指定。

标识符仅限于 ASCII 小写字母字符 `[a-z]`，且当前最大长度限制为 `116` 字符（由以下常量决定）：

```rust
const IDENTIFIER_SEPARATOR: u8 = b':';
const PLUGIN_PREFIX: &str = "tauri-plugin-";

// https://doc.rust-lang.org/cargo/reference/manifest.html#the-name-field
const MAX_LEN_PREFIX: usize = 64 - PLUGIN_PREFIX.len();
const MAX_LEN_BASE: usize = 64;
const MAX_LEN_IDENTIFIER: usize = MAX_LEN_PREFIX + 1 + MAX_LEN_BASE;
```

## 配置文件

Tauri **插件**目录结构的简化示例：

```sh
tauri-plugin
├── README.md
├── src
│  └── lib.rs
├── build.rs
├── Cargo.toml
├── permissions
│  └── <identifier>.json/toml
│  └── default.json/toml
```

默认权限以特殊方式处理，只要使用 Tauri CLI 向 Tauri 应用添加插件，该权限便会自动加入应用配置中。

对于**应用程序**开发者而言，其结构类似：

```sh
tauri-app
├── index.html
├── package.json
├── src
├── src-tauri
│   ├── Cargo.toml
│   ├── permissions
│      └── <identifier>.toml
|   ├── capabilities
│      └── <identifier>.json/.toml
│   ├── src
│   ├── tauri.conf.json
```

:::note

作为应用开发者，功能配置文件（Capability files）可使用 `json`/`json5` 或 `toml` 格式编写，但权限配置仅支持 `toml` 格式。

:::

## 示例

来自 File System 插件的权限示例

```toml title="plugins/fs/permissions/autogenerated/base-directories/home.toml"
[[permission]]
identifier = "scope-home"
description = """This scope permits access to all files and
list content of top level directories in the `$HOME`folder."""

[[scope.allow]]
path = "$HOME/*"
```

```toml title="plugins/fs/permissions/read-files.toml"
[[permission]]
identifier = "read-files"
description = """将允许所有读取文件的命令，无需预先指定可访问路径。"""
commands.allow = [
    "read_file",
    "read",
    "open",
    "read_text_file",
    "read_text_file_lines",
    "read_text_file_lines_next"
]
```

```toml title="plugins/fs/permissions/autogenerated/commands/mkdir.toml"
[[permission]]
identifier = "allow-mkdir"
description = "将允许使用 mkdir 命令"
commands.allow = [
    "mkdir"
]
```

以下示例演示如何在您的应用中扩展上述插件权限的实现：

```toml title="my-app/src-tauri/permissions/home-read-extends.toml"
[[set]]
identifier = "allow-home-read-extended"
description = """ 将允许在 `$HOME` 目录下使用非递归方式读取文件和创建文件夹
"""
permissions = [
    "fs:read-files",
    "fs:scope-home",
    "fs:allow-mkdir"
]
```
