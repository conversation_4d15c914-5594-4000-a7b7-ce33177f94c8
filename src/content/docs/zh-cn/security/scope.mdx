---
title: 命令作用域
sidebar:
  order: 3
i18nReady: true
---

作用域（scope）是定义 Tauri 命令允许（禁止）行为的细致的方法。

作用域分为 `允许（allow）` 作用域或 `禁止（deny）` 作用域，其中禁止作用域始终优先于允许作用域。

作用域类型必须是任意 [`serde`](https://docs.rs/serde/latest/serde/) 可序列化类型。这些类型通常特定于插件。对于在 Tauri 应用程序中实现的作用域命令，作用域类型需要在应用程序中定义，然后在命令实现中强制执行。

例如， [`Fs`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/fs) 插件可以让你使用作用域来允许或拒绝某些目录和文件，[`http`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/http) 插件使用作用域来筛选允许访问的 URL。

作用域传递给命令，并由命令本身实现处理或正确执行。

:::caution

命令开发人员需要确保不存在任何可能的作用域绕过。作用域验证的实施应接受审核，以确保其正确性。

:::

## 示例

这些示例取自 [`Fs`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/fs) 插件权限：

此插件中所有命令的作用域类型都是字符串，其中包含一个 [`glob`](https://docs.rs/glob/latest/glob/) 兼容路径。

```toml title="plugins/fs/permissions/autogenerated/base-directories/applocaldata.toml"
[[permission]]
identifier = "scope-applocaldata-recursive"
description = '''
这个作用域递归访问整个 `$APPLOCALDATA` 文件夹，
包括子文件夹和文件。
'''

[[permission.scope.allow]]
path = "$APPLOCALDATA/**"
```

```toml title="plugins/fs/permissions/deny-webview-data.toml"
[[permission]]
identifier = "deny-webview-data-linux"
description = '''
这将禁止对 Linux 上 `$APPLOCALDATA` 文件夹的访问，
因为那里存储了 webview 数据和配置信息。
允许对其访问可能会导致敏感信息泄漏，应谨慎考虑。
'''
platforms = ["linux"]

[[scope.deny]]
path = "$APPLOCALDATA/**"

[[permission]]
identifier = "deny-webview-data-windows"
description = '''
这将禁止对 windows 上 `$APPLOCALDATA/EBWebView`
文件夹的访问，因为那里存储了 webview 数据和配置信息。
允许对其访问可能会导致敏感信息泄漏，应谨慎考虑。
'''
platforms = ["windows"]

[[scope.deny]]
path = "$APPLOCALDATA/EBWebView/**"
```

上述作用域可用于允许访问 `APPLOCALDATA` 文件夹，同时阻止访问 Windows 上的包含敏感 webview 数据的 `EBWebView` 子文件夹。

这些可以合并成一个集合，从而减少重复配置，并使任何查看应用程序配置的人都更容易理解。

首先，将拒绝作用域合并到 `deny-default` 中：

```toml title="plugins/fs/permissions/deny-default.toml"
[[set]]
identifier = "deny-default"
description = '''
默认情况下这将禁止访问 Tauri 相关的危险文件和文件夹。
'''
permissions = ["deny-webview-data-linux", "deny-webview-data-windows"]
```

随后，将拒绝和允许作用域合并：

```toml
[[set]]
identifier = "scope-applocaldata-reasonable"
description = '''
此作用域集合允许非 Linux 系统下访问 `APPLOCALDATA` 文件夹及其子文件夹，
而在 Windows 上默认拒绝访问危险的 Tauri 相关文件和文件夹。
'''
permissions = ["scope-applocaldata-recursive", "deny-default"]
```

这些作用域可以通过扩展插件的全局作用域用于所有命令，或者当它们与权限内启用的命令结合使用时仅用于选定的命令。

对 `APPLOCALDATA` 中的文件进行合理的只读文件访问可能如下所示：

```toml
[[set]]
identifier = "read-files-applocaldata"
description = '''
此作用域集合允许非 Linux 系统下访问 `APPLOCALDATA` 文件夹及其子文件夹，
而在 Windows 上默认拒绝访问危险的 Tauri 相关文件和文件夹。
permissions = ["scope-applocaldata-reasonable", "allow-read-file"]
```

这些示例仅突出了作用域本身的功能。每个插件或应用程序开发者都需要根据自己的用例考虑合理的作用域组合。
