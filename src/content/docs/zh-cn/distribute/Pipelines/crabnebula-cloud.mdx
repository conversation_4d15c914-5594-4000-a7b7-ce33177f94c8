---
title: CrabNebula Cloud
---

## 使用 CrabNebula Cloud 进行分发

[CrabNebula](https://crabnebula.dev) 是 Tauri 官方合作伙伴，为 Tauri 应用提供服务和工具。
[CrabNebula Cloud](https://crabnebula.dev/cloud/)是一个与 Tauri 更新程序无缝集成的应用分发平台。

该服务提供了一个内容传递网络（CDN），能够以成本效益的方式全球运送您的应用程序安装程序和更新，并公开下载指标。

通过 CrabNebula Cloud 服务，实现多个发布渠道、应用网站的下载按钮等非常简单。

将您的 Tauri 应用程序设置为使用云非常简单：您只需使用 GitHub 帐户登录 [Cloud 网站]，创建组织和应用程序，并安装其 CLI 以创建发布并上传 Tauri 捆绑包。此外，还提供了一个 [GitHub Action] 来简化在 GitHub 工作流程中使用 CLI 的过程。

更多信息，请参阅 [CrabNebula Cloud 文档]。

[GitHub Action]: https://github.com/crabnebula-dev/cloud-release/
[Cloud 网站]: https://web.crabnebula.cloud/
[CrabNebula Cloud 文档]: https://docs.crabnebula.dev/cloud/
