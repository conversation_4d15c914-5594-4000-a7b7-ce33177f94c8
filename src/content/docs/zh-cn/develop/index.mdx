---
title: 开发
description: Tauri开发的核心概念
sidebar:
  label: 概述
i18nReady: true
---

import CommandTabs from '@components/CommandTabs.astro';

现在 [万事俱备](/start/)，你已经可以使用 Tauri 运行应用。

如果你正在使用一个 UI 框架或 JavaScript 打包工具，你可能有权访问开发服务器，
这会加速你的开发，所以如果你还没有配置应用的开发URL和启动脚本，你可以对 [devUrl](/reference/config/#devurl) 和
[beforeDevCommand](/reference/config/#beforedevcommand) 进行设置:

```json title=tauri.conf.json
{
  "build": {
    "devUrl": "http://localhost:3000",
    "beforeDevCommand": "npm run dev"
  }
}
```

:::note

每一个框架都有专属的开发工具。本文档无法涵盖所有内容或保持最新状态。

若要了解更多并明确正确的配置项，请参考所选框架的文档。

:::

反之，如果没有使用框架和打包器，你可以将你的前端源码交给 Tauri，
Tauri CLI 会帮你启动开发服务器:

```json title=tauri.conf.json
{
  "build": {
    "frontendDist": "./src"
  }
}
```

注意在这个例子中，`src` 文件夹必须包含 `index.html` 文件和其他全部加载前端界面所需的资源。

:::caution[简单/原版开发服务器的安全问题]

内置的Tauri开发服务器不支持双向验证和加密。不要在不信任的网络环境中使用它来开发。
查看 [开发服务器安全注意事项](/security/lifecycle/#development-server)
来获取更加详细的解释。

:::

### 开发你的桌面应用

如果要为桌面环境开发应用，请执行 `tauri dev` 命令。

<CommandTabs
  npm="npm run tauri dev"
  yarn="yarn tauri dev"
  pnpm="pnpm tauri dev"
  deno="deno task tauri dev"
  bun="bun tauri dev"
  cargo="cargo tauri dev"
/>

第一次执行这个命令时 Rust 包管理器可能会需要**一些时间**来下载并构建所有需要的包。
他们会被缓存，以后的构建会快得多，因为只有你的代码需要重新构建。

Rust 构建完成后，webview 会打开，显示你的 web 应用。你可以对 web 应用进行更改，如果你的工具支持，webview 应该会自动更新，就像浏览器一样。

#### 打开 web 检查器

你可以打开 web 检查器来调试你的应用，打开的方法是在webview上右键并点击 "Inspect" 或使用快捷键 `Ctrl + Shift + I`（Windows，Linux），`Cmd + Option + I`（macOS）。

### 开发你的移动应用

移动应用开发和桌面端相差不大，不同是需要执行`tauri android dev` 或 `tauri ios dev` :

<CommandTabs
  npm="npm run tauri [android|ios] dev"
  yarn="yarn tauri [android|ios] dev"
  pnpm="pnpm tauri [android|ios] dev"
  deno="deno task tauri [android|ios] dev"
  bun="bun tauri [android|ios] dev"
  cargo="cargo tauri [android|ios] dev"
/>

第一次执行这个命令时 Rust 包管理器可能会需要**一些时间**来下载并构建所有需要的包。
他们会被缓存，以后的构建会快得多，因为只有你的代码需要重新构建。

#### 开发服务器

移动开发服务器运行与桌面端类似，但如果要在iOS物理机运行，
则必须将其配置为监听 Tauri CLI 提供的特定地址，该地址在 `TAURI_DEV_HOST` 环境变量中定义。
该地址可以是一个公网地址 (默认) 也可以是实际的 iOS 设备 TUN 地址——后者更安全，但目前需要 Xcode 连接到设备。

要使用 iOS 设备的地址，你必须在运行 dev 命令之前打开 Xcode，并确保你的设备已在“窗口 > 设备和模拟器”菜单中通过网络连接。
然后，你必须运行 `tauri ios dev --force-ip-prompt` 来选择 iOS 设备地址（以 ::2 结尾的 IPv6 地址）。

为了使开发服务器监听正确的主机，以便 iOS 设备可以访问，你必须调整其配置，以使用 `TAURI_DEV_HOST` 值（如果已提供）。以下是 Vite 的示例配置：

```js
import { defineConfig } from 'vite';

const host = process.env.TAURI_DEV_HOST;

// https://vitejs.dev/config/
export default defineConfig({
  clearScreen: false,
  server: {
    host: host || false,
    port: 1420,
    strictPort: true,
    hmr: host
      ? {
          protocol: 'ws',
          host,
          port: 1421,
        }
      : undefined,
  },
});
```

查看框架的设置指南以获取更多信息。

:::note
使用 [create-tauri-app](https://github.com/tauri-apps/create-tauri-app) 创建的项目可为你的移动开发配置开箱即用的开发服务器。
:::

#### 设备选择

默认情况下，mobile dev 命令会尝试在已连接的设备中运行你的应用，并回退到提示你选择要使用的模拟器。
要预先定义运行目标，你可以提供设备或模拟器名称作为参数:

<CommandTabs
  npm="npm run tauri ios dev 'iPhone 15'"
  yarn="yarn tauri ios dev 'iPhone 15'"
  pnpm="pnpm tauri ios dev 'iPhone 15'"
  deno="deno task tauri ios dev 'iPhone 15'"
  bun="bun tauri ios dev 'iPhone 15'"
  cargo="cargo tauri ios dev 'iPhone 15'"
/>

#### 使用 Xcode 或 Android Studio

或者，你可以选择使用 Xcode 或 Android Studio 来开发你的应用。这可以帮助你通过使用 IDE 而不是命令行工具来排查一些开发问题。
要打开移动 IDE 而不是在连接的设备或模拟器上运行， 请使用 `--open` 标志：

<CommandTabs
  npm="npm run tauri [android|ios] dev --open"
  yarn="yarn tauri [android|ios] dev --open"
  pnpm="pnpm tauri [android|ios] dev --open"
  deno="deno task tauri [android|ios] dev --open"
  bun="bun tauri [android|ios] dev --open"
  cargo="cargo tauri [android|ios] dev --open"
/>

:::note
如果你打算在 iOS 物理设备上运行该应用，则还必须提供 `--host` 参数，并且你的开发服务器必须使用 `process.env.TAURI_DEV_HOST` 值作为主机。有关更多信息，请参阅你框架的设置指南。

<CommandTabs
  npm="npm run tauri [android|ios] dev --open --host"
  yarn="yarn tauri [android|ios] dev --open --host"
  pnpm="pnpm tauri [android|ios] dev --open --host"
  deno="deno task tauri [android|ios] dev --open --host"
  bun="bun tauri [android|ios] dev --open --host"
  cargo="cargo tauri [android|ios] dev --open --host"
/>
:::

:::caution
要使用 Xcode 或 Android Studio，Tauri CLI 进程**必须**处于运行状态，且**无法**终止。
建议使用 `tauri [android|ios] dev --open` 命令，并保持该进程处于活动状态，直到关闭 IDE。
:::

#### 打开 web 检查器

- iOS

  必须使用 Safari 来访问 iOS 应用程序的 Web Inspector。

  在 Mac 机上打开 Safari，在菜单栏中选择 **Safari > 设置**，点击**高级**，然后选择**显示针对 Web 开发人员的功能** 。

  如果你在物理设备上运行，则必须在**设置 > Safari > 高级**中启用 **Web Inspector** 。

  完成所有步骤后，你应该会在 Safari 中看到 **Develop** 菜单，其中会找到要检查的已连接设备和应用程序。在你的设备或模拟器上选择 **localhost** ，然后点击即可打开 Safari 开发者工具窗口。

- Android

  将你的 Android 设备连接到电脑，打开 Android 设备中的**设置**应用，选择**关于**，滚动到版本号，然后点击 7 次。
  这将为你的 Android 设备启用开发者模式和**开发者选项**设置。

  要在你的设备上启用应用程序调试，你必须进入**开发者选项**设置，切换开发者选项开关并启用 **USB 调试**。

  :::note
  每个 Android 发行版都有自己的方式来启用开发者模式，请查看制造商的文档以获取更多信息。
  :::

  Android 版 Web Inspector 由 Google Chrome 的 DevTools 提供支持，你可以通过在 Chrome 浏览器中访问 `chrome://inspect` 来访问。
  如果你的 Android 应用程序正在运行，你的设备或模拟器应该会出现在远程设备列表中，你可以通过点击设备上的**检查**来打开开发者工具。

#### 故障排除

1. 在 Xcode 上运行构建脚本时出错

Tauri 通过创建一个构建阶段来连接到 iOS Xcode 项目，该阶段会执行 Tauri CLI，将 Rust 源代码编译为运行时加载的库。
构建阶段在 Xcode 进程上下文中执行，因此可能无法使用 Shell 修改（例如添加 PATH）。
因此，在使用可能不兼容的工具（例如 Node.js 版本管理器）时请一定小心。

2. 在首次执行 iOS 应用时提示网络权限

首次执行 `tauri ios dev` 时，iOS 可能会提示你获取查找并连接本地网络中设备的权限。
此权限是必需的，因为要从 iOS 设备访问你的开发服务器，我们必须将其暴露在本地网络中。
要在你的设备中运行你的应用，你必须点击“允许”并重新启动你的应用。

### 响应源代码更改

与你的 webview 实时反映变化的方式类似，Tauri 会监视你的 Rust 文件的变化，
因此当你修改其中任何一个文件时，你的应用程序就会自动重建并重新启动。

你可以使用 `tauri dev` 命令上的 `--no-watch` 标志来禁用此行为。

要限制需要监视更改的文件，可以在 src-tauri 文件夹中创建一个 `.taurignore` 文件。
此文件的工作方式与常规 Git 忽略文件类似，因此你可以忽略任何文件夹或文件：

```filename=.taurignore
build/
src/generated/*.rs
deny.toml
```

### 使用浏览器 DevTools

Tauri 的 API 仅在你的应用程序窗口中有效，因此一旦你开始使用它们，你将无法再在系统浏览器中打开前端。

如果你更喜欢使用浏览器的开发人员工具，则需要配置 [tauri-invoke-http](https://github.com/tauri-apps/tauri-invoke-http) 通过 HTTP 服务器桥接 Tauri API 调用。

### 源代码控制

在你的项目仓库中，你**应该**将 `src-tauri/Cargo.lock` 和 `src-tauri/Cargo.toml` 一起提交到 git，因为 Cargo 使用 lockfile 来提供确定性构建。因此, 建议所有应用程序都写入其 `Cargo.lock` 文件。你**不应该**提交 `src-tauri/target` 文件夹或其中的任何内容。
