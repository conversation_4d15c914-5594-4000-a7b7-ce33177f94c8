---
title: Neovim エディターでのデバッグ
i18nReady: true
---

Neovim エディターで Rust コードをデバッグするために使用できるプラグインは数多くあります。この章では、`nvim-dap` といくつかの追加プラグインを設定して Tauri アプリケーションをデバッグする方法を説明します。

> > > 《訳注》 **dap**　[Debug Adapter Protocol](https://microsoft.github.io/debug-adapter-protocol/) の略。開発ツール（IDE やエディターなど）とデバッガー間で使用される抽象プロトコル。

### 事前準備

`nvim-dap` の拡張機能には `codelldb` バイナリが必要です。[Github サイト](https://github.com/vadimcn/codelldb/releases) からご自分のシステムに適合するバージョンをダウンロードし、解凍してください。次項「`nvim-dap` の設定」に必要です。

> > > 《訳注》 **codelldb**　LLDBデバッガをVSCodeで使用するための拡張機能。

### nvim-dap の設定

[`nvim-dap`](https://github.com/mfussenegger/nvim-dap) および [`nvim-dap-ui`](https://github.com/rcarriga/nvim-dap-ui) の両プラグインをインストールします。github のページに記載されている指示に従うか、あるいはお気に入りのプラグイン・マネージャーを使用して実施してください。
なお、`nvim-dap-ui` には `nvim-nio` プラグインが必要であることに注意してください。

次に、「Neovim 設定」でプラグインを設定します：

```lua title="init.lua"
local dap = require("dap")

dap.adapters.codelldb = {
  type = 'server',
  port = "${port}",
  executable = {
    -- このパスは、ご自分のシステムに合わせて変更してください！
    command = '/opt/codelldb/adapter/codelldb',
    args = {"--port", "${port}"},
  }
}

dap.configurations.rust= {
  {
    name = "Launch file",
    type = "codelldb",
    request = "launch",
    program = function()
      return vim.fn.input('Path to executable: ', vim.fn.getcwd() .. '/target/debug/', 'file')
    end,
    cwd = '${workspaceFolder}',
    stopOnEntry = false
  },
}
```

この「設定」では、デバッガーを起動するたびに「デバッグを行なう Tauri App バイナリを指定する」ように求められます。

オプションとして、「`nvim-dap-ui` プラグイン」を設定して、デバッグ・セッションが開始および停止するたびに「デバッガー・ビューを自動的に切り替える」ように指定することもできます：

```lua title="init.lua"
local dapui = require("dapui")
dapui.setup()

dap.listeners.before.attach.dapui_config = function()
  dapui.open()
end
dap.listeners.before.launch.dapui_config = function()
  dapui.open()
end
dap.listeners.before.event_terminated.dapui_config = function()
  dapui.close()
end
dap.listeners.before.event_exited.dapui_config = function()
  dapui.close()
end

```

最後に、エディターでブレークポイントを表示するデフォルトの方法を変更できます：

```lua title="init.lua"
vim.fn.sign_define('DapBreakpoint',{ text ='🟥', texthl ='', linehl ='', numhl =''})
vim.fn.sign_define('DapStopped',{ text ='▶️', texthl ='', linehl ='', numhl =''})
```

### 開発サーバーの起動

アプリの起動に Tauri CLI を使用していないため、開発サーバーは自動的には起動しません。Neovim から開発サーバーの状態を制御するには、[overseer プラグイン](https://github.com/stevearc/overseer.nvim/tree/master) が利用できます。

バックグラウンドで実行されているタスクを制御する最良の方法は、[VS Code スタイルのタスク設定](https://github.com/stevearc/overseer.nvim/blob/master/doc/guides.md#vs-code-tasks)　を使用することです。これを行なうには、プロジェクトのディレクトリに `.vscode/tasks.json` ファイルを作成してください。

以下に、`trunk` を使用したプロジェクトの「タスク設定」例を示します。

```json title=".vscode/tasks.json"
{
  "version": "2.0.0",
  "tasks": [
    {
      "type": "process",
      "label": "dev server",
      "command": "trunk",
      "args": ["serve"],
      "isBackground": true,
      "presentation": {
        "revealProblems": "onProblem"
      },
      "problemMatcher": {
        "pattern": {
          "regexp": "^error:.*",
          "file": 1,
          "line": 2
        },
        "background": {
          "activeOnStart": false,
          "beginsPattern": ".*Rebuilding.*",
          "endsPattern": ".*server listening at:.*"
        }
      }
    }
  ]
}
```

### キーへのコマンド割り当ての例

以下に、デバッグ・セッションを開始および制御するためのキー・バインディング（キーへの機能や記号、コマンドの割り当て）の例を示します。

```lua title="init.lua"
vim.keymap.set('n', '<F5>', function() dap.continue() end)
vim.keymap.set('n', '<F6>', function() dap.disconnect({ terminateDebuggee = true }) end)
vim.keymap.set('n', '<F10>', function() dap.step_over() end)
vim.keymap.set('n', '<F11>', function() dap.step_into() end)
vim.keymap.set('n', '<F12>', function() dap.step_out() end)
vim.keymap.set('n', '<Leader>b', function() dap.toggle_breakpoint() end)
vim.keymap.set('n', '<Leader>o', function() overseer.toggle() end)
vim.keymap.set('n', '<Leader>R', function() overseer.run_template() end)
```

<div style="text-align: right">
  【※ この日本語版は、「Dec 22, 2024 英語版」に基づいています】
</div>
