---
title: アップグレード ＆ 移行
sidebar:
  label: Overview
  order: 10
---

Tauri 1.0 からのアップグレードや、他のフレームワークから移行するための一般的なシナリオと手順について説明します。

import { LinkCard, CardGrid } from '@astrojs/starlight/components';

<CardGrid>
  <LinkCard
    title="Tauri 1.0 からのアップグレード"
    href="../start/migrate/from-tauri-1/"
    description="バージョン 1.0 のプロジェクトをバージョン 2.0 にアップグレードするために必要なアップデートについて"
  />
  <LinkCard
    title="Tauri 2.0 ベータ版からの移行"
    href="../start/migrate/from-tauri-2-beta/"
    description="2.0 ベータ版プロジェクトを 2.0 正規版にアップグレードするために必要なアプデートについて"
  />
</CardGrid>

<div style="text-align: right;">
  【※ この日本語版は、「Oct 01, 2024 英語版」に基づいています】
  <br />
  Doc-JP 2.00.00
</div>
