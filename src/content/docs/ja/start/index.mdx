---
title: Tauri とは？
i18nReady: true
sidebar:
  order: 0
---

Tauri は、主要なデスクトップおよびモバイル・プラットフォームのすべてで、軽量・高速なバイナリ・ファイルを構築できるフレームワーク（仕組み）です。フロントエンドには、HTML、JavaScript、CSS にコンパイルされるフレームワークを利用してユーザー・エクスペリエンス（UX）を構築し、バックエンドの処理ロジックには、必要に応じて Rust、Swift、Kotlin などの言語を活用できます。

以下のシェル・コマンド（Bash、PowerShell、…）のいずれかを使用して、「Tauri アプリを作成する」[`create-tauri-app`](https://github.com/tauri-apps/create-tauri-app) を始める準備をしましょう。まず「必須事項ガイド」[prerequisites guide](/ja/start/prerequisites/) に従って、Tauri に必要なすべての「依存関係」をインストールし、次に推奨されるフロントエンド構成についての「フロントエンド構成ガイド」[Frontend Configuration guides](/ja/start/frontend/) を参照してください。

import Cta from '@fragments/cta.mdx';

<Cta />

最初のアプリを作成した後、「機能と使用例のリスト」 [List of Features & Recipes](/ja/plugin/) の章で Tauri のそのほかの機能や使い方を調べることができます。

## Tauri の利点

Tauri には、プログラム開発者が拠り所とできる三つの大きな利点があります：

- アプリ構築のための安全なプログラム基盤
- システム本体の Web ビューを使用することによるでバンドルサイズの軽量化
- 任意のフロントエンド技術の利用や複数のコンピュータ言語を統合できる柔軟性

Tauri の根本思想については、「Tauri 1.0 ブログ [Tauri 1.0 blog post](/ja/blog/tauri-1-0/)」を参照してください。

### 安全なプログラム基盤

Tauri は、「Rust」を用いて構築されていますので、Rust が提供する「メモリ」「スレッド」「データ・タイプ」の安全性という恩恵を受けています。Tauri で作成されたアプリは、Rust の熟練者でなくても、こうした利点を自動的に享受できます。

また、Tauri はメジャーなリリースでもマイナーなリリースでも、安全性の監査を実施しています。この監査には、Tauri 組織内のコードだけではなく、Tauri が依存している上流の依存関係も含まれています。もちろん、これですべてのリスクが緩和される訳ではありませんが、それでも、プログラム開発の拠り所となる強固な基盤となります。

詳しくは「Tauri 安全性方針 [Tauri security policy](https://github.com/tauri-apps/tauri/security/policy)」および「Tauri 2.0 監査レポート [Tauri 2.0 audit report](https://github.com/tauri-apps/tauri/blob/dev/audits/Radically_Open_Security-v2-report.pdf)」を御一読ください。

### ファイルサイズの軽量化

Tauri アプリは、すでにユーザー・システムに搭載されている Web ビューを利用しています。したがって、どの　Tauri アプリも、そのアプリに必要なコードとデータ資産のみを含み、アプリ内にブラウザ・エンジンを同梱する必要がありません。このことは、つまり、最小の Tauri アプリは 600 KB 以下のサイズになりうることを意味しています。

最適化されたアプリの作成の仕方については、「アプリ・サイズの考え方 [App Size concept](/ja/concept/size/) をご覧ください。

### 柔軟性のある基本仕様

Tauri は ウェブ技術を利用していますので、事実上どのフロントエンド技術も Tauri と互換性があります。「フロントエンド構成ガイド [Frontend Configuration guide](/ja/start/frontend/) 」では、良く用いられるフロントエンド技術の一般的な構成例を記載しています。

JavaScript 部分と Rust 部分間の結合には、JavaScript の「`invoke`（呼出し）」関数が、Swift や Kotlin との結合には「[Tauri Plugins](/ja/develop/plugins/)（Tauri プラグイン）」が利用できます。

[TAO](https://github.com/tauri-apps/tao) は Tauri のウィンドウ生成を、[WRY](https://github.com/tauri-apps/wry) はウェブ表示のレンダリングを行なうプラグインです。このようなプラグインは Tauri によって管理されているライブラリで、Tauri が対応できる以上の、より進んだシステム統合が必要な場合に直接使用できます。

加えて、Tauri には、Tauri 本体が対応できる機能を拡張する多くのプラグインもあります。このようなプラグインは、コミュニティによって提供されているプラグインと並んで、「プラグイン [Plugins section](/ja/plugin/)」のセクションで見つけることができます。

<div style="text-align: right;">
  【※ この日本語版は、「Oct 1, 2024 英語版」に基づいています】
  <br />
  Doc-JP 2.00.00
</div>
