---
title: Next.js
i18nReady: true
tableOfContents:
  collapseLevel: 1
  minHeadingLevel: 2
  maxHeadingLevel: 5
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';
import CommandTabs from '@components/CommandTabs.astro';

Next.js（ネクスト・ジェイエス）は React 用のメタ・フレームワークです。Next.js の詳細については、https://nextjs.org ををご覧ください。以下の説明内容は、「Next.js 14.2.3 バージョン」に準拠しています。

## チェック項目

- `output: 'export'` を設定して「静的エクスポート」を使用してください。Tauri はサーバー・ベースの方式には対応していません。
- `tauri.conf.json` の `frontendDist` に「`out`」ディレクトリを使用してください。

## 設定例

<Steps>

1.  ##### Tauri の設定をアップデート

    <Tabs>

      <TabItem label="npm">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "npm run dev",
        "beforeBuildCommand": "npm run build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="yarn">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "yarn dev",
        "beforeBuildCommand": "yarn build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="pnpm">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "pnpm dev",
        "beforeBuildCommand": "pnpm build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    <TabItem label="deno">

    ```json
    // src-tauri/tauri.conf.json
    {
      "build": {
        "beforeDevCommand": "deno task dev",
        "beforeBuildCommand": "deno task build",
        "devUrl": "http://localhost:3000",
        "frontendDist": "../out"
      }
    }
    ```

    </TabItem>

    </Tabs>

2.  ##### Next.js の設定をアップデート

    ```ts
    // next.conf.mjs
    const isProd = process.env.NODE_ENV === 'production';

    const internalHost = process.env.TAURI_DEV_HOST || 'localhost';

    /** @type {import('next').NextConfig} */
    const nextConfig = {
      // Next.js が「SSR（サーバーサイドレンダリング」ではなく「SSG（静的サイト生成）」を使用していること。
      // https://nextjs.org/docs/pages/building-your-application/deploying/static-exports
      output: 'export',
      // 注: この機能は、SSG モードで Next.js イメージ・コンポーネントを使用するために必要です。
      // 別の回避方法については https://nextjs.org/docs/messages/export-image-api を参照してください。
      images: {
        unoptimized: true,
      },
      // assetPrefix を設定してください。さもないとサーバーはアセットを適切に解決できません。
      assetPrefix: isProd ? undefined : `http://${internalHost}:3000`,
    };

    export default nextConfig;
    ```

3.  ##### package.json の設定をアップデート

        ```json
        "scripts": {
          "dev": "next dev",
          "build": "next build",
          "start": "next start",
          "lint": "next lint",
          "tauri": "tauri"
        }
        ```

</Steps>

<div style="text-align: right;">
  【※ この日本語版は、「Nov 01, 2024 英語版」に基づいています】
  <br />
  Doc-JP 2.00.00
</div>
