---
title: 基本設計思想
sidebar:
  order: 0
  label: 概要
i18nReady: true
---

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

Tauri には、中核となる設計思想がいくつかあります。これらの項目はアプリケーションを開発する際に開発者が知っておくべきものです。以下が、Tauri のフレームワークを最大限に活用するために、より詳しく理解しておくべき概念です。

<CardGrid>
  <LinkCard
    title="Tauri アーキテクチャ"
    href="/ja/concept/architecture/"
    description="基本設計とエコシステム"
  />
  <LinkCard
    title="プロセス間通信（IPC）"
    href="/ja/concept/inter-process-communication/"
    description="プロセス間通信の内部構造"
  />
  <LinkCard
    title="安全性"
    href="/ja/security/"
    description="Tauri の安全性強化施策"
  />
  <LinkCard
    title="プロセス・モデル"
    href="/ja/concept/process-model/"
    description="Tauri が管理するプロセスとその理由"
  />
  <LinkCard
    title="アプリのサイズ"
    href="/ja/concept/size/"
    description="アプリ・サイズを最小化する方法"
  />
</CardGrid>

<div style="text-align: right">
  【※ この日本語版は、「Feb 22, 2025 英語版」に基づいています】
  <br />
  Doc-JP 2.00.00
</div>
