---
title: プロセス・モデル
sidebar:
  order: 0
i18nReady: true
---

Tauri は、Electron（エレクトロン）や多くの最新 Web ブラウザのような、マルチプロセス・アーキテクチャを採用しています。このガイドでは、こうした設計方針の選択理由と、なぜそれが安全なアプリケーションを作成する上で重要であるのかについて説明します。

## マルチプロセスである理由

GUI（グラフィカル・ユーザー・インターフェース）アプリケーションの初期には、計算・インターフェイスの描画・ユーザー入力への反応を一つのプロセスで実行するのが一般的でした。ご想像のとおり、このプロセスは、時間の掛かる高負荷の計算処理によってユーザー・インターフェイスが無応答のままになったり、さらに悪いことには、一つのアプリ・コンポーネントの障害がアプリ全体のクラッシュを引き起こしてしまったりすることを意味していました。

この結果、より強靭なアーキテクチャが必要であることは明らかで、アプリケーションは異なるコンポーネントを異なるプロセスで実行するようになりました。これにより、最新のマルチコア CPU をより有効に活用し、はるかに安全なアプリケーションが作成できます。各コンポーネントは異なるプロセスに分散されて実行されているため、一つのコンポーネントでクラッシュが発生しても、システム全体に影響が及ぶことはありません。プロセスが無効な状態になった場合は簡単にそのプロセスを再起動できるのです。

また、各プロセスに、そのジョブをが完了するのに必要な最小限の権限のみを付与することで、潜在的な弱点箇所の影響範囲を制限することもできます。このやり方は [最小権限の原則] として知られており、現実世界ではよく見られます。生け垣の手入れに庭師が来た場合、庭の鍵を渡しますが、家の鍵は**渡さない**でしょう（家の中への出入りは庭師には必要ないですよね）？　同じ考え方がコンピュータ・プログラムにも当てはまります。アクセス権を少なくすればするほど、侵入されたときの被害が少なくなります。

## コア・プロセス

各 Tauri アプリケーションには「コア・プロセス」があり、これがアプリケーションの「エントリ・ポイント」として機能します。コア・プロセスは、オペレーティング・システムにフル・アクセスできる唯一のコンポーネントです。

この「コア部」の第一の役割は、そのアクセス権を用いて「アプリケーション・ウィンドウ」、「システムトレイ・メニュー」、および「通知」を作成・調整することです。Tauri は、このような処理を簡単にするために必要となるクロス・プラットフォームの抽象化を実装しています。また、すべての [プロセス間通信]（IPC）を「コア・プロセス」経由で転送し、IPC メッセージをこの中央部分一箇所で傍受し、フィルター処理を行ない、操作できるようにします。

「コア・プロセス」は、設定やデータベースの接続のような「グローバル状態」の管理も担当する必要があります。これにより、ウィンドウ間の状態を簡単に同期し、フロントエンドで覗き見しようとする外部の不審な目からビジネス上の機密データを保護できます。

私達が Tauri の実装に Rust を選択したのは、[所有権]の概念により、
優れたパフォーマンスを維持する一方で、メモリの安全性が保証されるからです。

<figure>

```d2 sketch pad=50
direction: right

Core: {
  shape: diamond
}

"Events & Commands 1": {
  WebView1: WebView
}

"Events & Commands 2": {
  WebView2: WebView
}

"Events & Commands 3": {
  WebView3: WebView
}

Core -> "Events & Commands 1"{style.animated: true}
Core -> "Events & Commands 2"{style.animated: true}
Core -> "Events & Commands 3"{style.animated: true}

"Events & Commands 1" -> WebView1{style.animated: true}
"Events & Commands 2" -> WebView2{style.animated: true}
"Events & Commands 3" -> WebView3{style.animated: true}
```

<figcaption>Tauri プロセス・モデルの略図。一つの「コア・プロセス」が一つ以上の WebView プロセスを制御します。</figcaption>
</figure>

## WebView プロセス

「コア・プロセス」が実際のユーザー・インターフェイス（UI）をレンダリングしているわけではありません。オペレーティング・システムによって提供される WebView ライブラリを利用して、WebView プロセスを起動しているのです。WebView はブラウザーのような環境で、あなたが作成した HTML、CSS、JavaScript を実行しているのです。

このことはつまり、従来の Web 開発で使用されているほとんどの技法とツールが Tauri アプリケーションの作成で使用できるということです。たとえば、Tauri の参考事例の多くが、[Svelte] フロントエンド・フレームワークと [Vite] バンドラーを使用して作成されています。

セキュリティ面でも最良の方法が取られています：　たとえば、ユーザー入力を常にサニタイズ（無害化）し、フロントエンドでは決して機密情報を処理しないようにし、理想的には、攻撃対象領域を小さく保つために可能な限り多くのビジネス・ロジック（業務処理手順）を「コア・プロセス」に委ねる必要があります。

他の類似した方法とは異なり、WebView ライブラリは最終的な実行可能ファイルには**含まれず**、実行時に動的にリンクされます[^1]。これにより、アプリケーションは*大幅に*小さくなりますが、従来の Web 開発と同様に、プラットフォームの違いに留意する必要があるということも意味しています。

[^1]:
    現時点では、Tauri は、Windows では [Microsoft Edge WebView2] を、macOS では [WKWebView] を、
    Linux では [webkitgtk] を使用しています。

[最小権限の原則]: https://ja.wikipedia.org/wiki/最小権限の原則
[プロセス間通信]: /ja/concept/inter-process-communication/
[所有権]: https://doc.rust-lang.org/book/ch04-01-what-is-ownership.html
[microsoft edge webview2]: https://docs.microsoft.com/ja-jp/microsoft-edge/webview2/
[wkwebview]: https://developer.apple.com/documentation/webkit/wkwebview
[webkitgtk]: https://webkitgtk.org
[svelte]: https://svelte.dev/
[vite]: https://vitejs.dev/

<div style="text-align: right;">
【※ この日本語版は、「Feb 22, 2025 英語版」に基づいています】<br />
Doc-JP 2.00.00
</div>
