---
title: アイソレーション型（Isolation Pattern）
i18nReady: true
---

アイソレーション（分離・隔絶）型は、フロントエンドから送信された Tauri API メッセージが Tauri コア部に到達する前に、JavaScript を使用して傍受・変更する方法です。アイソレーション型で挿入される安全な JavaScript コードは、アイソレーション型アプリケーションと呼ばれます。

## アイソレーション型の必要性

アイソレーション型の目的は、Tauri コア部への望ましくないあるいは悪意のあるフロントエンドからの呼び出しから、アプリケーションを保護するための仕組みを開発者に提供することです。アイソレーション型が必要となったのは、フロントエンドで実行される信頼できないコンテンツから生じる脅威に対応するために生まれました。このような脅威は多くの依存関係を持つアプリケーションによくあるケースです。アプリケーションが遭遇する可能性のある多くの脅威源のリストについては、[セキュリティ：脅威モデル] を参照してください。

上記の最大の脅威モデルは「開発時の脅威」でしたが、このような脅威は、アイソレーション型を設計する際に念頭に置かれていたものです。多くのフロントエンドのビルド・ツールが、しばしば深くネスト化された数十（もしくは数百）の依存関係で成り立っているだけではなく、複雑なアプリケーション側にも、数多くの（こちらもしばしば深くネスト化された）依存関係があり得るので、最終出力版にバンドルされるのです。

## アイソレーション型を使用する局面

Tauri は、アイソレーション型が使用できる場合には常に使用することを強く推奨しています。アイソレーション型のアプリケーションではフロントエンドからの _**すべての**_ メッセージを「捕捉」（インターセプト）するため、アイソレーション型が _常に_ 使用されます。

さらに Tauri では、外部の Tauri API を使用するときは常に、アプリケーションを「封鎖」（ロックダウン）することを強く推奨しています。アプリの開発者は、安全なアイソレーション型アプリケーションを利用して IPC 入力を検証し、その入力内容が確実に想定されるパラメータの範囲内にあることを確かめます。事例としては、ファイルの読み取りまたは書き込みの呼び出しが、そのアプリケーションの想定外の場所へのパスにアクセスしようとしていないかを確認したり、あるいは、Tauri API の「HTTP フェッチコール」が 「Origin ヘッダー」にそのアプリケーションが想定しているもののみを設定しているかを確認したりすることです。

とはいえ、フロントエンドからの _**すべての**_ メッセージを捕捉するため、常時動作 API、たとえば [Events] など、でも動作します。一部のイベントでは、アプリ独自の Rust コードにアクションを実行させる可能性があるので、それにも同様の検証手法で対応することができます。

## アイソレーション型の適用

アイソレーション型で重要なのは、フロントエンドと Tauri Core（タウリ・コア部）との間に安全なアプリケーションを挿入して、IPC 受信メッセージを捕捉し修正することです。これには、`<iframe>` のサンドボックス機能（隔離機能）を使用して、メインのフロントエンド・アプリケーションと並行して JavaScript を安全に実行することで実現します。Tauri は、ページの読み込み中にアイソレーション型を発動し、Tauri Core に対するすべての IPC 呼び出しを、直接ではなく、サンドボックス化（隔離化）されたアイソレーション型アプリケーション経由で行なうように強制的にルートを切り替えます。Tauri Core に渡されるメッセージの準備が整うと、メッセージはブラウザ実装の [SubtleCrypto] を使用して暗号化され、メインのフロントエンド・アプリケーションに返されます。
フロントエンドに戻されるとすぐに、メッセージは直接 Tauri Core に渡され、そこで通常どおりに復号化されて読み取られます。

誰かがアプリケーションの特定のバージョンのキーを手動で読み取り、暗号化後にそのキーを用いてメッセージの変更を行なえないことを確実にするために、アプリケーションが実行されるたびに新しいキーが生成されます。

### IPC メッセージのおおよその手順

動作の流れを判りやすくするために、アイソレーション型の IPC メッセージが Tauri Core に送信されるときに実行されるおおよその手順を、順序付きリストで以下に示します：

1. Tauri の IPC ハンドラーがメッセージを受け取ります
2. IPC ハンドラー　→　アイソレーション型アプリケーション
3. `[sandbox]`　アイソレーション型アプリケーションのフックが実行され、メッセージの変更を行なう可能性があります。
4. `[sandbox]` メッセージは「実行時に生成されるキー」を使用して AES-GCM で暗号化されます
5. `[encrypted]` アイソレーション型アプリケーション　→　IPC ハンドラー
6. `[encrypted]` IPC ハンドラー　→　Tauri Core

_注記：　矢印（→）は、メッセージ・パッシング（受け渡し）を示します。_

### パフォーマンスへの影響

安全なアイソレーション型アプリケーションは、メッセージの暗号化が行なわれるため、何も行なわない場合ですら [ブラウンフィールド型] と比較して追加のオーバーヘッド・コストが発生します。（適切なパフォーマンスを維持するために、慎重に保守され依存関係も少ないであろう）パフォーマンス重視のアプリケーションを除けば、ほとんどのアプリケーションは比較的小型で AES-GCM 暗号化方式も比較的高速であるため、IPC メッセージの暗号化／復号化の実行時コストを意識する必要はありません。もし AES-GCM について馴染みがないとしても、ここで関連するのは、それが [SubtleCrypto] に含まれる唯一の認証モード・アルゴリズムであり、おそらく「[TLS][transport_layer_security] 暗号化プロトコル」の内部で既に毎日使用しているということだけです。

Tauri アプリケーションが起動されるたびに一度、暗号化された安全なキーも生成されますが、システムがすでに十分なエントロピー（ランダム性）を備えていて、即座に十分な乱数を返すのであれば、この処理には通常気付きません。これは「デスクトップ環境」では極めて一般的です。「ヘッドレス環境」で [WebDriver との統合テスト] などを実行する場合で、オペレーティング・システムにエントロピー生成サービス《訳注：　「乱数生成」機能》が含まれていない場合には、`haveged` などの何らかのエントロピー生成サービスをインストールすることをお勧めします。<sup>Linux 5.6 (March 2020) 版から、投機的実行によるエントロピー生成が含まれるようになりました。</sup>

### 制限事項

アイソレーション型には、プラットフォームとの不整合から生じる制限事項がいくつかあります。最も重大な制限は、Windows 上のサンドボックス化された `<iframes>` 内に外部ファイルが正しく読み込まれないことによるものです。このため、アイソレーション型アプリケーションに関連するスクリプトの内容を取得してインラインに挿入する、ビルド時の簡単なスクリプト・インライン化手順を実装しました。つまりこれは、`<script src="index.js"></script>` のようなファイルの典型的なバンドル、あるいは単純な挿入は依然として正常に機能しますが、ES Modules（ECMAScript Modules）などの新しいメカニズムは上手く読み込ま**ない**ということを意味します。

## 推奨事項

アイソレーション型アプリケーションの目的は開発時の脅威から保護することであるため、アイソレーション型アプリケーションはできる限り簡素化しておくことを強くお勧めします。依存関係を最小限に抑えるよう努めるだけでなく、必要なビルド手順を最小限に抑えることを検討する必要もあります。これにより、フロントエンド・アプリケーションを覆っているアイソレーション型アプリケーションへのサプライ・チェーン攻撃には心配する必要がなくなります。

## アイソレーション型アプリケーションの作成

次の例では、小さな「hello-world」のようなアイソレーション型アプリケーションを作成し、それを仮に既存の Tauri アプリケーションに接続することを想定します。このアプリでは、通過するメッセージの検証は行なわれず、WebView コンソールに内容が出力されるだけです。

この事例の目的のため、`tauri.conf.json` と同じディレクトリにあると仮定します。既存の Tauri アプリケーションの `distDir` は `../dist` に設定されています。

`../dist-isolation/index.html`:

```html
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Isolation Secure Script</title>
  </head>
  <body>
    <script src="index.js"></script>
  </body>
</html>
```

`../dist-isolation/index.js`:

```javascript
window.__TAURI_ISOLATION_HOOK__ = (payload) => {
  // 何も検証や修正せず、ただ hook から内容を印字するだけです。
  console.log('hook', payload);
  return payload;
};
```

あとは、アイソレーション型を使用するように `tauri.conf.json` の[設定](#設定) を変更し、ブート処理経路を [ブラウンフィールド型] からアイソレーション型に切り替えるだけです。

## 設定

メインのフロントエンド `distDir` が `../dist` に設定されていると仮定します。また、アイソレーション型アプリケーションを `../dist-isolation` に出力します。

```json
{
  "build": {
    "distDir": "../dist"
  },
  "app": {
    "security": {
      "pattern": {
        "use": "isolation",
        "options": {
          "dir": "../dist-isolation"
        }
      }
    }
  }
}
```

[transport_layer_security]: https://ja.wikipedia.org/wiki/Transport_Layer_Security
[セキュリティ：脅威モデル]: /ja/security/lifecycle/
[events]: /ja/reference/javascript/api/namespaceevent/
[subtlecrypto]: https://developer.mozilla.org/ja-JP/docs/Web/API/SubtleCrypto
[ブラウンフィールド型]: /ja/concept/inter-process-communication/brownfield/
[WebDriver との統合テスト]: /ja/develop/tests/webdriver/

<div style="text-align:right">
［※ この日本語版は、「Feb 22, 2025 英語版」に基づいています］<br />
Doc-JP 2.00.00
</div>
