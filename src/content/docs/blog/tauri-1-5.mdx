---
title: Announcing Tauri 1.5.0
date: 2023-09-28
authors: lucas<PERSON>g
excerpt: The Tauri team is happy to announce the 1.5.0 release.
---

import CommandTabs from '@components/CommandTabs.astro';

![Tauri 1.5 Launch Hero Image](./tauri_1_5_images/header.png)

The Tauri team is excited to announce the 1.5 release. This version includes several new features and important bug fixes such as improved resources bundling, code signing enhancements, `notarytool` migration on macOS and Bun support.

## Upgrading

Make sure to update both NPM and Cargo dependencies to the 1.5.0 release. You can update the dependencies with:

<CommandTabs
  npm="npm install @tauri-apps/cli@latest @tauri-apps/api@latest"
  yarn="yarn upgrade @tauri-apps/cli @tauri-apps/api --latest"
  pnpm="pnpm update @tauri-apps/cli @tauri-apps/api --latest"
  cargo="cargo update"
/>

## What's in 1.5.0

### Notarytool

At WWDC 2021 Apple introduced notarytool, a new tool for interacting with the Apple notary service.
Before the 1.5 release, <PERSON><PERSON> used altool to notarize your application, but that tool has been deprecated and will stop working for notarization on 2023-11-01.
You **must** upgrade your Tauri CLI to 1.5 before that.

If you are using API keys for authentication with the notary service, notarytool no longer automatically searches for your APi key `.p8` file.
We recommend users to define its path via the `APPLE_API_KEY_PATH` environment variable, though to avoid breaking changes we perform the same lookup done by altool to find your key file in case you did not set the environment variable.
In the future, this might change, so please adjust your publish pipelines accordingly.

### Bun support

The Tauri CLI now supports the [Bun](https://bun.sh/) package manager.

We would like to thank [@colinhacks](https://github.com/colinhacks) for submitting the pull requests for this feature!

### Code signing improvements

Starting on Tauri 1.5, our bundler now signs all executables (including sidecars, app executables and NSIS uninstaller) and macOS frameworks.
We also improved our notarization algorithm adding support to the `APPLE_TEAM_ID` environment variable to properly define the team ID associated with your account in case you belong to multiple teams.

We would like to thank [@tr3ysmith](https://github.com/tr3ysmith) for submitting the pull requests for this feature!

### macOS frameworks

This release comes with enhanced macOS frameworks support by code signing all custom frameworks you inject via `tauri.conf.json > tauri > bundle > macOS > frameworks` and defining the `@rpath` value fixing a crash when updating your app.

We would like to thank [@tr3ysmith](https://github.com/tr3ysmith) for submitting the pull requests for this feature!

### Mixed content on Windows

We now offer a configuration option to switch our custom protocol on Windows to use the `http` scheme instead of `https`.
This reduces the security of your application on Windows since it allows connecting to insecure endpoints such as `ws://url`,
but it matches the behavior on Linux and macOS custom protocols. To enable it, set the `tauri.conf.json > tauri > security > dangerousUseHttpScheme` to true.

### Other changes

Check out the entire list of changes:

- [tauri](https://github.com/tauri-apps/tauri/releases/tag/tauri-v1.5.0)
- [tauri-cli](https://github.com/tauri-apps/tauri/releases/tag/tauri-cli-v1.5.0)
- [tauri-bundler](https://github.com/tauri-apps/tauri/releases/tag/tauri-bundler-v1.4.0)
