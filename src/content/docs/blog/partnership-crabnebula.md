---
title: 'Strengthening Tauri: Our Partnership with CrabNebula'
date: 2023-11-13
authors: [nothingismagick]
excerpt: "As an open-source project, <PERSON><PERSON>'s primary mission has always been to provide a secure, efficient framework for multi-platform application development. Understanding the concerns and needs of our community, we are excited to share insights into our partnership with CrabNebula and how it bolsters the stability and future of <PERSON><PERSON>."
---

![Hero Image](./partnership-crabnebula/header.png)

As an open-source project, <PERSON><PERSON>'s primary mission has always been to provide a secure, efficient framework for multi-platform application development. Understanding the concerns and needs of our community, we are excited to share insights into our partnership with CrabNebula and how it bolsters the stability and future of Tauri.

## CrabNebula's Contribution to Stability and Security

CrabNebula's role in <PERSON><PERSON>'s journey over the past year has been pivotal. By dedicating full-time engineers to work explicitly on <PERSON><PERSON>'s development and maintenance, they are helping us enhance the framework's stability and security. They are literally being paid to resolve bugs and issues, pushing forward on the 2.0 release, and offering their support in the many Discord discussions.

Specifically, we would like to call out some folks from CrabNebula for helping drive the community and the code forward: <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.

Their efforts in conducting thorough security audits of minor releases are particularly crucial. These audits not only help us identify and fix vulnerabilities but also reinforce our commitment to delivering a secure framework for all users. Collaborative features developed with Impierce and Kino AI under this partnership further expand Tauri's capabilities, which will help it to remain cutting-edge and reliable.

## A Partnership Model Focused on Open Source Values

Our collaboration with CrabNebula is grounded in open-source principles. It's designed not just for mutual benefit but to serve as a sustainable model for future collaborations. We understand the importance of maintaining the integrity and independence of Tauri as an open-source project. This partnership respects these values, ensuring that Tauri continues to be driven by community needs and open-source innovation.

## Long-Term Impact on the Tauri Ecosystem

The partnership is more than just a short-term alliance; it's a strategic move to secure the long-term future of Tauri. By bringing additional resources and diverse perspectives, we're enhancing the framework's robustness. Importantly, involvement of companies using Tauri, facilitated by this partnership, gets cycled directly into the development process. This feedback loop is vital for addressing real-world usage challenges, because it is important to all of us that Tauri remains relevant and continues to evolve according to the needs of its users.

## Creating Channels for Community Engagement

In line with our commitment to community engagement, we will be launching a #crabnebula channel on Tauri's Discord. This will be a dedicated space for direct communication, collaboration, and feedback. We want to make sure that our community's voice is heard and integrated into the ongoing development of Tauri.

## Call to Action

To our developers, contributors, users, and donors - we invite you to engage with us on this journey. Your input is invaluable as we continue to develop Tauri in partnership with CrabNebula. Together, we are not just building a framework; we are seeking ways to enhance its longevity and relevance in the ever-changing world of technology.

If you like, go over to the CrabNebula blog and read [their perspective](https://crabnebula.dev/blog/tauri-partnership) on this partnership.

- [Discord Tauri](https://discord.gg/tauri)
- [X Tauri](https://x.com/TauriApps)
- [Mastadon Tauri](https://fosstodon.org/@TauriApps)
- [GitHub Tauri](https://github.com/tauri-apps)
- [Website Tauri](https://tauri.app)
- [X CrabNebula](https://x.com/CrabNebulaDev)
- [Mastadon CrabNebula](https://fosstodon.org/@crabnebula)
- [GitHub CrabNebula](https://github.com/crabnebula-dev)
- [Website Crabnebula](https://crabnebula.dev)
