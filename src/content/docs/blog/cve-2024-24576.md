---
title: Rust Security Advisory CVE-2024-24576
date: 2024-04-10
authors: [tweidinger, chip]
excerpt: Recent Rust Security Advisory CVE-2024-24576.
---

The Rust Security Response WG announced [`CVE-2024-24576`](https://blog.rust-lang.org/2024/04/09/cve-2024-24576.html), which affects the Rust Standard Library on Windows.

> TL;DR: Upgrade your Rust version to `1.77.2`.

## How Does it Affect Tauri as a Library?

Some Tauri organization repositories use batch files (`cmd.exe` under the hood) for developer environment tooling such as build scripts.
No reviewed repositories use batch files for runtime code.

We don't see additional risks for the Tauri project based on this CVE.

Nevertheless, we will update our CI systems to use the latest Rust version.

## Is My Tauri App Affected?

In general you are possibly affected if you fulfil **all** of the below criteria:

- You ship your app on **Windows**
- Your project enables the Tauri v1 [`shell`](https://tauri.app/v1/api/js/shell/) feature with `"execute": true` or the v2 [`shell-plugin`](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/shell) with `allow-execute` permission
- You allow arguments in the `scope` element of the `shell` feature
- **You pass untrusted input to `cmd.exe` or `.bat`/`.cmd` files and improperly validate the scope** (🚩)

If any of these criteria are not fulfilled in your application you are likely **NOT** affected.

If you implement custom commands or logic written in your application that directly exposes the Rust `Command` with arguments provided at runtime, you may be affected.
While not Tauri specific, this pattern could affect any Rust project.

## Conclusion

Please upgrade your Rust version to `1.77.2`
as soon as possible and distribute updates to your users.

This investigation and writeup was performed in cooperation with our partner [CrabNebula](https://crabnebula.dev/blog/cve-2024-24576/) ❤️.

---

[Read more about this security advisory here](https://flatt.tech/research/posts/batbadbut-you-cant-securely-execute-commands-on-windows/).
This affects many programming languages, this specific CVE is just the one filed for Rust.
