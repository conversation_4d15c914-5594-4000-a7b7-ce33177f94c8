---
title: Frontend Configuration
i18nReady: true
sidebar:
  label: Overview
  order: 10
---

import { LinkCard, CardGrid } from '@astrojs/starlight/components';

<PERSON><PERSON> is frontend agnostic and supports most frontend frameworks out of the box. However, sometimes a framework need a bit of extra configuration to integrate with <PERSON><PERSON>. Below is a list of frameworks with recommended configurations.

If a framework is not listed then it may work with <PERSON><PERSON> with no additional configuration needed or it could have not been documented yet. Any contributions to add a framework that may require additional configuration are welcome to help others in the Tauri community.

## Configuration Checklist

Conceptually <PERSON><PERSON> acts as a static web host. You need to provide <PERSON><PERSON> with a folder containing some mix of HTML, CSS, Javascript and possibly WASM that can be served to the webview Tau<PERSON> provides.

Below is a checklist of common scenarios needed to integrate a frontend with Tau<PERSON>:

{/* TODO: Link to core concept of SSG/SSR, etc. */}
{/* TODO: Link to mobile development server guide */}
{/* TODO: Concept of how to do a client-server relationship? */}

- Use static site generation (SSG), single-page applications (SPA), or classic multi-page apps (MPA). <PERSON>ri does not natively support server based alternatives (such as SSR).
- For mobile development, a development server of some kind is necessary that can host the frontend on your internal IP.
- Use a proper client-server relationship between your app and your API's (no hybrid solutions with SSR).

## JavaScript

{/* TODO: Help me with the wording here lol */}
For most projects we recommend [Vite](https://vitejs.dev/) for SPA frameworks such as React, Vue, Svelte, and Solid, but also for plain JavaScript or TypeScript projects. Most other guides listed here show how to use Meta-Frameworks as they are typically designed for SSR and therefore require special configuration.

<CardGrid>
  <LinkCard title="Next.js" href="/start/frontend/nextjs/" />
  <LinkCard title="Nuxt" href="/start/frontend/nuxt/" />
  <LinkCard title="Qwik" href="/start/frontend/qwik/" />
  <LinkCard title="SvelteKit" href="/start/frontend/sveltekit/" />
  <LinkCard title="Vite (recommended)" href="/start/frontend/vite/" />
</CardGrid>

## Rust

<CardGrid>
  <LinkCard title="Leptos" href="/start/frontend/leptos/" />
  <LinkCard title="Trunk" href="/start/frontend/trunk/" />
</CardGrid>

<br />

:::tip[Framework Not Listed?]

Don't see a framework listed? It may work with Tauri without any additional configuration required. Read the [configuration checklist](/start/frontend/#configuration-checklist) for any common configurations to check for.

:::
