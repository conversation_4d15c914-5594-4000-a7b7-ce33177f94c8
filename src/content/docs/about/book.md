---
title: The Tauri Book
i18nReady: true
---

:::note[Progress Update]

We're actively working on authoring and writing The Tauri Book. We've encountered delays due to the immense growth of <PERSON><PERSON> but have recently re-prioritized this project. While we don't yet have details on the timelines of a release, you can keep an eye on this page for updates.

We'd like to apologize for this being delayed beyond the originally communicated publish date. If you've donated through GitHub Sponsors or Open Collective and would like to request a refund you may do so via Open Collective: [Contact Tauri on Open Collective](https://opencollective.com/tauri/contact).

:::

### Overview

The Tauri Book will guide you through the history of <PERSON><PERSON> and the design decisions we've made. It will also talk in depth about why privacy, security and sustainability are important and fundamental discussions you can apply to any modern software project.

Topics included are:

- The method and reasoning behind the design of <PERSON>ri
- The options you have when building with <PERSON><PERSON>
- That you don't have to choose between shipping fast and being sustainable and responsible
- Why we chose the Rust language as a binding and application layer for Tauri
- Why a binary review is important

### History

In 2020, the manufacture of native-apps has become easier and more accessible than ever before. All the same, beginners and seasoned developers alike are confronted with tough choices in a rapidly changing landscape of security and privacy. This is especially true in the semi-trusted environment of user devices.

<PERSON><PERSON> takes the guesswork out of the equation, as it was designed from the ground up to embrace new paradigms of secure development and creative flexibility that leverage the language features of Rust and lets you build an app using any frontend framework you like. Find out how you can design, build, audit and deploy tiny, fast, robust, and secure native applications for the major desktop and mobile platforms, all from the exact same codebase and in record time - without even needing to know the Rust programming language.

Authors and Tauri co-founders Daniel and Lucas take you on a journey from theory to execution, during which you will learn why Tauri was built and how it works under the hood. Together with guest insights that specialize in Open Source, DevOps, Security and Enterprise Architecture, this book also presents discourse-formatted philosophical discussions and open-source sustainability viewpoints from which your next-gen apps will profit - and your users will benefit.
