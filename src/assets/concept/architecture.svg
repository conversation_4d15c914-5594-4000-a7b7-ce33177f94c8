<svg xmlns="http://www.w3.org/2000/svg" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 1071.03125 368" height="368" width="1071.03125" id="container"><style>#container{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#container .error-icon{fill:#552222;}#container .error-text{fill:#552222;stroke:#552222;}#container .edge-thickness-normal{stroke-width:2px;}#container .edge-thickness-thick{stroke-width:3.5px;}#container .edge-pattern-solid{stroke-dasharray:0;}#container .edge-pattern-dashed{stroke-dasharray:3;}#container .edge-pattern-dotted{stroke-dasharray:2;}#container .marker{fill:#333333;stroke:#333333;}#container .marker.cross{stroke:#333333;}#container svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#container .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#container .cluster-label text{fill:#333;}#container .cluster-label span,#container p{color:#333;}#container .label text,#container span,#container p{fill:#333;color:#333;}#container .node rect,#container .node circle,#container .node ellipse,#container .node polygon,#container .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#container .flowchart-label text{text-anchor:middle;}#container .node .label{text-align:center;}#container .node.clickable{cursor:pointer;}#container .arrowheadPath{fill:#333333;}#container .edgePath .path{stroke:#333333;stroke-width:2.0px;}#container .flowchart-link{stroke:#333333;fill:none;}#container .edgeLabel{background-color:#e8e8e8;text-align:center;}#container .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#container .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#container .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#container .cluster text{fill:#333;}#container .cluster span,#container p{color:#333;}#container div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#container .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#container :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="10" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g id="Upstream" class="cluster default flowchart-label"><rect height="168" width="116.28125" y="184" x="889.203125" ry="0" rx="0" style=""/><g transform="translate(912.328125, 184)" class="cluster-label"><foreignObject height="19" width="70.03125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">Upstream</span></div></foreignObject></g></g><g id="Core" class="cluster default flowchart-label"><rect height="134" width="1055.03125" y="0" x="0" ry="0" rx="0" style=""/><g transform="translate(511.3125, 0)" class="cluster-label"><foreignObject height="19" width="32.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">Core</span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-tauri-runtime-wry LE-WRY" id="L-tauri-runtime-wry-WRY-0" d="M947.34375,84L947.34375,92.33333333333333C947.34375,100.66666666666667,947.34375,117.33333333333333,947.34375,129.83333333333334C947.34375,142.33333333333334,947.34375,150.66666666666666,947.34375,159C947.34375,167.33333333333334,947.34375,175.66666666666666,947.34375,184C947.34375,192.33333333333334,947.34375,200.66666666666666,947.34375,204.83333333333334L947.34375,209"/><path marker-end="url(#flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-WRY LE-TAO" id="L-WRY-TAO-0" d="M947.34375,243L947.34375,247.16666666666666C947.34375,251.33333333333334,947.34375,259.6666666666667,947.34375,268C947.34375,276.3333333333333,947.34375,284.6666666666667,947.34375,288.8333333333333L947.34375,293"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(947.34375, 226)" id="flowchart-WRY-7" class="node default default flowchart-label"><rect height="34" width="46.28125" y="-17" x="-23.140625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-15.640625, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="31.28125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">WRY</span></div></foreignObject></g></g><g transform="translate(947.34375, 310)" id="flowchart-TAO-9" class="node default default flowchart-label"><rect height="34" width="42.5625" y="-17" x="-21.28125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-13.78125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="27.5625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">TAO</span></div></foreignObject></g></g><g transform="translate(27.5, 17)" class="root"><g class="clusters"><g id="tauri" class="cluster default flowchart-label"><rect height="84" width="479.390625" y="8" x="8" ry="0" rx="0" style=""/><g transform="translate(230.5078125, 8)" class="cluster-label"><foreignObject height="19" width="34.375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri</span></div></foreignObject></g></g></g><g class="edgePaths"/><g class="edgeLabels"/><g class="nodes"><g transform="translate(99.703125, 50)" id="flowchart-tauri-runtime-0" class="node default default flowchart-label"><rect height="34" width="113.40625" y="-17" x="-56.703125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-49.203125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="98.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-runtime</span></div></foreignObject></g></g><g transform="translate(260.609375, 50)" id="flowchart-tauri-macros-1" class="node default default flowchart-label"><rect height="34" width="108.40625" y="-17" x="-54.203125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-46.703125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="93.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-macros</span></div></foreignObject></g></g><g transform="translate(408.6015625, 50)" id="flowchart-tauri-utils-2" class="node default default flowchart-label"><rect height="34" width="87.578125" y="-17" x="-43.7890625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-36.2890625, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="72.578125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-utils</span></div></foreignObject></g></g></g></g><g transform="translate(611.0546875, 67)" id="flowchart-tauri-build-3" class="node default default flowchart-label"><rect height="34" width="93.328125" y="-17" x="-46.6640625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-39.1640625, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="78.328125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-build</span></div></foreignObject></g></g><g transform="translate(766.1875, 67)" id="flowchart-tauri-codegen-4" class="node default default flowchart-label"><rect height="34" width="116.9375" y="-17" x="-58.46875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-50.96875, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="101.9375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-codegen</span></div></foreignObject></g></g><g transform="translate(947.34375, 67)" id="flowchart-tauri-runtime-wry-5" class="node default default flowchart-label"><rect height="34" width="145.375" y="-17" x="-72.6875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-65.1875, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="130.375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">tauri-runtime-wry</span></div></foreignObject></g></g></g></g></g></svg>
