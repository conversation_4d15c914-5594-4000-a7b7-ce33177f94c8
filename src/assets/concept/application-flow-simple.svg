<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="491px" height="434px" viewBox="-0.5 -0.5 491 434" class="ge-export-svg-auto"><defs><style type="text/css">@media (prefers-color-scheme: dark) {&#xa;svg.ge-export-svg-auto { filter: invert(100%) hue-rotate(180deg); }&#xa;svg.ge-export-svg-auto foreignObject img,&#xa;svg.ge-export-svg-auto image:not(svg.ge-export-svg-auto switch image),&#xa;svg.ge-export-svg-auto svg { filter: invert(100%) hue-rotate(180deg) }&#xa;}</style></defs><g><path d="M 245 18 L 245 416" fill="none" stroke="#cccccc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><ellipse cx="245" cy="12" rx="6" ry="6" fill="transparent" stroke="#333333" stroke-width="2" pointer-events="all"/><ellipse cx="245" cy="422" rx="6" ry="6" fill="transparent" stroke="#333333" stroke-width="2" pointer-events="all"/><path d="M 126 7.5 C 125.69 5.13 124.18 3.09 122 2.1 C 108.14 -0.73 98.14 -12.85 98 -27 C 98 -43.57 111.43 -57 128 -57 C 135.96 -57 143.59 -53.84 149.21 -48.21 C 154.84 -42.59 158 -34.96 158 -27 C 157.86 -12.85 147.86 -0.73 134 2.1 C 131.82 3.09 130.31 5.13 130 7.5 L 130 177 C 132.52 177.89 134.15 180.33 134 183 C 134 186.31 131.31 189 128 189 C 124.69 189 122 186.31 122 183 C 121.85 180.33 123.48 177.89 126 177 Z M 128 -51 C 116.77 -51.36 106.8 -43.88 104 -33 C 102.11 -25.68 103.77 -17.9 108.49 -12 C 113.22 -6.09 120.44 -2.76 128 -3 C 135.56 -2.76 142.78 -6.09 147.51 -12 C 152.23 -17.9 153.89 -25.68 152 -33 C 149.2 -43.88 139.23 -51.36 128 -51 Z M 128 178.5 C 125.51 178.5 123.5 180.51 123.5 183 C 123.5 185.49 125.51 187.5 128 187.5 C 130.49 187.5 132.5 185.49 132.5 183 C 132.5 180.51 130.49 178.5 128 178.5 Z" fill="#10739e" stroke="none" transform="rotate(270,128,66)" pointer-events="all"/><path d="M 128 -51 C 116.77 -51.36 106.8 -43.88 104 -33 C 102.11 -25.68 103.77 -17.9 108.49 -12 C 113.22 -6.09 120.44 -2.76 128 -3 C 135.56 -2.76 142.78 -6.09 147.51 -12 C 152.23 -17.9 153.89 -25.68 152 -33 C 149.2 -43.88 139.23 -51.36 128 -51 Z M 128 178.5 C 125.51 178.5 123.5 180.51 123.5 183 C 123.5 185.49 125.51 187.5 128 187.5 C 130.49 187.5 132.5 185.49 132.5 183 C 132.5 180.51 130.49 178.5 128 178.5 Z" fill="rgb(255, 255, 255)" stroke="none" transform="rotate(270,128,66)" pointer-events="all"/><path d="M 360 77.5 C 359.69 75.13 358.18 73.09 356 72.1 C 342.14 69.27 332.14 57.15 332 43 C 332 26.43 345.43 13 362 13 C 369.96 13 377.59 16.16 383.21 21.79 C 388.84 27.41 392 35.04 392 43 C 391.86 57.15 381.86 69.27 368 72.1 C 365.82 73.09 364.31 75.13 364 77.5 L 364 247 C 366.52 247.89 368.15 250.33 368 253 C 368 256.31 365.31 259 362 259 C 358.69 259 356 256.31 356 253 C 355.85 250.33 357.48 247.89 360 247 Z M 362 19 C 350.77 18.64 340.8 26.12 338 37 C 336.11 44.32 337.77 52.1 342.49 58 C 347.22 63.91 354.44 67.24 362 67 C 369.56 67.24 376.78 63.91 381.51 58 C 386.23 52.1 387.89 44.32 386 37 C 383.2 26.12 373.23 18.64 362 19 Z M 362 248.5 C 359.51 248.5 357.5 250.51 357.5 253 C 357.5 255.49 359.51 257.5 362 257.5 C 364.49 257.5 366.5 255.49 366.5 253 C 366.5 250.51 364.49 248.5 362 248.5 Z" fill="#f2931e" stroke="none" transform="translate(0,136)scale(1,-1)translate(0,-136)rotate(-270,362,136)" pointer-events="all"/><path d="M 362 19 C 350.77 18.64 340.8 26.12 338 37 C 336.11 44.32 337.77 52.1 342.49 58 C 347.22 63.91 354.44 67.24 362 67 C 369.56 67.24 376.78 63.91 381.51 58 C 386.23 52.1 387.89 44.32 386 37 C 383.2 26.12 373.23 18.64 362 19 Z M 362 248.5 C 359.51 248.5 357.5 250.51 357.5 253 C 357.5 255.49 359.51 257.5 362 257.5 C 364.49 257.5 366.5 255.49 366.5 253 C 366.5 250.51 364.49 248.5 362 248.5 Z" fill="rgb(255, 255, 255)" stroke="none" transform="translate(0,136)scale(1,-1)translate(0,-136)rotate(-270,362,136)" pointer-events="all"/><path d="M 126 147.5 C 125.69 145.13 124.18 143.09 122 142.1 C 108.14 139.27 98.14 127.15 98 113 C 98 96.43 111.43 83 128 83 C 135.96 83 143.59 86.16 149.21 91.79 C 154.84 97.41 158 105.04 158 113 C 157.86 127.15 147.86 139.27 134 142.1 C 131.82 143.09 130.31 145.13 130 147.5 L 130 317 C 132.52 317.89 134.15 320.33 134 323 C 134 326.31 131.31 329 128 329 C 124.69 329 122 326.31 122 323 C 121.85 320.33 123.48 317.89 126 317 Z M 128 89 C 116.77 88.64 106.8 96.12 104 107 C 102.11 114.32 103.77 122.1 108.49 128 C 113.22 133.91 120.44 137.24 128 137 C 135.56 137.24 142.78 133.91 147.51 128 C 152.23 122.1 153.89 114.32 152 107 C 149.2 96.12 139.23 88.64 128 89 Z M 128 318.5 C 125.51 318.5 123.5 320.51 123.5 323 C 123.5 325.49 125.51 327.5 128 327.5 C 130.49 327.5 132.5 325.49 132.5 323 C 132.5 320.51 130.49 318.5 128 318.5 Z" fill="#ae4132" stroke="none" transform="rotate(270,128,206)" pointer-events="all"/><path d="M 128 89 C 116.77 88.64 106.8 96.12 104 107 C 102.11 114.32 103.77 122.1 108.49 128 C 113.22 133.91 120.44 137.24 128 137 C 135.56 137.24 142.78 133.91 147.51 128 C 152.23 122.1 153.89 114.32 152 107 C 149.2 96.12 139.23 88.64 128 89 Z M 128 318.5 C 125.51 318.5 123.5 320.51 123.5 323 C 123.5 325.49 125.51 327.5 128 327.5 C 130.49 327.5 132.5 325.49 132.5 323 C 132.5 320.51 130.49 318.5 128 318.5 Z" fill="rgb(255, 255, 255)" stroke="none" transform="rotate(270,128,206)" pointer-events="all"/><path d="M 360 222.5 C 359.69 220.13 358.18 218.09 356 217.1 C 342.14 214.27 332.14 202.15 332 188 C 332 171.43 345.43 158 362 158 C 369.96 158 377.59 161.16 383.21 166.79 C 388.84 172.41 392 180.04 392 188 C 391.86 202.15 381.86 214.27 368 217.1 C 365.82 218.09 364.31 220.13 364 222.5 L 364 392 C 366.52 392.89 368.15 395.33 368 398 C 368 401.31 365.31 404 362 404 C 358.69 404 356 401.31 356 398 C 355.85 395.33 357.48 392.89 360 392 Z M 362 164 C 350.77 163.64 340.8 171.12 338 182 C 336.11 189.32 337.77 197.1 342.49 203 C 347.22 208.91 354.44 212.24 362 212 C 369.56 212.24 376.78 208.91 381.51 203 C 386.23 197.1 387.89 189.32 386 182 C 383.2 171.12 373.23 163.64 362 164 Z M 362 393.5 C 359.51 393.5 357.5 395.51 357.5 398 C 357.5 400.49 359.51 402.5 362 402.5 C 364.49 402.5 366.5 400.49 366.5 398 C 366.5 395.51 364.49 393.5 362 393.5 Z" fill="#23445d" stroke="none" transform="translate(0,281)scale(1,-1)translate(0,-281)rotate(-270,362,281)" pointer-events="all"/><path d="M 362 164 C 350.77 163.64 340.8 171.12 338 182 C 336.11 189.32 337.77 197.1 342.49 203 C 347.22 208.91 354.44 212.24 362 212 C 369.56 212.24 376.78 208.91 381.51 203 C 386.23 197.1 387.89 189.32 386 182 C 383.2 171.12 373.23 163.64 362 164 Z M 362 393.5 C 359.51 393.5 357.5 395.51 357.5 398 C 357.5 400.49 359.51 402.5 362 402.5 C 364.49 402.5 366.5 400.49 366.5 398 C 366.5 395.51 364.49 393.5 362 393.5 Z" fill="rgb(255, 255, 255)" stroke="none" transform="translate(0,281)scale(1,-1)translate(0,-281)rotate(-270,362,281)" pointer-events="all"/><path d="M 126 297.5 C 125.69 295.13 124.18 293.09 122 292.1 C 108.14 289.27 98.14 277.15 98 263 C 98 246.43 111.43 233 128 233 C 135.96 233 143.59 236.16 149.21 241.79 C 154.84 247.41 158 255.04 158 263 C 157.86 277.15 147.86 289.27 134 292.1 C 131.82 293.09 130.31 295.13 130 297.5 L 130 467 C 132.52 467.89 134.15 470.33 134 473 C 134 476.31 131.31 479 128 479 C 124.69 479 122 476.31 122 473 C 121.85 470.33 123.48 467.89 126 467 Z M 128 239 C 116.77 238.64 106.8 246.12 104 257 C 102.11 264.32 103.77 272.1 108.49 278 C 113.22 283.91 120.44 287.24 128 287 C 135.56 287.24 142.78 283.91 147.51 278 C 152.23 272.1 153.89 264.32 152 257 C 149.2 246.12 139.23 238.64 128 239 Z M 128 468.5 C 125.51 468.5 123.5 470.51 123.5 473 C 123.5 475.49 125.51 477.5 128 477.5 C 130.49 477.5 132.5 475.49 132.5 473 C 132.5 470.51 130.49 468.5 128 468.5 Z" fill="#12aab5" stroke="none" transform="rotate(270,128,356)" pointer-events="all"/><path d="M 128 239 C 116.77 238.64 106.8 246.12 104 257 C 102.11 264.32 103.77 272.1 108.49 278 C 113.22 283.91 120.44 287.24 128 287 C 135.56 287.24 142.78 283.91 147.51 278 C 152.23 272.1 153.89 264.32 152 257 C 149.2 246.12 139.23 238.64 128 239 Z M 128 468.5 C 125.51 468.5 123.5 470.51 123.5 473 C 123.5 475.49 125.51 477.5 128 477.5 C 130.49 477.5 132.5 475.49 132.5 473 C 132.5 470.51 130.49 468.5 128 468.5 Z" fill="rgb(255, 255, 255)" stroke="none" transform="rotate(270,128,356)" pointer-events="all"/><rect x="75" y="26" width="160" height="30" rx="4.5" ry="4.5" fill="#10739e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 41px; margin-left: 76px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #FFFFFF; "><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Upstream</div></div></div></foreignObject><text x="155" y="46" fill="#FFFFFF" font-family="Helvetica" font-size="17px" text-anchor="middle" font-weight="bold">Upstream</text></switch></g><rect x="255" y="91" width="160" height="30" rx="4.5" ry="4.5" fill="#f2931e" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 106px; margin-left: 256px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #FFFFFF; "><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Development</div></div></div></foreignObject><text x="335" y="111" fill="#FFFFFF" font-family="Helvetica" font-size="17px" text-anchor="middle" font-weight="bold">Development</text></switch></g><rect x="75" y="166" width="160" height="30" rx="4.5" ry="4.5" fill="#ae4132" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 181px; margin-left: 76px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #FFFFFF; "><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Building</div></div></div></foreignObject><text x="155" y="186" fill="#FFFFFF" font-family="Helvetica" font-size="17px" text-anchor="middle" font-weight="bold">Building</text></switch></g><rect x="255" y="236" width="160" height="30" rx="4.5" ry="4.5" fill="#23445d" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 251px; margin-left: 256px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #FFFFFF; "><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Distribution</div></div></div></foreignObject><text x="335" y="256" fill="#FFFFFF" font-family="Helvetica" font-size="17px" text-anchor="middle" font-weight="bold">Distribution</text></switch></g><rect x="75" y="316" width="160" height="30" rx="4.5" ry="4.5" fill="#12aab5" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 331px; margin-left: 76px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #FFFFFF; "><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Runtime</div></div></div></foreignObject><text x="155" y="336" fill="#FFFFFF" font-family="Helvetica" font-size="17px" text-anchor="middle" font-weight="bold">Runtime</text></switch></g></g></svg>
