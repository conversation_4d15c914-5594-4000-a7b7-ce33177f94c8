<svg xmlns="http://www.w3.org/2000/svg" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 500.234375 200.40625" height="200.40625" width="500.234375" id="container"><style>#container{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#container .error-icon{fill:#552222;}#container .error-text{fill:#552222;stroke:#552222;}#container .edge-thickness-normal{stroke-width:2px;}#container .edge-thickness-thick{stroke-width:3.5px;}#container .edge-pattern-solid{stroke-dasharray:0;}#container .edge-pattern-dashed{stroke-dasharray:3;}#container .edge-pattern-dotted{stroke-dasharray:2;}#container .marker{fill:#333333;stroke:#333333;}#container .marker.cross{stroke:#333333;}#container svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#container .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#container .cluster-label text{fill:#333;}#container .cluster-label span,#container p{color:#333;}#container .label text,#container span,#container p{fill:#333;color:#333;}#container .node rect,#container .node circle,#container .node ellipse,#container .node polygon,#container .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#container .flowchart-label text{text-anchor:middle;}#container .node .label{text-align:center;}#container .node.clickable{cursor:pointer;}#container .arrowheadPath{fill:#333333;}#container .edgePath .path{stroke:#333333;stroke-width:2.0px;}#container .flowchart-link{stroke:#333333;fill:none;}#container .edgeLabel{background-color:#e8e8e8;text-align:center;}#container .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#container .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#container .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#container .cluster text{fill:#333;}#container .cluster span,#container p{color:#333;}#container div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#container .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#container :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="10" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#flowchart-pointEnd)" marker-start="url(#flowchart-pointStart)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-W1" id="L-C-W1-0" d="M214.49621668272317,53.78527918272319L191.08669098560264,64.13877431893599C167.67716528848212,74.49226945514879,120.85811389424106,95.19925972757439,97.44858819712051,111.3027548637872C74.0390625,127.40625,74.0390625,138.90625,74.0390625,144.65625L74.0390625,150.40625"/><path marker-end="url(#flowchart-pointEnd)" marker-start="url(#flowchart-pointStart)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-W2" id="L-C-W2-0" d="M242.6171875,81.90625L242.53385416666666,87.57291666666667C242.45052083333334,93.23958333333333,242.28385416666666,104.57291666666667,242.20052083333334,115.98958333333333C242.1171875,127.40625,242.1171875,138.90625,242.1171875,144.65625L242.1171875,150.40625"/><path marker-end="url(#flowchart-pointEnd)" marker-start="url(#flowchart-pointStart)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-W3" id="L-C-W3-0" d="M270.7381583172768,53.78527918272319L293.9810173477307,64.13877431893599C317.22387637818457,74.49226945514879,363.7095944390923,95.19925972757439,386.9524534695461,111.3027548637872C410.1953125,127.40625,410.1953125,138.90625,410.1953125,144.65625L410.1953125,150.40625"/></g><g class="edgeLabels"><g transform="translate(74.0390625, 115.90625)" class="edgeLabel"><g transform="translate(-74.0390625, -9.5)" class="label"><foreignObject height="19" width="148.078125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">Events &amp; Commands</span></div></foreignObject></g></g><g transform="translate(242.1171875, 115.90625)" class="edgeLabel"><g transform="translate(-74.0390625, -9.5)" class="label"><foreignObject height="19" width="148.078125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">Events &amp; Commands</span></div></foreignObject></g></g><g transform="translate(410.1953125, 115.90625)" class="edgeLabel"><g transform="translate(-74.0390625, -9.5)" class="label"><foreignObject height="19" width="148.078125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">Events &amp; Commands</span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(242.1171875, 40.703125)" id="flowchart-C-0" class="node default default flowchart-label"><polygon style="" transform="translate(-40.703125,40.703125)" class="label-container" points="40.703125,0 81.40625,-40.703125 40.703125,-81.40625 0,-40.703125"/><g transform="translate(-16.203125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="32.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">Core</span></div></foreignObject></g></g><g transform="translate(74.0390625, 167.40625)" id="flowchart-W1-1" class="node default default flowchart-label"><rect height="34" width="79.890625" y="-17" x="-39.9453125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.4453125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="64.890625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">WebView</span></div></foreignObject></g></g><g transform="translate(242.1171875, 167.40625)" id="flowchart-W2-2" class="node default default flowchart-label"><rect height="34" width="79.890625" y="-17" x="-39.9453125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.4453125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="64.890625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">WebView</span></div></foreignObject></g></g><g transform="translate(410.1953125, 167.40625)" id="flowchart-W3-3" class="node default default flowchart-label"><rect height="34" width="79.890625" y="-17" x="-39.9453125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.4453125, -9.5)" style="" class="label"><rect/><foreignObject height="19" width="64.890625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="nodeLabel">WebView</span></div></foreignObject></g></g></g></g></g></svg>
