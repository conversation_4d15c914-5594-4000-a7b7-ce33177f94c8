
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Container, AppBar, Toolbar, Typography, Box, IconButton, Tooltip } from '@mui/material';
import { Speed as SpeedIcon } from '@mui/icons-material';
import { theme } from './theme';
import { ScreenshotCapture } from './components/ScreenshotCapture';
import { RecentScreenshots } from './components/RecentScreenshots';
import { PerformanceMonitor } from './components/PerformanceMonitor';

import { useState } from 'react';

function App() {
  const [performanceMonitorOpen, setPerformanceMonitorOpen] = useState(false);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" elevation={1}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Mecap - Screenshot Tool
            </Typography>
            <Tooltip title="性能监控">
              <IconButton
                color="inherit"
                onClick={() => setPerformanceMonitorOpen(true)}
              >
                <SpeedIcon />
              </IconButton>
            </Tooltip>
          </Toolbar>
        </AppBar>

        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            <ScreenshotCapture />
            <RecentScreenshots />
          </Box>
        </Container>

        {/* 性能监控对话框 */}
        <PerformanceMonitor
          open={performanceMonitorOpen}
          onClose={() => setPerformanceMonitorOpen(false)}
        />
      </Box>
    </ThemeProvider>
  );
}

export default App;
