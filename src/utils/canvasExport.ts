import { Stage } from 'konva/lib/Stage';
import { CropArea } from '../types/editor';

/**
 * Export the canvas content as a data URL
 */
export const exportCanvasAsDataURL = (
  stage: Stage,
  cropArea: CropArea | null = null,
  format: 'image/png' | 'image/jpeg' = 'image/png',
  quality: number = 1.0
): string => {
  if (!stage) {
    throw new Error('Stage is required for export');
  }

  let exportOptions: any = {
    mimeType: format,
    quality: quality
  };

  // If crop area is specified, export only that region
  if (cropArea) {
    exportOptions = {
      ...exportOptions,
      x: cropArea.x,
      y: cropArea.y,
      width: cropArea.width,
      height: cropArea.height
    };
  }

  return stage.toDataURL(exportOptions);
};

/**
 * Export canvas with applied crop area
 */
export const exportCroppedCanvas = (
  stage: Stage,
  cropArea: CropArea,
  format: 'image/png' | 'image/jpeg' = 'image/png'
): string => {
  return exportCanvasAsDataURL(stage, cropArea, format);
};

/**
 * Export full canvas content
 */
export const exportFullCanvas = (
  stage: Stage,
  format: 'image/png' | 'image/jpeg' = 'image/png'
): string => {
  return exportCanvasAsDataURL(stage, null, format);
};

/**
 * Get canvas dimensions
 */
export const getCanvasDimensions = (stage: Stage) => {
  return {
    width: stage.width(),
    height: stage.height()
  };
};

/**
 * Validate export parameters
 */
export const validateExportParams = (
  stage: Stage | null,
  cropArea: CropArea | null = null
): { isValid: boolean; error?: string } => {
  if (!stage) {
    return { isValid: false, error: 'Stage is required' };
  }

  if (cropArea) {
    const stageWidth = stage.width();
    const stageHeight = stage.height();

    if (cropArea.x < 0 || cropArea.y < 0) {
      return { isValid: false, error: 'Crop area coordinates must be positive' };
    }

    if (cropArea.x + cropArea.width > stageWidth || cropArea.y + cropArea.height > stageHeight) {
      return { isValid: false, error: 'Crop area exceeds canvas bounds' };
    }

    if (cropArea.width <= 0 || cropArea.height <= 0) {
      return { isValid: false, error: 'Crop area must have positive dimensions' };
    }
  }

  return { isValid: true };
};
