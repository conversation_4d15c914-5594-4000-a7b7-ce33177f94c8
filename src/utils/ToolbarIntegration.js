/**
 * Toolbar Integration Module
 * Integrates the unified ToolbarPositionManager with existing toolbar systems
 * in Mecap's screenshot editing modes
 */

import { ToolbarPositionManager } from './ToolbarPositionManager.js';

// Create a global instance
const toolbarPositionManager = new ToolbarPositionManager();

/**
 * Enhanced Quick Action Toolbar with unified positioning
 */
export class EnhancedQuickActionToolbar {
    constructor(options = {}) {
        this.options = {
            showEditTools: true,
            showCaptureTools: true,
            showSaveOptions: true,
            draggable: true,
            autoHide: false,
            ...options
        };

        this.element = null;
        this.isVisible = false;
        this.currentRegion = null;
    }

    /**
     * Create and initialize the toolbar
     * @param {Object} region - Initial region to attach to
     */
    create(region = null) {
        if (this.element) {
            this.destroy();
        }

        this.element = this.createToolbarElement();
        document.body.appendChild(this.element);

        // Register with position manager
        toolbarPositionManager.registerToolbar(this.element, {
            mode: 'auto',
            edge: 'bottom',
            draggable: this.options.draggable
        });

        if (region) {
            this.attachToRegion(region);
        }

        console.log('[EnhancedToolbar] Created and registered');
        return this.element;
    }

    /**
     * Create the toolbar DOM element
     * @returns {HTMLElement} Toolbar element
     */
    createToolbarElement() {
        const toolbar = document.createElement('div');
        toolbar.className = 'enhanced-quick-toolbar';
        toolbar.style.cssText = `
            display: flex;
            flex-direction: row;
            gap: 8px;
            padding: 12px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 10px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 2px 8px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            opacity: 0;
            transform: scale(0.8) translateY(10px);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            z-index: 10000;
            position: fixed;
            pointer-events: auto;
        `;

        // Create toolbar buttons based on options
        this.createToolbarButtons(toolbar);

        return toolbar;
    }

    /**
     * Create toolbar buttons
     * @param {HTMLElement} toolbar - Toolbar container
     */
    createToolbarButtons(toolbar) {
        const buttonGroups = [];

        // Capture tools group
        if (this.options.showCaptureTools) {
            buttonGroups.push([
                { icon: '📐', title: 'Rectangle Selection', action: 'rectangle_select', group: 'capture' },
                { icon: '🪟', title: 'Window Capture', action: 'window_capture', group: 'capture' }
            ]);
        }

        // Edit tools group
        if (this.options.showEditTools) {
            buttonGroups.push([
                { icon: '📝', title: 'Add Text', action: 'add_text', group: 'edit' },
                { icon: '🖌️', title: 'Draw', action: 'draw', group: 'edit' },
                { icon: '➡️', title: 'Arrow', action: 'arrow', group: 'edit' },
                { icon: '⭕', title: 'Circle', action: 'circle', group: 'edit' }
            ]);
        }

        // Save tools group
        if (this.options.showSaveOptions) {
            buttonGroups.push([
                { icon: '💾', title: 'Save', action: 'save', group: 'save' },
                { icon: '📋', title: 'Copy', action: 'copy', group: 'save' },
                { icon: '❌', title: 'Cancel', action: 'cancel', group: 'save' }
            ]);
        }

        // Create button groups with separators
        buttonGroups.forEach((group, groupIndex) => {
            if (groupIndex > 0) {
                // Add separator
                const separator = document.createElement('div');
                separator.style.cssText = `
                    width: 1px;
                    height: 24px;
                    background: rgba(255, 255, 255, 0.2);
                    margin: 0 4px;
                `;
                toolbar.appendChild(separator);
            }

            // Create buttons in group
            group.forEach(buttonDef => {
                const button = this.createButton(buttonDef);
                toolbar.appendChild(button);
            });
        });
    }

    /**
     * Create individual toolbar button
     * @param {Object} buttonDef - Button definition
     * @returns {HTMLElement} Button element
     */
    createButton(buttonDef) {
        const button = document.createElement('button');
        button.className = `toolbar-btn toolbar-btn-${buttonDef.group}`;
        button.style.cssText = `
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            position: relative;
        `;

        button.innerHTML = buttonDef.icon;
        button.title = buttonDef.title;
        button.dataset.action = buttonDef.action;

        // Add hover effects
        button.addEventListener('mouseenter', () => {
            button.style.background = 'rgba(255, 107, 53, 0.3)';
            button.style.transform = 'scale(1.1)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.background = 'rgba(255, 255, 255, 0.1)';
            button.style.transform = 'scale(1)';
        });

        // Add click handler
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleButtonClick(buttonDef.action, button);
        });

        return button;
    }

    /**
     * Handle button click events
     * @param {string} action - Action identifier
     * @param {HTMLElement} button - Button element
     */
    handleButtonClick(action, button) {
        console.log('[EnhancedToolbar] Button clicked:', action);

        // Add click animation
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 100);

        // Emit custom event for action handling
        const event = new CustomEvent('toolbar-action', {
            detail: {
                action,
                toolbar: this,
                region: this.currentRegion
            }
        });
        document.dispatchEvent(event);

        // Handle built-in actions
        switch (action) {
            case 'cancel':
                this.hide();
                break;
            case 'save':
                this.handleSave();
                break;
            case 'copy':
                this.handleCopy();
                break;
        }
    }

    /**
     * Attach toolbar to a specific region with immediate display
     * @param {Object} region - Region coordinates {x, y, width, height}
     * @param {boolean} immediate - Whether to show immediately without delay
     */
    attachToRegion(region, immediate = false) {
        this.currentRegion = region;

        // Update position immediately for responsive feel
        toolbarPositionManager.updateRegion(region);

        if (!this.isVisible) {
            if (immediate) {
                // Show immediately for editing mode activation
                this.showImmediate();
            } else {
                // Normal animated show
                this.show();
            }
        }
    }

    /**
     * Show toolbar immediately without animation delays
     */
    showImmediate() {
        if (!this.element || this.isVisible) return;

        this.isVisible = true;
        this.element.style.display = 'flex';

        // Apply final state immediately
        this.element.style.opacity = '1';
        this.element.style.transform = 'scale(1) translateY(0)';

        // Still animate buttons for visual appeal but faster
        const buttons = this.element.querySelectorAll('.toolbar-btn');
        buttons.forEach((button, index) => {
            button.style.opacity = '1';
            button.style.transform = 'scale(1) translateY(0)';
            button.style.transition = 'all 0.1s ease';
        });

        console.log('[EnhancedToolbar] Shown immediately');
    }

    /**
     * Update toolbar position when region changes
     * @param {Object} region - Updated region coordinates
     */
    updateRegion(region) {
        this.currentRegion = region;
        toolbarPositionManager.updateRegion(region);
    }

    /**
     * Show the toolbar with enhanced animation
     */
    show() {
        if (!this.element || this.isVisible) return;

        this.isVisible = true;
        this.element.style.display = 'flex';

        // Force reflow to ensure initial state is applied
        this.element.offsetHeight;

        // Trigger staggered animation for smooth appearance
        requestAnimationFrame(() => {
            this.element.style.opacity = '1';
            this.element.style.transform = 'scale(1) translateY(0)';

            // Animate buttons with stagger effect
            this.animateButtonsIn();
        });

        console.log('[EnhancedToolbar] Shown with enhanced animation');
    }

    /**
     * Animate buttons with stagger effect
     */
    animateButtonsIn() {
        const buttons = this.element.querySelectorAll('.toolbar-btn');
        buttons.forEach((button, index) => {
            button.style.opacity = '0';
            button.style.transform = 'scale(0.8) translateY(5px)';
            button.style.transition = 'all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1)';

            setTimeout(() => {
                button.style.opacity = '1';
                button.style.transform = 'scale(1) translateY(0)';
            }, 50 + index * 30); // Stagger by 30ms per button
        });
    }

    /**
     * Hide the toolbar with enhanced animation
     */
    hide() {
        if (!this.element || !this.isVisible) return;

        this.isVisible = false;

        // Animate buttons out first
        this.animateButtonsOut(() => {
            // Then animate toolbar container
            this.element.style.opacity = '0';
            this.element.style.transform = 'scale(0.8) translateY(10px)';

            setTimeout(() => {
                if (this.element) {
                    this.element.style.display = 'none';
                }
            }, 300);
        });

        console.log('[EnhancedToolbar] Hidden with enhanced animation');
    }

    /**
     * Animate buttons out with stagger effect
     * @param {Function} callback - Callback when animation completes
     */
    animateButtonsOut(callback) {
        const buttons = this.element.querySelectorAll('.toolbar-btn');
        let completed = 0;

        buttons.forEach((button, index) => {
            setTimeout(() => {
                button.style.opacity = '0';
                button.style.transform = 'scale(0.8) translateY(-5px)';

                completed++;
                if (completed === buttons.length && callback) {
                    setTimeout(callback, 100);
                }
            }, index * 20); // Reverse stagger
        });

        // Fallback if no buttons
        if (buttons.length === 0 && callback) {
            callback();
        }
    }

    /**
     * Handle save action
     */
    handleSave() {
        console.log('[EnhancedToolbar] Save action triggered');
        // Implementation will be added based on existing save logic
    }

    /**
     * Handle copy action
     */
    handleCopy() {
        console.log('[EnhancedToolbar] Copy action triggered');
        // Implementation will be added based on existing copy logic
    }

    /**
     * Destroy the toolbar and cleanup
     */
    destroy() {
        if (this.element) {
            this.element.remove();
            this.element = null;
        }
        this.isVisible = false;
        this.currentRegion = null;
        console.log('[EnhancedToolbar] Destroyed');
    }
}

/**
 * Integration helper for existing overlay systems
 */
export class ToolbarIntegrationHelper {
    constructor() {
        this.activeToolbar = null;
        this.regionObserver = null;
    }

    /**
     * Initialize integration with existing overlay systems
     */
    init() {
        this.setupEventListeners();
        this.observeRegionChanges();
        console.log('[ToolbarIntegration] Initialized');
    }

    /**
     * Setup event listeners for integration
     */
    setupEventListeners() {
        // Listen for window selection events
        document.addEventListener('window-selected', (event) => {
            this.handleWindowSelected(event.detail);
        });

        // Listen for region selection events
        document.addEventListener('region-selected', (event) => {
            this.handleRegionSelected(event.detail);
        });

        // Listen for editing mode changes
        document.addEventListener('editing-mode-changed', (event) => {
            this.handleEditingModeChanged(event.detail);
        });
    }

    /**
     * Handle window selection event
     * @param {Object} windowData - Selected window data
     */
    handleWindowSelected(windowData) {
        const region = {
            x: windowData.x,
            y: windowData.y,
            width: windowData.width,
            height: windowData.height
        };

        this.createOrUpdateToolbar(region);
    }

    /**
     * Handle region selection event
     * @param {Object} regionData - Selected region data
     */
    handleRegionSelected(regionData) {
        this.createOrUpdateToolbar(regionData);
    }

    /**
     * Handle editing mode changes
     * @param {Object} modeData - Mode change data
     */
    handleEditingModeChanged(modeData) {
        if (modeData.mode === 'editing' && modeData.region) {
            // Use immediate display for editing mode activation
            this.createOrUpdateToolbar(modeData.region, true);
        } else if (modeData.mode === 'detecting') {
            this.hideToolbar();
        }
    }

    /**
     * Create or update the active toolbar
     * @param {Object} region - Region to attach toolbar to
     * @param {boolean} immediate - Whether to show immediately
     */
    createOrUpdateToolbar(region, immediate = false) {
        if (!this.activeToolbar) {
            this.activeToolbar = new EnhancedQuickActionToolbar({
                draggable: true,
                showEditTools: true,
                showCaptureTools: false, // Hide capture tools in editing mode
                showSaveOptions: true
            });
            this.activeToolbar.create(region);

            if (immediate) {
                // Force immediate display for editing mode
                this.activeToolbar.showImmediate();
            }
        } else {
            this.activeToolbar.updateRegion(region);

            if (immediate && !this.activeToolbar.isVisible) {
                this.activeToolbar.showImmediate();
            }
        }
    }

    /**
     * Hide the active toolbar
     */
    hideToolbar() {
        if (this.activeToolbar) {
            this.activeToolbar.hide();
        }
    }

    /**
     * Observe region changes from various sources
     */
    observeRegionChanges() {
        // This will be implemented to observe DOM changes and region updates
        // For now, we rely on event-based updates
    }

    /**
     * Cleanup integration
     */
    destroy() {
        if (this.activeToolbar) {
            this.activeToolbar.destroy();
            this.activeToolbar = null;
        }

        if (this.regionObserver) {
            this.regionObserver.disconnect();
        }

        console.log('[ToolbarIntegration] Destroyed');
    }
}

// Export singleton instance
export const toolbarIntegration = new ToolbarIntegrationHelper();
