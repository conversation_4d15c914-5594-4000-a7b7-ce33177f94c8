/**
 * Comprehensive Test Suite for Toolbar Positioning System
 * Tests toolbar behavior across different scenarios and configurations
 */

import { ToolbarPositionManager } from './ToolbarPositionManager.js';
import { EnhancedQuickActionToolbar } from './ToolbarIntegration.js';

export class ToolbarPositionTest {
    constructor() {
        this.testResults = [];
        this.testContainer = null;
        this.mockToolbar = null;
        this.positionManager = null;
    }

    /**
     * Run all toolbar positioning tests
     */
    async runAllTests() {
        console.log('[ToolbarTest] Starting comprehensive toolbar positioning tests...');
        
        this.setupTestEnvironment();
        
        // Core positioning tests
        await this.testBasicPositioning();
        await this.testBoundaryConstraints();
        await this.testMultiMonitorSupport();
        await this.testSnapToEdge();
        await this.testDragAndDrop();
        await this.testPositionPersistence();
        await this.testAnimationTiming();
        await this.testResponsiveUpdates();
        
        this.cleanupTestEnvironment();
        
        return this.generateTestReport();
    }

    /**
     * Setup test environment
     */
    setupTestEnvironment() {
        // Create test container
        this.testContainer = document.createElement('div');
        this.testContainer.id = 'toolbar-test-container';
        this.testContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.1);
            z-index: 9999;
            pointer-events: none;
        `;
        document.body.appendChild(this.testContainer);

        // Create mock toolbar
        this.mockToolbar = document.createElement('div');
        this.mockToolbar.id = 'mock-toolbar';
        this.mockToolbar.style.cssText = `
            width: 200px;
            height: 50px;
            background: rgba(255, 0, 0, 0.5);
            border: 2px solid red;
            position: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        `;
        this.mockToolbar.textContent = 'TEST TOOLBAR';
        this.testContainer.appendChild(this.mockToolbar);

        // Initialize position manager
        this.positionManager = new ToolbarPositionManager({
            animationDuration: 100, // Faster for testing
            snapThreshold: 30,
            minDistanceFromEdge: 10
        });

        console.log('[ToolbarTest] Test environment setup complete');
    }

    /**
     * Test basic positioning functionality
     */
    async testBasicPositioning() {
        const testName = 'Basic Positioning';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Register toolbar
            this.positionManager.registerToolbar(this.mockToolbar, {
                mode: 'auto',
                edge: 'bottom',
                draggable: false
            });

            // Test different region positions
            const testRegions = [
                { x: 100, y: 100, width: 300, height: 200 }, // Top-left
                { x: 500, y: 300, width: 400, height: 250 }, // Center
                { x: 800, y: 500, width: 200, height: 150 }  // Bottom-right
            ];

            let passed = true;
            for (const region of testRegions) {
                this.positionManager.updateRegion(region);
                await this.wait(150);

                const toolbarRect = this.mockToolbar.getBoundingClientRect();
                
                // Verify toolbar is positioned relative to region
                const isPositioned = toolbarRect.left >= 0 && 
                                   toolbarRect.top >= 0 && 
                                   toolbarRect.right <= window.innerWidth &&
                                   toolbarRect.bottom <= window.innerHeight;

                if (!isPositioned) {
                    passed = false;
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'All regions positioned correctly' : 'Positioning failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test boundary constraints
     */
    async testBoundaryConstraints() {
        const testName = 'Boundary Constraints';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Test edge cases
            const edgeRegions = [
                { x: -50, y: -50, width: 100, height: 100 },     // Off-screen top-left
                { x: window.innerWidth - 50, y: window.innerHeight - 50, width: 100, height: 100 }, // Off-screen bottom-right
                { x: 10, y: 10, width: 50, height: 50 },         // Very small region
                { x: 100, y: window.innerHeight - 100, width: 300, height: 150 } // Near bottom edge
            ];

            let passed = true;
            for (const region of edgeRegions) {
                this.positionManager.updateRegion(region);
                await this.wait(150);

                const toolbarRect = this.mockToolbar.getBoundingClientRect();
                
                // Verify toolbar stays within viewport
                const withinBounds = toolbarRect.left >= 0 && 
                                   toolbarRect.top >= 0 && 
                                   toolbarRect.right <= window.innerWidth &&
                                   toolbarRect.bottom <= window.innerHeight;

                if (!withinBounds) {
                    passed = false;
                    console.log(`[ToolbarTest] Boundary violation for region:`, region);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'All boundary constraints respected' : 'Boundary violations detected');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test multi-monitor coordinate handling
     */
    async testMultiMonitorSupport() {
        const testName = 'Multi-Monitor Support';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Simulate multi-monitor coordinates (negative values)
            const multiMonitorRegions = [
                { x: -500, y: 100, width: 300, height: 200 },    // Left monitor
                { x: 1920, y: 100, width: 300, height: 200 },    // Right monitor
                { x: 0, y: -300, width: 300, height: 200 }       // Top monitor
            ];

            let passed = true;
            for (const region of multiMonitorRegions) {
                this.positionManager.updateRegion(region);
                await this.wait(150);

                // For multi-monitor, we mainly test that no errors occur
                // and the toolbar position is calculated without throwing
                const toolbarRect = this.mockToolbar.getBoundingClientRect();
                
                // Basic sanity check - toolbar should have valid dimensions
                if (toolbarRect.width <= 0 || toolbarRect.height <= 0) {
                    passed = false;
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'Multi-monitor coordinates handled' : 'Multi-monitor handling failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test snap-to-edge functionality
     */
    async testSnapToEdge() {
        const testName = 'Snap to Edge';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Enable dragging for snap testing
            this.positionManager.registerToolbar(this.mockToolbar, {
                mode: 'manual',
                draggable: true
            });

            const region = { x: 300, y: 300, width: 400, height: 300 };
            this.positionManager.updateRegion(region);

            // Test snap positions near region edges
            const snapTests = [
                { x: 480, y: 620, expectedSnap: 'bottom' },  // Near bottom edge
                { x: 480, y: 280, expectedSnap: 'top' },     // Near top edge
                { x: 720, y: 440, expectedSnap: 'right' },   // Near right edge
                { x: 280, y: 440, expectedSnap: 'left' }     // Near left edge
            ];

            let passed = true;
            for (const test of snapTests) {
                const snapResult = this.positionManager.calculateSnapPosition(test.x, test.y);
                
                // Verify snap occurred (position changed)
                const snapped = snapResult.x !== test.x || snapResult.y !== test.y;
                if (!snapped) {
                    passed = false;
                    console.log(`[ToolbarTest] Snap failed for position:`, test);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'Snap-to-edge working correctly' : 'Snap functionality failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test drag and drop functionality
     */
    async testDragAndDrop() {
        const testName = 'Drag and Drop';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Simulate drag events
            const region = { x: 200, y: 200, width: 300, height: 200 };
            this.positionManager.updateRegion(region);

            // Simulate mousedown
            const mouseDownEvent = new MouseEvent('mousedown', {
                clientX: 250,
                clientY: 250,
                button: 0
            });
            this.positionManager.handleDragStart(mouseDownEvent);

            // Simulate drag
            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientX: 350,
                clientY: 350
            });
            this.positionManager.handleDrag(mouseMoveEvent);

            // Simulate mouseup
            const mouseUpEvent = new MouseEvent('mouseup', {
                clientX: 350,
                clientY: 350
            });
            this.positionManager.handleDragEnd(mouseUpEvent);

            // Verify position was updated
            const hasUserPosition = this.positionManager.userPosition !== null;
            const isManualMode = this.positionManager.positionMode === 'manual';

            const passed = hasUserPosition && isManualMode;
            this.recordTest(testName, passed, passed ? 'Drag and drop working' : 'Drag and drop failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test position persistence
     */
    async testPositionPersistence() {
        const testName = 'Position Persistence';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            // Clear any existing stored position
            localStorage.removeItem('mecap-toolbar-position');

            // Set a manual position
            this.positionManager.positionMode = 'manual';
            this.positionManager.userPosition = { offsetX: 50, offsetY: 50 };
            
            // Save position
            this.positionManager.savePositionToStorage();

            // Create new manager and load position
            const newManager = new ToolbarPositionManager({ persistPosition: true });
            const loadedPosition = newManager.loadPositionFromStorage();

            const passed = loadedPosition !== null && 
                          loadedPosition.userPosition.offsetX === 50 &&
                          loadedPosition.userPosition.offsetY === 50;

            this.recordTest(testName, passed, passed ? 'Position persistence working' : 'Position persistence failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test animation timing
     */
    async testAnimationTiming() {
        const testName = 'Animation Timing';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            const region = { x: 100, y: 100, width: 300, height: 200 };
            
            const startTime = performance.now();
            this.positionManager.updateRegion(region);
            
            // Wait for animation to complete
            await this.wait(200);
            
            const endTime = performance.now();
            const duration = endTime - startTime;

            // Animation should complete within reasonable time
            const passed = duration >= 100 && duration <= 300;
            this.recordTest(testName, passed, `Animation completed in ${duration.toFixed(1)}ms`);
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test responsive updates during region changes
     */
    async testResponsiveUpdates() {
        const testName = 'Responsive Updates';
        console.log(`[ToolbarTest] Running ${testName}...`);

        try {
            let updateCount = 0;
            const originalUpdate = this.positionManager.updateRegion.bind(this.positionManager);
            
            // Override updateRegion to count calls
            this.positionManager.updateRegion = function(region) {
                updateCount++;
                return originalUpdate(region);
            };

            // Rapidly change regions
            const regions = [
                { x: 100, y: 100, width: 200, height: 150 },
                { x: 200, y: 150, width: 250, height: 180 },
                { x: 300, y: 200, width: 300, height: 200 },
                { x: 400, y: 250, width: 350, height: 220 }
            ];

            for (const region of regions) {
                this.positionManager.updateRegion(region);
                await this.wait(50); // Rapid updates
            }

            // Restore original method
            this.positionManager.updateRegion = originalUpdate;

            const passed = updateCount === regions.length;
            this.recordTest(testName, passed, `${updateCount}/${regions.length} updates processed`);
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Cleanup test environment
     */
    cleanupTestEnvironment() {
        if (this.testContainer) {
            this.testContainer.remove();
        }
        
        if (this.positionManager) {
            this.positionManager.destroy();
        }

        // Clear test data from localStorage
        localStorage.removeItem('mecap-toolbar-position');
        
        console.log('[ToolbarTest] Test environment cleaned up');
    }

    /**
     * Record test result
     */
    recordTest(name, passed, details) {
        const result = {
            name,
            passed,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        console.log(`[ToolbarTest] ${name}: ${passed ? 'PASS' : 'FAIL'} - ${details}`);
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        const report = {
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                successRate: ((passedTests / totalTests) * 100).toFixed(1) + '%'
            },
            results: this.testResults,
            timestamp: new Date().toISOString()
        };

        console.log('[ToolbarTest] Test Report:', report);
        return report;
    }

    /**
     * Utility function to wait for specified duration
     */
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export test runner function
export async function runToolbarPositionTests() {
    const tester = new ToolbarPositionTest();
    return await tester.runAllTests();
}
