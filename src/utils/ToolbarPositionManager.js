/**
 * Unified Toolbar Position Manager
 * Handles intelligent positioning and synchronization of quick action toolbars
 * across all screenshot editing modes in Mecap
 */

export class ToolbarPositionManager {
    constructor(options = {}) {
        this.options = {
            defaultOffset: { x: 10, y: 10 },
            animationDuration: 200,
            snapThreshold: 20,
            minDistanceFromEdge: 10,
            updateThrottle: 16, // ~60fps
            snapToEdges: true,
            persistPosition: true,
            snapAnimationDuration: 150,
            ...options
        };

        // State management
        this.activeRegion = null;
        this.toolbarElement = null;
        this.positionMode = 'auto'; // 'auto', 'manual', 'attached'
        this.attachmentEdge = 'bottom'; // 'top', 'bottom', 'left', 'right'
        this.userPosition = null;
        this.isDragging = false;
        this.lastUpdateTime = 0;
        this.snapZones = [];
        this.isSnapping = false;

        // Animation state
        this.animationFrame = null;
        this.currentAnimation = null;

        // Event handlers
        this.boundHandlers = {
            onRegionChange: this.handleRegionChange.bind(this),
            onWindowResize: this.handleWindowResize.bind(this),
            onDragStart: this.handleDragStart.bind(this),
            onDrag: this.handleDrag.bind(this),
            onDragEnd: this.handleDragEnd.bind(this)
        };

        this.init();
    }

    init() {
        // Set up global event listeners
        window.addEventListener('resize', this.boundHandlers.onWindowResize);
        
        // Set up region change observer
        this.setupRegionObserver();
    }

    /**
     * Register a toolbar element for position management
     * @param {HTMLElement} toolbarElement - The toolbar DOM element
     * @param {Object} config - Configuration options
     */
    registerToolbar(toolbarElement, config = {}) {
        this.toolbarElement = toolbarElement;
        this.positionMode = config.mode || 'auto';
        this.attachmentEdge = config.edge || 'bottom';

        // Add drag functionality if enabled
        if (config.draggable !== false) {
            this.enableDragging();
        }

        // Apply initial styling
        this.applyToolbarStyles();

        // Apply stored position if available
        this.applyStoredPosition();

        // Use Tauri log plugin if available, fallback to console
        if (window.log && window.log.info) {
            window.log.info(`[ToolbarManager] Toolbar registered: ${toolbarElement.id}`);
        } else {
            console.log('[ToolbarManager] Toolbar registered:', toolbarElement.id);
        }
    }

    /**
     * Update the active region that the toolbar should follow
     * @param {Object} region - Region coordinates {x, y, width, height}
     */
    updateRegion(region) {
        if (!region || !this.toolbarElement) return;

        const now = performance.now();
        if (now - this.lastUpdateTime < this.options.updateThrottle) return;
        
        this.lastUpdateTime = now;
        this.activeRegion = region;
        
        // Calculate new position based on current mode
        const newPosition = this.calculateOptimalPosition(region);
        
        // Apply position with animation
        this.animateToPosition(newPosition);
        
        // Use Tauri log plugin if available, fallback to console
        if (window.log && window.log.debug) {
            window.log.debug(`[ToolbarManager] Region updated: ${JSON.stringify(region)}`);
        } else {
            console.log('[ToolbarManager] Region updated:', region);
        }
    }

    /**
     * Calculate optimal toolbar position based on region and constraints
     * @param {Object} region - Region coordinates
     * @returns {Object} Calculated position {x, y}
     */
    calculateOptimalPosition(region) {
        if (!this.toolbarElement || !region) return { x: 0, y: 0 };

        // 🔧 BUG FIX: Validate region dimensions
        if (region.width <= 0 || region.height <= 0) {
            console.warn('[ToolbarManager] Invalid region dimensions:', region);
            return { x: 0, y: 0 };
        }

        const toolbarRect = this.toolbarElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const margin = this.options.minDistanceFromEdge;

        // 🔧 DEBUG: Add comprehensive debugging information
        console.log('[ToolbarManager] === POSITION CALCULATION DEBUG ===');
        console.log('[ToolbarManager] Region:', region);
        console.log('[ToolbarManager] Toolbar rect:', {
            width: toolbarRect.width,
            height: toolbarRect.height,
            left: toolbarRect.left,
            top: toolbarRect.top
        });
        console.log('[ToolbarManager] Viewport:', { width: viewportWidth, height: viewportHeight });
        console.log('[ToolbarManager] Margin:', margin);

        let position = { x: 0, y: 0 };

        // 🔧 BUG FIX: Always use smart positioning for better user experience
        // Check if manual position would be visible and reasonable
        let useManualPosition = false;
        if (this.positionMode === 'manual' && this.userPosition) {
            const manualPosition = {
                x: region.x + this.userPosition.offsetX,
                y: region.y + this.userPosition.offsetY
            };

            // Check if manual position is within reasonable bounds
            const isWithinBounds = manualPosition.x >= 0 &&
                                 manualPosition.y >= 0 &&
                                 manualPosition.x + toolbarRect.width <= viewportWidth &&
                                 manualPosition.y + toolbarRect.height <= viewportHeight;

            if (isWithinBounds) {
                position = manualPosition;
                useManualPosition = true;
                console.log('[ToolbarManager] Using valid manual position:', position);
            } else {
                console.log('[ToolbarManager] Manual position is out of bounds, using smart positioning instead');
                console.log('[ToolbarManager] Manual position would be:', manualPosition);
                console.log('[ToolbarManager] Viewport bounds:', { width: viewportWidth, height: viewportHeight });
            }
        }

        if (!useManualPosition) {
            // 🔧 BUG FIX: Handle edge cases for small and maximized windows
            position = this.calculateAttachedPositionWithEdgeCases(region, toolbarRect, viewportWidth, viewportHeight);
            console.log('[ToolbarManager] Calculated smart position before constraints:', position);

            // Reset manual position mode to allow smart positioning
            if (this.positionMode === 'manual') {
                console.log('[ToolbarManager] Resetting position mode from manual to auto for better positioning');
                this.positionMode = 'auto';
                this.userPosition = null;
                this.savePositionToStorage();
            }
        }

        // Apply boundary constraints
        const constrainedPosition = this.constrainToBoundaries(position, toolbarRect, viewportWidth, viewportHeight, margin);
        console.log('[ToolbarManager] Final constrained position:', constrainedPosition);
        console.log('[ToolbarManager] === END POSITION CALCULATION ===');

        return constrainedPosition;
    }

    /**
     * Calculate position with edge case handling for small and maximized windows
     * @param {Object} region - Region coordinates
     * @param {DOMRect} toolbarRect - Toolbar dimensions
     * @param {number} viewportWidth - Viewport width
     * @param {number} viewportHeight - Viewport height
     * @returns {Object} Position {x, y}
     */
    calculateAttachedPositionWithEdgeCases(region, toolbarRect, viewportWidth, viewportHeight) {
        const offset = this.options.defaultOffset;
        let position = { x: 0, y: 0 };

        // 🔧 BUG FIX: Detect window size categories
        const isSmallWindow = region.width < 300 || region.height < 200;
        const isMaximizedWindow = region.width >= viewportWidth * 0.9 && region.height >= viewportHeight * 0.9;
        const isFullScreenWindow = region.x <= 0 && region.y <= 0 &&
                                  region.width >= viewportWidth && region.height >= viewportHeight;

        console.log(`[ToolbarManager] Window classification: small=${isSmallWindow}, maximized=${isMaximizedWindow}, fullscreen=${isFullScreenWindow}`);

        if (isFullScreenWindow || isMaximizedWindow) {
            // 🔧 ISSUE 1 FIX: 使用智能多位置选择算法处理最大化窗口
            position = this.calculateBestPositionForMaximizedWindow(region, toolbarRect, viewportWidth, viewportHeight);
            if (window.log && window.log.info) {
                window.log.info(`[ToolbarManager] Maximized window: selected position (${position.x}, ${position.y})`);
            }
        } else if (isSmallWindow) {
            // For small windows, try to position toolbar outside but close to the window
            // Priority: right > bottom > left > top
            const spaceRight = viewportWidth - (region.x + region.width);
            const spaceBottom = viewportHeight - (region.y + region.height);
            const spaceLeft = region.x;
            const spaceTop = region.y;

            if (spaceRight >= toolbarRect.width + offset.x) {
                // Position to the right
                position.x = region.x + region.width + offset.x;
                position.y = region.y + Math.max(0, (region.height - toolbarRect.height) / 2);
                console.log('[ToolbarManager] Using small window positioning: right');
            } else if (spaceBottom >= toolbarRect.height + offset.y) {
                // Position below
                position.x = region.x + Math.max(0, (region.width - toolbarRect.width) / 2);
                position.y = region.y + region.height + offset.y;
                console.log('[ToolbarManager] Using small window positioning: bottom');
            } else if (spaceLeft >= toolbarRect.width + offset.x) {
                // Position to the left
                position.x = region.x - toolbarRect.width - offset.x;
                position.y = region.y + Math.max(0, (region.height - toolbarRect.height) / 2);
                console.log('[ToolbarManager] Using small window positioning: left');
            } else if (spaceTop >= toolbarRect.height + offset.y) {
                // Position above
                position.x = region.x + Math.max(0, (region.width - toolbarRect.width) / 2);
                position.y = region.y - toolbarRect.height - offset.y;
                console.log('[ToolbarManager] Using small window positioning: top');
            } else {
                // Fallback: overlay on the window (bottom-right corner)
                position.x = region.x + region.width - toolbarRect.width - offset.x;
                position.y = region.y + region.height - toolbarRect.height - offset.y;
                console.log('[ToolbarManager] Using small window positioning: overlay');
            }
        } else {
            // 🔧 ISSUE 2 FIX: 使用智能附着边选择处理普通窗口
            position = this.calculateBestPositionForNormalWindow(region, toolbarRect, viewportWidth, viewportHeight);
            if (window.log && window.log.info) {
                window.log.info(`[ToolbarManager] Normal window: selected position (${position.x}, ${position.y})`);
            }
        }

        return position;
    }

    /**
     * 🔧 ISSUE 1 FIX: 为最大化窗口计算最佳位置
     * @param {Object} region - Region coordinates
     * @param {DOMRect} toolbarRect - Toolbar dimensions
     * @param {number} viewportWidth - Viewport width
     * @param {number} viewportHeight - Viewport height
     * @returns {Object} Position {x, y}
     */
    calculateBestPositionForMaximizedWindow(region, toolbarRect, viewportWidth, viewportHeight) {
        const offset = this.options.defaultOffset;
        const toolbarWidth = toolbarRect.width;
        const toolbarHeight = toolbarRect.height;
        const margin = this.options.minDistanceFromEdge;

        // 计算所有可能的位置选项
        const positionOptions = [];

        // 选项1: 编辑框下方右对齐
        const spaceBelow = viewportHeight - (region.y + region.height);
        if (spaceBelow >= toolbarHeight + offset.y * 2) {
            positionOptions.push({
                x: Math.max(margin, Math.min(viewportWidth - toolbarWidth - margin, region.x + region.width - toolbarWidth)),
                y: region.y + region.height + offset.y,
                priority: 1,
                description: 'below-right-aligned'
            });
        }

        // 选项2: 编辑框上方右对齐
        const spaceAbove = region.y;
        if (spaceAbove >= toolbarHeight + offset.y * 2) {
            positionOptions.push({
                x: Math.max(margin, Math.min(viewportWidth - toolbarWidth - margin, region.x + region.width - toolbarWidth)),
                y: region.y - toolbarHeight - offset.y,
                priority: 2,
                description: 'above-right-aligned'
            });
        }

        // 选项3: 编辑框右侧中间对齐
        const spaceRight = viewportWidth - (region.x + region.width);
        if (spaceRight >= toolbarWidth + offset.x * 2) {
            positionOptions.push({
                x: region.x + region.width + offset.x,
                y: Math.max(margin, Math.min(viewportHeight - toolbarHeight - margin, region.y + (region.height - toolbarHeight) / 2)),
                priority: 3,
                description: 'right-center-aligned'
            });
        }

        // 选项4: 编辑框左侧中间对齐
        const spaceLeft = region.x;
        if (spaceLeft >= toolbarWidth + offset.x * 2) {
            positionOptions.push({
                x: region.x - toolbarWidth - offset.x,
                y: Math.max(margin, Math.min(viewportHeight - toolbarHeight - margin, region.y + (region.height - toolbarHeight) / 2)),
                priority: 4,
                description: 'left-center-aligned'
            });
        }

        // 选项5: 屏幕顶部右对齐（适用于底部右角窗口）
        positionOptions.push({
            x: Math.max(margin, Math.min(viewportWidth - toolbarWidth - margin, region.x + region.width - toolbarWidth)),
            y: margin,
            priority: 5,
            description: 'screen-top-right'
        });

        // 选项6: 编辑框内部右上角（最后手段）
        positionOptions.push({
            x: Math.max(margin, Math.min(viewportWidth - toolbarWidth - margin, region.x + region.width - toolbarWidth - offset.x)),
            y: Math.max(margin, region.y + offset.y),
            priority: 6,
            description: 'inside-top-right'
        });

        // 选择最佳位置
        positionOptions.sort((a, b) => a.priority - b.priority);
        const selectedPosition = positionOptions[0];

        if (window.log && window.log.debug) {
            window.log.debug(`[ToolbarManager] Maximized window position options: ${JSON.stringify(positionOptions.map(p => ({desc: p.description, priority: p.priority})))}`);
            window.log.debug(`[ToolbarManager] Selected position: ${selectedPosition.description}`);
        }

        return { x: selectedPosition.x, y: selectedPosition.y };
    }

    /**
     * Calculate position based on attachment edge preference
     * @param {Object} region - Region coordinates
     * @param {DOMRect} toolbarRect - Toolbar dimensions
     * @returns {Object} Position {x, y}
     */
    calculateAttachedPosition(region, toolbarRect) {
        const offset = this.options.defaultOffset;
        let position = { x: 0, y: 0 };

        switch (this.attachmentEdge) {
            case 'bottom':
                position.x = region.x + (region.width - toolbarRect.width) / 2;
                position.y = region.y + region.height + offset.y;
                break;
            case 'top':
                position.x = region.x + (region.width - toolbarRect.width) / 2;
                position.y = region.y - toolbarRect.height - offset.y;
                break;
            case 'right':
                position.x = region.x + region.width + offset.x;
                position.y = region.y + (region.height - toolbarRect.height) / 2;
                break;
            case 'left':
                position.x = region.x - toolbarRect.width - offset.x;
                position.y = region.y + (region.height - toolbarRect.height) / 2;
                break;
            default:
                // Default to bottom-right corner
                position.x = region.x + region.width - toolbarRect.width - offset.x;
                position.y = region.y + region.height + offset.y;
        }

        return position;
    }

    /**
     * 🔧 ISSUE 2 FIX: 为普通窗口计算最佳位置
     * @param {Object} region - Region coordinates
     * @param {DOMRect} toolbarRect - Toolbar dimensions
     * @param {number} viewportWidth - Viewport width
     * @param {number} viewportHeight - Viewport height
     * @returns {Object} Position {x, y}
     */
    calculateBestPositionForNormalWindow(region, toolbarRect, viewportWidth, viewportHeight) {
        const offset = this.options.defaultOffset;
        const toolbarWidth = toolbarRect.width;
        const toolbarHeight = toolbarRect.height;
        const margin = this.options.minDistanceFromEdge;

        // 计算各个方向的可用空间
        const spaceBelow = viewportHeight - (region.y + region.height);
        const spaceAbove = region.y;
        const spaceRight = viewportWidth - (region.x + region.width);
        const spaceLeft = region.x;

        // 🔧 DEBUG: Add detailed space calculation logging
        console.log('[ToolbarManager] === NORMAL WINDOW POSITIONING DEBUG ===');
        console.log('[ToolbarManager] Region:', region);
        console.log('[ToolbarManager] Toolbar dimensions:', { width: toolbarWidth, height: toolbarHeight });
        console.log('[ToolbarManager] Offset:', offset);
        console.log('[ToolbarManager] Available spaces:', {
            below: spaceBelow,
            above: spaceAbove,
            right: spaceRight,
            left: spaceLeft
        });

        // 计算所有可能的位置选项，按距离编辑框的远近排序
        const positionOptions = [];

        // 选项1: 编辑框下方居中（最优选择）
        if (spaceBelow >= toolbarHeight + offset.y) {
            positionOptions.push({
                x: region.x + (region.width - toolbarWidth) / 2,
                y: region.y + region.height + offset.y,
                priority: 1,
                distance: offset.y,
                description: 'below-center'
            });
        }

        // 选项2: 编辑框上方居中
        if (spaceAbove >= toolbarHeight + offset.y) {
            positionOptions.push({
                x: region.x + (region.width - toolbarWidth) / 2,
                y: region.y - toolbarHeight - offset.y,
                priority: 2,
                distance: offset.y,
                description: 'above-center'
            });
        }

        // 选项3: 编辑框右侧中间对齐
        if (spaceRight >= toolbarWidth + offset.x) {
            const rightCenterX = region.x + region.width + offset.x;
            console.log(`[ToolbarManager] Right-center option: x = ${region.x} + ${region.width} + ${offset.x} = ${rightCenterX}`);
            positionOptions.push({
                x: rightCenterX,
                y: region.y + (region.height - toolbarHeight) / 2,
                priority: 3,
                distance: offset.x,
                description: 'right-center'
            });
        } else {
            console.log(`[ToolbarManager] Right-center option rejected: spaceRight(${spaceRight}) < required(${toolbarWidth + offset.x})`);
        }

        // 选项4: 编辑框左侧中间对齐
        if (spaceLeft >= toolbarWidth + offset.x) {
            positionOptions.push({
                x: region.x - toolbarWidth - offset.x,
                y: region.y + (region.height - toolbarHeight) / 2,
                priority: 4,
                distance: offset.x,
                description: 'left-center'
            });
        }

        // 选项5: 编辑框下方右对齐（如果居中不合适）
        if (spaceBelow >= toolbarHeight + offset.y) {
            positionOptions.push({
                x: region.x + region.width - toolbarWidth,
                y: region.y + region.height + offset.y,
                priority: 5,
                distance: offset.y,
                description: 'below-right'
            });
        }

        // 选项6: 编辑框下方左对齐（如果居中和右对齐都不合适）
        if (spaceBelow >= toolbarHeight + offset.y) {
            positionOptions.push({
                x: region.x,
                y: region.y + region.height + offset.y,
                priority: 6,
                distance: offset.y,
                description: 'below-left'
            });
        }

        // 如果没有合适的外部位置，使用默认的右下角位置
        if (positionOptions.length === 0) {
            positionOptions.push({
                x: region.x + region.width - toolbarWidth - offset.x,
                y: region.y + region.height + offset.y,
                priority: 7,
                distance: Math.sqrt(offset.x * offset.x + offset.y * offset.y),
                description: 'default-bottom-right'
            });
        }

        // 选择最佳位置（优先级最高且距离最近）
        positionOptions.sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            return a.distance - b.distance;
        });

        const selectedPosition = positionOptions[0];

        if (window.log && window.log.debug) {
            window.log.debug(`[ToolbarManager] Normal window position options: ${JSON.stringify(positionOptions.map(p => ({desc: p.description, priority: p.priority, distance: p.distance})))}`);
            window.log.debug(`[ToolbarManager] Selected position: ${selectedPosition.description} (distance: ${selectedPosition.distance})`);
        }

        return { x: selectedPosition.x, y: selectedPosition.y };
    }

    /**
     * Constrain position to viewport boundaries with smart repositioning
     * @param {Object} position - Desired position
     * @param {DOMRect} toolbarRect - Toolbar dimensions
     * @param {number} viewportWidth - Viewport width
     * @param {number} viewportHeight - Viewport height
     * @param {number} margin - Minimum distance from edges
     * @returns {Object} Constrained position
     */
    constrainToBoundaries(position, toolbarRect, viewportWidth, viewportHeight, margin) {
        let { x, y } = position;

        // 🔧 BUG FIX: Enhanced boundary constraints for edge cases
        const originalX = x;
        const originalY = y;

        // Horizontal constraints
        if (x < margin) {
            x = margin;
            console.log(`[ToolbarManager] Constrained X from ${originalX} to ${x} (left boundary)`);
        } else if (x + toolbarRect.width > viewportWidth - margin) {
            x = viewportWidth - toolbarRect.width - margin;
            console.log(`[ToolbarManager] Constrained X from ${originalX} to ${x} (right boundary)`);
        }

        // Vertical constraints with smart repositioning
        if (y < margin) {
            y = margin;
            console.log(`[ToolbarManager] Constrained Y from ${originalY} to ${y} (top boundary)`);

            // If toolbar was supposed to be above region, try below
            if (this.attachmentEdge === 'top' && this.activeRegion) {
                const belowY = this.activeRegion.y + this.activeRegion.height + this.options.defaultOffset.y;
                if (belowY + toolbarRect.height <= viewportHeight - margin) {
                    y = belowY;
                    this.attachmentEdge = 'bottom'; // Temporarily switch edge
                    console.log(`[ToolbarManager] Smart repositioning: moved from top to bottom (${y})`);
                }
            }
        } else if (y + toolbarRect.height > viewportHeight - margin) {
            y = viewportHeight - toolbarRect.height - margin;
            console.log(`[ToolbarManager] Constrained Y from ${originalY} to ${y} (bottom boundary)`);

            // If toolbar was supposed to be below region, try above
            if (this.attachmentEdge === 'bottom' && this.activeRegion) {
                const aboveY = this.activeRegion.y - toolbarRect.height - this.options.defaultOffset.y;
                if (aboveY >= margin) {
                    y = aboveY;
                    this.attachmentEdge = 'top'; // Temporarily switch edge
                    console.log(`[ToolbarManager] Smart repositioning: moved from bottom to top (${y})`);
                }
            }
        }

        // 🔧 BUG FIX: Ensure toolbar is always visible even in extreme cases
        if (x < 0) x = 0;
        if (y < 0) y = 0;
        if (x + toolbarRect.width > viewportWidth) x = viewportWidth - toolbarRect.width;
        if (y + toolbarRect.height > viewportHeight) y = viewportHeight - toolbarRect.height;

        const finalPosition = { x, y };
        if (originalX !== x || originalY !== y) {
            console.log(`[ToolbarManager] Final position adjustment: (${originalX}, ${originalY}) → (${x}, ${y})`);
        }

        return finalPosition;
    }

    /**
     * Animate toolbar to new position with smooth transition
     * @param {Object} targetPosition - Target position {x, y}
     */
    animateToPosition(targetPosition) {
        if (!this.toolbarElement) return;

        // Cancel any existing animation
        if (this.currentAnimation) {
            this.currentAnimation.cancel();
        }

        const startPosition = {
            x: parseFloat(this.toolbarElement.style.left) || 0,
            y: parseFloat(this.toolbarElement.style.top) || 0
        };

        // Skip animation if position hasn't changed significantly
        const deltaX = Math.abs(targetPosition.x - startPosition.x);
        const deltaY = Math.abs(targetPosition.y - startPosition.y);
        if (deltaX < 1 && deltaY < 1) return;

        // Create smooth animation
        this.currentAnimation = this.toolbarElement.animate([
            {
                left: `${startPosition.x}px`,
                top: `${startPosition.y}px`
            },
            {
                left: `${targetPosition.x}px`,
                top: `${targetPosition.y}px`
            }
        ], {
            duration: this.options.animationDuration,
            easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // easeOutQuad
            fill: 'forwards'
        });

        // Update final position
        this.currentAnimation.addEventListener('finish', () => {
            this.toolbarElement.style.left = `${targetPosition.x}px`;
            this.toolbarElement.style.top = `${targetPosition.y}px`;
            this.currentAnimation = null;
        });
    }

    /**
     * Apply base styles to toolbar for positioning
     */
    applyToolbarStyles() {
        if (!this.toolbarElement) return;

        Object.assign(this.toolbarElement.style, {
            position: 'fixed',
            zIndex: '1002',
            pointerEvents: 'auto',
            transition: 'none' // We handle animations manually
        });
    }

    /**
     * Enable drag functionality for manual positioning
     * 🔧 ISSUE 2 FIX: Enhanced drag functionality with visual feedback
     */
    enableDragging() {
        if (!this.toolbarElement) return;

        // Add drag handle if it doesn't exist
        let dragHandle = this.toolbarElement.querySelector('.toolbar-drag-handle');
        if (!dragHandle) {
            dragHandle = document.createElement('div');
            dragHandle.className = 'toolbar-drag-handle';
            dragHandle.innerHTML = '⋮⋮';
            dragHandle.style.cssText = `
                cursor: grab;
                padding: 6px 8px;
                user-select: none;
                opacity: 0.7;
                transition: all 0.2s ease;
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Add hover effects
            dragHandle.addEventListener('mouseenter', () => {
                dragHandle.style.opacity = '1';
                dragHandle.style.background = 'rgba(255, 255, 255, 0.2)';
                dragHandle.style.transform = 'scale(1.05)';
            });

            dragHandle.addEventListener('mouseleave', () => {
                if (!this.isDragging) {
                    dragHandle.style.opacity = '0.7';
                    dragHandle.style.background = 'rgba(255, 255, 255, 0.1)';
                    dragHandle.style.transform = 'scale(1)';
                }
            });

            this.toolbarElement.insertBefore(dragHandle, this.toolbarElement.firstChild);
        }

        // Add event listeners with proper cleanup
        this.removeDragListeners(); // Remove any existing listeners

        dragHandle.addEventListener('mousedown', this.boundHandlers.onDragStart);
        document.addEventListener('mousemove', this.boundHandlers.onDrag);
        document.addEventListener('mouseup', this.boundHandlers.onDragEnd);

        // Store references for cleanup
        this.dragHandle = dragHandle;

        if (window.log && window.log.debug) {
            window.log.debug('[ToolbarManager] Drag functionality enabled with enhanced visual feedback');
        }
    }

    /**
     * Remove drag event listeners for cleanup
     */
    removeDragListeners() {
        if (this.dragHandle) {
            this.dragHandle.removeEventListener('mousedown', this.boundHandlers.onDragStart);
        }
        document.removeEventListener('mousemove', this.boundHandlers.onDrag);
        document.removeEventListener('mouseup', this.boundHandlers.onDragEnd);
    }

    // Event handlers
    handleRegionChange(region) {
        this.updateRegion(region);
    }

    handleWindowResize() {
        if (this.activeRegion) {
            this.updateRegion(this.activeRegion);
        }
    }

    handleDragStart(event) {
        if (!this.toolbarElement) return;

        this.isDragging = true;
        this.positionMode = 'manual';

        const rect = this.toolbarElement.getBoundingClientRect();
        this.dragOffset = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };

        // 🔧 ISSUE 2 FIX: Enhanced visual feedback during drag
        event.target.style.cursor = 'grabbing';
        this.toolbarElement.style.opacity = '0.8';
        this.toolbarElement.style.transform = 'scale(1.05)';
        this.toolbarElement.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.4)';
        this.toolbarElement.style.zIndex = '10001'; // Ensure it's above other elements during drag

        // Add dragging class for additional styling
        this.toolbarElement.classList.add('toolbar-dragging');

        event.preventDefault();
        event.stopPropagation();

        if (window.log && window.log.debug) {
            window.log.debug('[ToolbarManager] Drag started with enhanced visual feedback');
        }
    }

    handleDrag(event) {
        if (!this.isDragging || !this.toolbarElement || !this.activeRegion) return;

        let newX = event.clientX - this.dragOffset.x;
        let newY = event.clientY - this.dragOffset.y;

        // Apply snap-to-edge behavior if enabled
        if (this.options.snapToEdges) {
            const snapResult = this.calculateSnapPosition(newX, newY);
            newX = snapResult.x;
            newY = snapResult.y;

            // Update visual feedback for snap zones
            this.updateSnapVisualFeedback(snapResult.snapZone);
        }

        // Calculate relative position to region
        this.userPosition = {
            offsetX: newX - this.activeRegion.x,
            offsetY: newY - this.activeRegion.y
        };

        // Apply position immediately during drag
        this.toolbarElement.style.left = `${newX}px`;
        this.toolbarElement.style.top = `${newY}px`;
    }

    handleDragEnd(event) {
        if (!this.isDragging) return;

        this.isDragging = false;
        this.isSnapping = false;

        // 🔧 ISSUE 2 FIX: Restore visual state after drag
        const dragHandle = this.toolbarElement.querySelector('.toolbar-drag-handle');
        if (dragHandle) {
            dragHandle.style.cursor = 'grab';
            dragHandle.style.opacity = '0.7';
            dragHandle.style.background = 'rgba(255, 255, 255, 0.1)';
            dragHandle.style.transform = 'scale(1)';
        }

        // Restore toolbar visual state
        this.toolbarElement.style.opacity = '1';
        this.toolbarElement.style.transform = 'scale(1)';
        this.toolbarElement.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
        this.toolbarElement.style.zIndex = '10000'; // Reset z-index
        this.toolbarElement.classList.remove('toolbar-dragging');

        // Clear snap visual feedback
        this.clearSnapVisualFeedback();

        // Persist position if enabled
        if (this.options.persistPosition && this.userPosition) {
            this.savePositionToStorage();
        }

        if (window.log && window.log.info) {
            window.log.info('[ToolbarManager] Manual position set:', this.userPosition);
        }
    }

    /**
     * Set up observer for region changes
     */
    setupRegionObserver() {
        // This will be implemented to observe region changes from various sources
        // For now, we rely on explicit updateRegion calls
    }

    /**
     * Reset to automatic positioning
     */
    resetToAutoPosition() {
        this.positionMode = 'auto';
        this.userPosition = null;
        
        if (this.activeRegion) {
            this.updateRegion(this.activeRegion);
        }
    }

    /**
     * Calculate snap position based on region edges and viewport boundaries
     * @param {number} x - Current X position
     * @param {number} y - Current Y position
     * @returns {Object} Snap result {x, y, snapZone}
     */
    calculateSnapPosition(x, y) {
        if (!this.activeRegion || !this.toolbarElement) {
            return { x, y, snapZone: null };
        }

        const toolbarRect = this.toolbarElement.getBoundingClientRect();
        const region = this.activeRegion;
        const threshold = this.options.snapThreshold;
        const margin = this.options.minDistanceFromEdge;

        let snapX = x;
        let snapY = y;
        let snapZone = null;

        // Define snap zones around the region
        const snapZones = [
            // Bottom edge
            {
                name: 'bottom',
                x: region.x + (region.width - toolbarRect.width) / 2,
                y: region.y + region.height + margin,
                edge: 'bottom'
            },
            // Top edge
            {
                name: 'top',
                x: region.x + (region.width - toolbarRect.width) / 2,
                y: region.y - toolbarRect.height - margin,
                edge: 'top'
            },
            // Right edge
            {
                name: 'right',
                x: region.x + region.width + margin,
                y: region.y + (region.height - toolbarRect.height) / 2,
                edge: 'right'
            },
            // Left edge
            {
                name: 'left',
                x: region.x - toolbarRect.width - margin,
                y: region.y + (region.height - toolbarRect.height) / 2,
                edge: 'left'
            }
        ];

        // Find closest snap zone
        let minDistance = threshold;
        for (const zone of snapZones) {
            const distance = Math.sqrt(
                Math.pow(x - zone.x, 2) + Math.pow(y - zone.y, 2)
            );

            if (distance < minDistance) {
                minDistance = distance;
                snapX = zone.x;
                snapY = zone.y;
                snapZone = zone;
            }
        }

        return { x: snapX, y: snapY, snapZone };
    }

    /**
     * Update visual feedback for snap zones
     * @param {Object} snapZone - Active snap zone
     */
    updateSnapVisualFeedback(snapZone) {
        if (!snapZone) {
            this.clearSnapVisualFeedback();
            return;
        }

        // Create or update snap indicator
        let indicator = document.getElementById('toolbar-snap-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'toolbar-snap-indicator';
            indicator.style.cssText = `
                position: fixed;
                width: 8px;
                height: 8px;
                background: #FF6B35;
                border: 2px solid white;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1003;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                transition: all 0.1s ease;
            `;
            document.body.appendChild(indicator);
        }

        indicator.style.left = `${snapZone.x}px`;
        indicator.style.top = `${snapZone.y}px`;
        indicator.style.display = 'block';
    }

    /**
     * Clear snap visual feedback
     */
    clearSnapVisualFeedback() {
        const indicator = document.getElementById('toolbar-snap-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    /**
     * Save current position to localStorage
     */
    savePositionToStorage() {
        if (!this.userPosition) return;

        try {
            const positionData = {
                mode: this.positionMode,
                userPosition: this.userPosition,
                attachmentEdge: this.attachmentEdge,
                timestamp: Date.now()
            };

            localStorage.setItem('mecap-toolbar-position', JSON.stringify(positionData));
            console.log('[ToolbarManager] Position saved to storage');
        } catch (error) {
            console.warn('[ToolbarManager] Failed to save position:', error);
        }
    }

    /**
     * Load position from localStorage
     */
    loadPositionFromStorage() {
        if (!this.options.persistPosition) return null;

        try {
            const stored = localStorage.getItem('mecap-toolbar-position');
            if (stored) {
                const positionData = JSON.parse(stored);

                // Check if position is not too old (24 hours)
                const maxAge = 24 * 60 * 60 * 1000;
                if (Date.now() - positionData.timestamp < maxAge) {
                    console.log('[ToolbarManager] Position loaded from storage');
                    return positionData;
                }
            }
        } catch (error) {
            console.warn('[ToolbarManager] Failed to load position:', error);
        }

        return null;
    }

    /**
     * Apply stored position if available
     */
    applyStoredPosition() {
        const stored = this.loadPositionFromStorage();
        if (stored) {
            this.positionMode = stored.mode;
            this.userPosition = stored.userPosition;
            this.attachmentEdge = stored.attachmentEdge;

            if (this.activeRegion) {
                this.updateRegion(this.activeRegion);
            }
        }
    }

    /**
     * Cleanup and remove event listeners
     */
    destroy() {
        window.removeEventListener('resize', this.boundHandlers.onWindowResize);

        // 🔧 ISSUE 2 FIX: Cleanup drag listeners
        this.removeDragListeners();

        if (this.currentAnimation) {
            this.currentAnimation.cancel();
        }

        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        this.clearSnapVisualFeedback();

        if (window.log && window.log.info) {
            window.log.info('[ToolbarManager] Destroyed with complete cleanup');
        }
    }
}

// Export singleton instance
export const toolbarPositionManager = new ToolbarPositionManager();
