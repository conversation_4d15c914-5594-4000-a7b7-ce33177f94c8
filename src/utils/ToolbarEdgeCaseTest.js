/**
 * Edge Case Test Suite for Toolbar Positioning Issues
 * Specifically tests small windows and maximized windows scenarios
 */

import { ToolbarPositionManager } from './ToolbarPositionManager.js';
import { EnhancedQuickActionToolbar } from './ToolbarIntegration.js';

export class ToolbarEdgeCaseTest {
    constructor() {
        this.testResults = [];
        this.testContainer = null;
        this.mockToolbar = null;
        this.positionManager = null;
    }

    /**
     * Run edge case tests for toolbar positioning
     */
    async runEdgeCaseTests() {
        console.log('[EdgeCaseTest] Starting toolbar edge case tests...');
        
        this.setupTestEnvironment();
        
        // Test specific edge cases
        await this.testSmallWindowPositioning();
        await this.testMaximizedWindowPositioning();
        await this.testFullScreenWindowPositioning();
        await this.testZeroDimensionRegion();
        await this.testNegativeCoordinates();
        await this.testMultiMonitorEdgeCases();
        
        this.cleanupTestEnvironment();
        
        return this.generateTestReport();
    }

    /**
     * Setup test environment
     */
    setupTestEnvironment() {
        // Create test container
        this.testContainer = document.createElement('div');
        this.testContainer.id = 'edge-case-test-container';
        this.testContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(255, 0, 0, 0.1);
            z-index: 9998;
            pointer-events: none;
        `;
        document.body.appendChild(this.testContainer);

        // Create mock toolbar
        this.mockToolbar = document.createElement('div');
        this.mockToolbar.id = 'edge-case-mock-toolbar';
        this.mockToolbar.style.cssText = `
            width: 200px;
            height: 50px;
            background: rgba(0, 255, 0, 0.8);
            border: 2px solid green;
            position: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        `;
        this.mockToolbar.textContent = 'EDGE CASE TEST';
        this.testContainer.appendChild(this.mockToolbar);

        // Initialize position manager
        this.positionManager = new ToolbarPositionManager({
            animationDuration: 50, // Faster for testing
            snapThreshold: 30,
            minDistanceFromEdge: 10
        });

        console.log('[EdgeCaseTest] Test environment setup complete');
    }

    /**
     * Test small window positioning (< 300px dimensions)
     */
    async testSmallWindowPositioning() {
        const testName = 'Small Window Positioning';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            this.positionManager.registerToolbar(this.mockToolbar, {
                mode: 'auto',
                edge: 'bottom',
                draggable: false
            });

            // Test various small window scenarios
            const smallWindows = [
                { x: 100, y: 100, width: 200, height: 150 },  // Very small
                { x: 300, y: 200, width: 250, height: 180 },  // Small
                { x: 500, y: 300, width: 150, height: 100 },  // Tiny
                { x: 50, y: 50, width: 180, height: 120 }     // Small near edge
            ];

            let passed = true;
            for (const region of smallWindows) {
                this.positionManager.updateRegion(region);
                await this.wait(100);

                const toolbarRect = this.mockToolbar.getBoundingClientRect();
                
                // Verify toolbar is visible and positioned correctly
                const isVisible = toolbarRect.width > 0 && toolbarRect.height > 0;
                const isWithinViewport = toolbarRect.left >= 0 && 
                                       toolbarRect.top >= 0 && 
                                       toolbarRect.right <= window.innerWidth &&
                                       toolbarRect.bottom <= window.innerHeight;

                if (!isVisible || !isWithinViewport) {
                    passed = false;
                    console.log(`[EdgeCaseTest] Small window test failed for region:`, region);
                    console.log(`[EdgeCaseTest] Toolbar rect:`, toolbarRect);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'All small windows positioned correctly' : 'Small window positioning failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test maximized window positioning
     */
    async testMaximizedWindowPositioning() {
        const testName = 'Maximized Window Positioning';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            // Test maximized window scenarios
            const maximizedWindows = [
                { x: 0, y: 0, width: window.innerWidth, height: window.innerHeight },  // True fullscreen
                { x: 0, y: 25, width: window.innerWidth, height: window.innerHeight - 25 },  // Maximized with menu bar
                { x: 0, y: 0, width: window.innerWidth * 0.95, height: window.innerHeight * 0.95 }  // Near-maximized
            ];

            let passed = true;
            for (const region of maximizedWindows) {
                this.positionManager.updateRegion(region);
                await this.wait(100);

                const toolbarRect = this.mockToolbar.getBoundingClientRect();
                
                // Verify toolbar is visible and not hidden off-screen
                const isVisible = toolbarRect.width > 0 && toolbarRect.height > 0;
                const isWithinViewport = toolbarRect.left >= 0 && 
                                       toolbarRect.top >= 0 && 
                                       toolbarRect.right <= window.innerWidth &&
                                       toolbarRect.bottom <= window.innerHeight;

                // For maximized windows, toolbar should be in bottom-right corner
                const isInBottomRight = toolbarRect.right >= window.innerWidth * 0.8 &&
                                      toolbarRect.bottom >= window.innerHeight * 0.8;

                if (!isVisible || !isWithinViewport || !isInBottomRight) {
                    passed = false;
                    console.log(`[EdgeCaseTest] Maximized window test failed for region:`, region);
                    console.log(`[EdgeCaseTest] Toolbar rect:`, toolbarRect);
                    console.log(`[EdgeCaseTest] Checks: visible=${isVisible}, inViewport=${isWithinViewport}, bottomRight=${isInBottomRight}`);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'All maximized windows positioned correctly' : 'Maximized window positioning failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test fullscreen window positioning
     */
    async testFullScreenWindowPositioning() {
        const testName = 'FullScreen Window Positioning';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            // Test fullscreen window
            const fullscreenRegion = { 
                x: 0, 
                y: 0, 
                width: window.innerWidth, 
                height: window.innerHeight 
            };

            this.positionManager.updateRegion(fullscreenRegion);
            await this.wait(100);

            const toolbarRect = this.mockToolbar.getBoundingClientRect();
            
            // Verify toolbar is visible and positioned in bottom-right
            const isVisible = toolbarRect.width > 0 && toolbarRect.height > 0;
            const isWithinViewport = toolbarRect.left >= 0 && 
                                   toolbarRect.top >= 0 && 
                                   toolbarRect.right <= window.innerWidth &&
                                   toolbarRect.bottom <= window.innerHeight;

            const passed = isVisible && isWithinViewport;
            this.recordTest(testName, passed, passed ? 'Fullscreen window positioned correctly' : 'Fullscreen positioning failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test zero dimension region handling
     */
    async testZeroDimensionRegion() {
        const testName = 'Zero Dimension Region';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            // Test zero dimension regions
            const zeroDimensionRegions = [
                { x: 100, y: 100, width: 0, height: 0 },
                { x: 200, y: 200, width: 0, height: 100 },
                { x: 300, y: 300, width: 100, height: 0 }
            ];

            let passed = true;
            for (const region of zeroDimensionRegions) {
                // This should not crash and should handle gracefully
                try {
                    this.positionManager.updateRegion(region);
                    await this.wait(50);
                } catch (error) {
                    passed = false;
                    console.log(`[EdgeCaseTest] Zero dimension test failed:`, error);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'Zero dimension regions handled gracefully' : 'Zero dimension handling failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test negative coordinates (multi-monitor scenarios)
     */
    async testNegativeCoordinates() {
        const testName = 'Negative Coordinates';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            // Test negative coordinate regions
            const negativeRegions = [
                { x: -500, y: 100, width: 300, height: 200 },  // Left monitor
                { x: 100, y: -300, width: 300, height: 200 },  // Top monitor
                { x: -200, y: -200, width: 300, height: 200 }  // Top-left monitor
            ];

            let passed = true;
            for (const region of negativeRegions) {
                try {
                    this.positionManager.updateRegion(region);
                    await this.wait(50);
                    
                    // Should not crash, toolbar position should be calculated
                    const toolbarRect = this.mockToolbar.getBoundingClientRect();
                    if (toolbarRect.width <= 0 || toolbarRect.height <= 0) {
                        passed = false;
                        break;
                    }
                } catch (error) {
                    passed = false;
                    console.log(`[EdgeCaseTest] Negative coordinate test failed:`, error);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'Negative coordinates handled correctly' : 'Negative coordinate handling failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test multi-monitor edge cases
     */
    async testMultiMonitorEdgeCases() {
        const testName = 'Multi-Monitor Edge Cases';
        console.log(`[EdgeCaseTest] Running ${testName}...`);

        try {
            // Test extreme multi-monitor scenarios
            const multiMonitorRegions = [
                { x: 3840, y: 100, width: 300, height: 200 },    // Far right monitor
                { x: 100, y: 2160, width: 300, height: 200 },    // Bottom monitor
                { x: -1920, y: -1080, width: 300, height: 200 }  // Top-left monitor
            ];

            let passed = true;
            for (const region of multiMonitorRegions) {
                try {
                    this.positionManager.updateRegion(region);
                    await this.wait(50);
                    
                    // Should handle gracefully without errors
                } catch (error) {
                    passed = false;
                    console.log(`[EdgeCaseTest] Multi-monitor test failed:`, error);
                    break;
                }
            }

            this.recordTest(testName, passed, passed ? 'Multi-monitor scenarios handled' : 'Multi-monitor handling failed');
        } catch (error) {
            this.recordTest(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Cleanup test environment
     */
    cleanupTestEnvironment() {
        if (this.testContainer) {
            this.testContainer.remove();
        }
        
        if (this.positionManager) {
            this.positionManager.destroy();
        }
        
        console.log('[EdgeCaseTest] Test environment cleaned up');
    }

    /**
     * Record test result
     */
    recordTest(name, passed, details) {
        const result = {
            name,
            passed,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        console.log(`[EdgeCaseTest] ${name}: ${passed ? 'PASS' : 'FAIL'} - ${details}`);
    }

    /**
     * Generate test report
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        const report = {
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                successRate: ((passedTests / totalTests) * 100).toFixed(1) + '%'
            },
            results: this.testResults,
            timestamp: new Date().toISOString()
        };

        console.log('[EdgeCaseTest] Edge Case Test Report:', report);
        return report;
    }

    /**
     * Utility function to wait
     */
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export test runner function
export async function runToolbarEdgeCaseTests() {
    const tester = new ToolbarEdgeCaseTest();
    return await tester.runEdgeCaseTests();
}
