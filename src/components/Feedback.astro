---
import FeelbackReaction from 'astro-feelback/components/FeelbackReaction.astro';
import 'astro-feelback/styles/feelback.css';

// Waiting for https://discord.com/channels/830184174198718474/1134090586052378624 to be build out to dynamically insert into the layout for every page
const CONTENT_SET_ID = '54faa8e2-cd0d-4997-9a3c-d9fed28f358a';
---

<FeelbackReaction
  contentSetId={CONTENT_SET_ID}
  key={Astro.url.pathname}
  layout="list"
  preset="feeling"
/>
