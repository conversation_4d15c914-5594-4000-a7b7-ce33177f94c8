import React from 'react';
import { 
  <PERSON>ton, 
  CircularProgress, 
  Box, 
  Card, 
  CardContent, 
  Typography,
  ButtonGroup,
  Tooltip
} from '@mui/material';
import { 
  CropFree as RegionIcon,
  Window as WindowIcon,
  Screenshot as FullScreenIcon
} from '@mui/icons-material';
import { invoke } from '@tauri-apps/api/core';
import { useScreenshotStore } from '../store/screenshotStore';

interface ScreenshotResult {
  path: string;
  width: number;
  height: number;
}

interface ScreenshotArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const ScreenshotCapture: React.FC = () => {
  const { isCapturing, addScreenshot, setIsCapturing } = useScreenshotStore();

  const captureRegion = async () => {
    setIsCapturing(true);
    try {
      // TODO: Implement region selection UI
      // For now, using placeholder values
      const area: ScreenshotArea = { x: 0, y: 0, width: 800, height: 600 };
      
      const result = await invoke<ScreenshotResult>('capture_region', {
        area,
        savePath: null
      });
      
      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['region'],
        name: `Region Screenshot ${new Date().toLocaleTimeString()}`
      });
      
    } catch (error) {
      console.error('Screenshot failed:', error);
      // TODO: Show error notification
    } finally {
      setIsCapturing(false);
    }
  };

  const captureWindow = async () => {
    setIsCapturing(true);
    try {
      // TODO: Implement window selection
      const result = await invoke<ScreenshotResult>('capture_window', {
        savePath: null
      });
      
      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['window'],
        name: `Window Screenshot ${new Date().toLocaleTimeString()}`
      });
      
    } catch (error) {
      console.error('Window screenshot failed:', error);
    } finally {
      setIsCapturing(false);
    }
  };

  const captureFullScreen = async () => {
    setIsCapturing(true);
    try {
      const result = await invoke<ScreenshotResult>('capture_fullscreen', {
        savePath: null
      });
      
      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['fullscreen'],
        name: `Full Screen Screenshot ${new Date().toLocaleTimeString()}`
      });
      
    } catch (error) {
      console.error('Full screen screenshot failed:', error);
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <Card sx={{ maxWidth: 400, margin: 'auto' }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          Capture Screenshot
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Choose the type of screenshot you want to capture
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <ButtonGroup 
            variant="contained" 
            orientation="vertical" 
            fullWidth
            disabled={isCapturing}
          >
            <Tooltip title="Select a region of the screen to capture">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <RegionIcon />}
                onClick={captureRegion}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Region
              </Button>
            </Tooltip>
            
            <Tooltip title="Capture a specific window">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <WindowIcon />}
                onClick={captureWindow}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Window
              </Button>
            </Tooltip>
            
            <Tooltip title="Capture the entire screen">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <FullScreenIcon />}
                onClick={captureFullScreen}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Full Screen
              </Button>
            </Tooltip>
          </ButtonGroup>
        </Box>

        {isCapturing && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Capturing screenshot...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
