import React, { useState } from 'react';
import { Rect, Line, Text, Group } from 'react-konva';
import { useEditorStore } from '../../store/editorStore';
import { Shape } from '../../types/editor';

interface DrawingToolsProps {
  stageRef: React.RefObject<any>;
  currentColor?: string;
  currentStrokeWidth?: number;
}

export const DrawingTools: React.FC<DrawingToolsProps> = ({
  stageRef,
  currentColor = '#ff0000',
  currentStrokeWidth = 2
}) => {
  const {
    currentTool,
    shapes,
    addShape,
    selectedShapeId,
    setSelectedShapeId
  } = useEditorStore();

  const [isDrawing, setIsDrawing] = useState(false);
  const [currentShape, setCurrentShape] = useState<Shape | null>(null);

  // 开始绘制
  const handleMouseDown = (e: any) => {
    if (!['rectangle', 'arrow', 'pen'].includes(currentTool)) return;
    
    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();
    setIsDrawing(true);

    const newShape: Shape = {
      id: `shape_${Date.now()}`,
      type: currentTool as 'rectangle' | 'arrow' | 'path',
      x: pos.x,
      y: pos.y,
      style: {
        stroke: currentColor,
        strokeWidth: currentStrokeWidth,
        fill: currentTool === 'rectangle' ? 'transparent' : undefined
      }
    };

    if (currentTool === 'rectangle') {
      newShape.width = 0;
      newShape.height = 0;
    } else if (currentTool === 'pen') {
      newShape.type = 'path';
      newShape.points = [pos.x, pos.y];
    } else if (currentTool === 'arrow') {
      newShape.points = [pos.x, pos.y, pos.x, pos.y];
    }

    setCurrentShape(newShape);
  };

  // 绘制过程中
  const handleMouseMove = (e: any) => {
    if (!isDrawing || !currentShape) return;

    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();

    if (currentTool === 'rectangle') {
      const width = pos.x - currentShape.x;
      const height = pos.y - currentShape.y;
      setCurrentShape({
        ...currentShape,
        width: Math.abs(width),
        height: Math.abs(height),
        x: width < 0 ? pos.x : currentShape.x,
        y: height < 0 ? pos.y : currentShape.y
      });
    } else if (currentTool === 'pen') {
      const newPoints = currentShape.points!.concat([pos.x, pos.y]);
      setCurrentShape({
        ...currentShape,
        points: newPoints
      });
    } else if (currentTool === 'arrow') {
      setCurrentShape({
        ...currentShape,
        points: [currentShape.x, currentShape.y, pos.x, pos.y]
      });
    }
  };

  // 结束绘制
  const handleMouseUp = () => {
    if (!isDrawing || !currentShape) return;

    setIsDrawing(false);
    
    // 只有当形状有实际大小时才添加
    if (currentTool === 'rectangle' && currentShape.width! > 5 && currentShape.height! > 5) {
      addShape(currentShape);
    } else if (currentTool === 'pen' && currentShape.points!.length > 4) {
      addShape(currentShape);
    } else if (currentTool === 'arrow' && currentShape.points!.length === 4) {
      const [x1, y1, x2, y2] = currentShape.points!;
      const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
      if (distance > 10) {
        addShape(currentShape);
      }
    }

    setCurrentShape(null);
  };

  // 渲染箭头
  const renderArrow = (shape: Shape) => {
    if (!shape.points || shape.points.length !== 4) return null;
    
    const [x1, y1, x2, y2] = shape.points;
    const headLength = 15;
    const angle = Math.atan2(y2 - y1, x2 - x1);
    
    // 箭头线
    const linePoints = [x1, y1, x2, y2];
    
    // 箭头头部
    const arrowHead1X = x2 - headLength * Math.cos(angle - Math.PI / 6);
    const arrowHead1Y = y2 - headLength * Math.sin(angle - Math.PI / 6);
    const arrowHead2X = x2 - headLength * Math.cos(angle + Math.PI / 6);
    const arrowHead2Y = y2 - headLength * Math.sin(angle + Math.PI / 6);
    
    return (
      <Group key={shape.id}>
        <Line
          points={linePoints}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
        <Line
          points={[x2, y2, arrowHead1X, arrowHead1Y]}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
        <Line
          points={[x2, y2, arrowHead2X, arrowHead2Y]}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
      </Group>
    );
  };

  // 渲染形状
  const renderShape = (shape: Shape) => {
    const isSelected = selectedShapeId === shape.id;
    const strokeWidth = isSelected ? shape.style.strokeWidth + 1 : shape.style.strokeWidth;

    switch (shape.type) {
      case 'rectangle':
        return (
          <Rect
            key={shape.id}
            x={shape.x}
            y={shape.y}
            width={shape.width || 0}
            height={shape.height || 0}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            fill={shape.style.fill}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );
      
      case 'path':
        return (
          <Line
            key={shape.id}
            points={shape.points || []}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            lineCap="round"
            lineJoin="round"
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );
      
      case 'arrow':
        return renderArrow(shape);
      
      case 'text':
        return (
          <Text
            key={shape.id}
            x={shape.x}
            y={shape.y}
            text={shape.text || ''}
            fontSize={shape.style.fontSize || 16}
            fontFamily={shape.style.fontFamily || 'Arial'}
            fill={shape.style.stroke}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <>
      {/* 渲染所有已保存的形状 */}
      {shapes.map(renderShape)}
      
      {/* 渲染正在绘制的形状 */}
      {currentShape && renderShape(currentShape)}
      
      {/* 绑定鼠标事件到stage */}
      {stageRef.current && (
        <>
          {stageRef.current.on('mousedown touchstart', handleMouseDown)}
          {stageRef.current.on('mousemove touchmove', handleMouseMove)}
          {stageRef.current.on('mouseup touchend', handleMouseUp)}
        </>
      )}
    </>
  );
};
