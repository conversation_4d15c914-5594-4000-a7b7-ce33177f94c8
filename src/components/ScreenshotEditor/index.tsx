import React, { useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Button,
  Box,
  Toolbar,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  CropFree as CropIcon,
  Rectangle as RectangleIcon,
  ArrowForward as ArrowIcon,
  TextFields as TextIcon,
  Brush as BrushIcon,
  PanTool as SelectIcon,
  Check as CheckIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { EditorCanvas } from './EditorCanvas';
import { ColorPicker } from './ColorPicker';
import { useEditorStore } from '../../store/editorStore';
import { EditorProps, ToolType } from '../../types/editor';
import { exportCanvasAsDataURL, validateExportParams } from '../../utils/canvasExport';
import { invoke } from '@tauri-apps/api/core';

export const ScreenshotEditor: React.FC<EditorProps> = ({
  imagePath,
  onSave,
  onCancel,
  initialCropArea
}) => {
  const [currentColor, setCurrentColor] = React.useState('#ff0000');
  const [currentStrokeWidth, setCurrentStrokeWidth] = React.useState(2);
  const [isSaving, setIsSaving] = React.useState(false);
  const stageRef = React.useRef<any>(null);
  const {
    currentTool,
    setCurrentTool,
    history,
    historyIndex,
    undo,
    redo,
    cropArea,
    setCropArea,
    saveToHistory
  } = useEditorStore();

  // 初始化编辑器
  useEffect(() => {
    if (initialCropArea) {
      // TODO: 设置初始裁剪区域
    }
  }, [initialCropArea]);

  const handleToolChange = (tool: ToolType) => {
    setCurrentTool(tool);
  };

  const handleCropConfirm = () => {
    if (cropArea) {
      saveToHistory();
      setCurrentTool('select');
    }
  };

  const handleCropCancel = () => {
    setCropArea(null);
    setCurrentTool('select');
  };

  const handleSave = async () => {
    if (!stageRef.current || isSaving) return;

    setIsSaving(true);
    try {
      const stage = stageRef.current;

      // Validate export parameters
      const validation = validateExportParams(stage, cropArea);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Export canvas as data URL
      const dataURL = exportCanvasAsDataURL(stage, cropArea);

      // Save via Tauri backend
      const result = await invoke<{ path: string; width: number; height: number }>('save_edited_screenshot', {
        imageDataUrl: dataURL,
        finalPath: null // Let backend generate default path
      });

      console.log('Screenshot saved successfully:', result);
      onSave(result.path);
    } catch (error) {
      console.error('Failed to save screenshot:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`保存失败: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  const tools = [
    { id: 'select' as ToolType, icon: SelectIcon, label: '选择' },
    { id: 'crop' as ToolType, icon: CropIcon, label: '裁剪' },
    { id: 'rectangle' as ToolType, icon: RectangleIcon, label: '矩形' },
    { id: 'arrow' as ToolType, icon: ArrowIcon, label: '箭头' },
    { id: 'text' as ToolType, icon: TextIcon, label: '文字' },
    { id: 'pen' as ToolType, icon: BrushIcon, label: '画笔' }
  ];

  return (
    <Dialog
      open={true}
      onClose={onCancel}
      maxWidth={false}
      fullWidth
      PaperProps={{
        sx: {
          width: '90vw',
          height: '90vh',
          maxWidth: 'none',
          maxHeight: 'none'
        }
      }}
    >
      <DialogTitle sx={{ p: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>截图编辑器 {isSaving && '- 保存中...'}</Box>
          <IconButton onClick={onCancel} size="small" disabled={isSaving}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      {/* 工具栏 */}
      <Toolbar variant="dense" sx={{ borderBottom: 1, borderColor: 'divider' }}>
        {/* 撤销重做 */}
        <Tooltip title="撤销">
          <span>
            <IconButton 
              onClick={undo} 
              disabled={!canUndo}
              size="small"
            >
              <UndoIcon />
            </IconButton>
          </span>
        </Tooltip>
        
        <Tooltip title="重做">
          <span>
            <IconButton 
              onClick={redo} 
              disabled={!canRedo}
              size="small"
            >
              <RedoIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 工具按钮 */}
        {tools.map((tool) => {
          const IconComponent = tool.icon;
          return (
            <Tooltip key={tool.id} title={tool.label}>
              <IconButton
                onClick={() => handleToolChange(tool.id)}
                color={currentTool === tool.id ? 'primary' : 'default'}
                size="small"
                sx={{
                  backgroundColor: currentTool === tool.id ? 'action.selected' : 'transparent'
                }}
              >
                <IconComponent />
              </IconButton>
            </Tooltip>
          );
        })}

        {/* 裁剪工具的确认和取消按钮 */}
        {currentTool === 'crop' && (
          <>
            <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
            <Tooltip title="确认裁剪">
              <IconButton
                onClick={handleCropConfirm}
                color="success"
                size="small"
                sx={{ backgroundColor: 'success.light', color: 'success.contrastText' }}
              >
                <CheckIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="取消裁剪">
              <IconButton
                onClick={handleCropCancel}
                color="error"
                size="small"
                sx={{ backgroundColor: 'error.light', color: 'error.contrastText' }}
              >
                <CancelIcon />
              </IconButton>
            </Tooltip>
          </>
        )}

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 颜色选择器 */}
        <ColorPicker
          currentColor={currentColor}
          currentStrokeWidth={currentStrokeWidth}
          onColorChange={setCurrentColor}
          onStrokeWidthChange={setCurrentStrokeWidth}
        />

        <Box sx={{ flexGrow: 1 }} />

        {/* 保存按钮 */}
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={isSaving}
          size="small"
        >
          {isSaving ? '保存中...' : '保存'}
        </Button>
      </Toolbar>

      <DialogContent sx={{ p: 0, flex: 1 }}>
        <EditorCanvas
          imagePath={imagePath}
          currentColor={currentColor}
          currentStrokeWidth={currentStrokeWidth}
          stageRef={stageRef}
        />
      </DialogContent>
    </Dialog>
  );
};
