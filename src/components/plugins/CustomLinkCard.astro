---
// copied from LinkCard.Astro
// removed icon
// added a footer field to place the supported platform icons

import type { HTMLAttributes } from 'astro/types';

interface Props extends Omit<HTMLAttributes<'a'>, 'title'> {
  title: string;
  description?: string;
  footer?: string;
}

const { title, description, footer, ...attributes } = Astro.props;
---

<div class="sl-link-card">
  <span class="sl-flex stack">
    <a {...attributes}>
      <span class="title" set:html={title} />
    </a>
    {description && <p class="description" set:html={description} />}
    {footer && <div style="pointer-events:none" class="footer" set:html={footer} />}
  </span>
</div>

<style>
  .footer {
    display: inline-flex;
    align-items: end;
    flex-direction: row;
    position: absolute;
    bottom: 1em;
    left: 1em;
  }

  .sl-link-card {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
    border: 1px solid var(--sl-color-gray-5);
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: var(--sl-shadow-sm);
    position: relative;
    /* space for icons */
    padding-bottom: 5em;
  }

  a {
    text-decoration: none;
    line-height: var(--sl-line-height-headings);
  }

  /* a11y fix for https://github.com/withastro/starlight/issues/487 */
  a::before {
    content: '';
    position: absolute;
    inset: 0;
  }

  .stack {
    flex-direction: column;
    gap: 0.5rem;
  }

  .title {
    color: var(--sl-color-white);
    font-weight: 600;
    font-size: var(--sl-text-lg);
  }

  .description {
    color: var(--sl-color-gray-3);
    line-height: 1.5;
  }

  .icon {
    color: var(--sl-color-gray-3);
  }

  /* Hover state */
  .sl-link-card:hover {
    background: var(--sl-color-gray-7, var(--sl-color-gray-6));
    border-color: var(--sl-color-gray-2);
  }

  .sl-link-card:hover .icon {
    color: var(--sl-color-white);
  }
</style>
