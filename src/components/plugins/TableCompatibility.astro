---
import { getSupportText, getSupportIcon, type PluginData } from './_helpers';
// table generated by build:compatibility-table command
import data from './_tableContent.json';
import { createMarkdownProcessor } from '@astrojs/markdown-remark';

const md = await createMarkdownProcessor();

const pluginData: Record<string, PluginData> = data;

const platforms = Array.from(
  new Set(Object.values(pluginData).flatMap((info) => info.support.map((s) => s.platform)))
).sort();

function getTableData(data: Record<string, PluginData>) {
  return Object.entries(data).map(([plugin, info]) => {
    const row: Record<string, any> = { plugin, rustVersion: info.rustVersion };
    info.support.forEach((support) => {
      row[support.platform] = {
        level: support.level,
        levelIcon: getSupportIcon(support.level),
        notes: support.notes,
      };
    });
    return row;
  });
}
// TODO: support markdown on notes: have to change the table structure because as of now the notes are a tooltip

const tableData = getTableData(pluginData);
---

<table>
  <thead>
    <tr>
      <th>Plugin</th>
      <th>Rust Version</th>
      {platforms.map((platform) => <th>{platform}</th>)}
    </tr>
  </thead>
  <tbody>
    {
      tableData.map((row) => {
        const { plugin, rustVersion } = row;
        return (
          <tr>
            <td>{plugin}</td>
            <td>{rustVersion}</td>
            {platforms.map((platform) => {
              const { notes, level, levelIcon } = row[platform];
              let title = getSupportText(level);

              if (notes) {
                title = `${title}:  ${notes}`;
              }
              return (
                <td>
                  {platform && (
                    <div class="support" title={title}>
                      <div class="icon" set:html={levelIcon} />
                      {notes && '*'}
                    </div>
                  )}
                </td>
              );
            })}
          </tr>
        );
      })
    }
  </tbody>
</table>

<style>
  table {
    width: 100%;
  }

  .support {
    cursor: help;
  }
  .icon {
    width: 1rem;
    display: inline-block;
  }
  .notes {
    margin-left: 0.5rem;
    font-size: 0.8em;
  }
</style>
