import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Typography,
  Box,
  Grid,
  IconButton,
  Chip,
  Tooltip,
  CardMedia,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Share as ShareIcon,
  CloudUpload as SyncIcon,
  CheckCircle as SyncedIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useScreenshotStore } from '../store/screenshotStore';
import { convertFileSrc } from '@tauri-apps/api/core';

export const RecentScreenshots: React.FC = () => {
  const { 
    recentScreenshots, 
    deleteScreenshot, 
    setSelectedScreenshot,
    updateSyncStatus 
  } = useScreenshotStore();

  const handleEdit = (screenshot: any) => {
    setSelectedScreenshot(screenshot);
    // TODO: Open editor
  };

  const handleDelete = (id: string) => {
    deleteScreenshot(id);
  };

  const handleSync = async (screenshot: any) => {
    updateSyncStatus(screenshot.id, 'syncing');
    try {
      // TODO: Implement actual sync logic
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate sync
      updateSyncStatus(screenshot.id, 'synced');
    } catch (error) {
      updateSyncStatus(screenshot.id, 'error');
    }
  };

  const getSyncIcon = (status: string) => {
    switch (status) {
      case 'syncing':
        return <SyncIcon color="action" />;
      case 'synced':
        return <SyncedIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <SyncIcon color="disabled" />;
    }
  };

  const getSyncColor = (status: string) => {
    switch (status) {
      case 'syncing':
        return 'warning';
      case 'synced':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  if (recentScreenshots.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Screenshots
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No screenshots yet. Capture your first screenshot above!
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Recent Screenshots
        </Typography>
        
        <Grid container spacing={2}>
          {recentScreenshots.map((screenshot) => (
            <Grid item xs={12} sm={6} md={4} key={screenshot.id}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardMedia
                  component="img"
                  height="120"
                  image={convertFileSrc(screenshot.path)}
                  alt={screenshot.name || 'Screenshot'}
                  sx={{ 
                    objectFit: 'cover',
                    cursor: 'pointer',
                    '&:hover': {
                      opacity: 0.8,
                    }
                  }}
                  onClick={() => handleEdit(screenshot)}
                />
                
                <CardContent sx={{ p: 1.5 }}>
                  <Typography variant="body2" noWrap sx={{ mb: 1 }}>
                    {screenshot.name || 'Untitled'}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(screenshot.createdAt).toLocaleString()}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {screenshot.tags.map((tag) => (
                        <Chip
                          key={tag}
                          label={tag}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                      ))}
                    </Box>
                    
                    <Chip
                      icon={getSyncIcon(screenshot.syncStatus)}
                      label={screenshot.syncStatus}
                      size="small"
                      color={getSyncColor(screenshot.syncStatus) as any}
                      variant="outlined"
                      sx={{ fontSize: '0.7rem', height: 20 }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Box>
                      <Tooltip title="Edit">
                        <IconButton size="small" onClick={() => handleEdit(screenshot)}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Sync to cloud">
                        <IconButton 
                          size="small" 
                          onClick={() => handleSync(screenshot)}
                          disabled={screenshot.syncStatus === 'syncing'}
                        >
                          <SyncIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Share">
                        <IconButton size="small">
                          <ShareIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    
                    <Tooltip title="Delete">
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleDelete(screenshot.id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};
