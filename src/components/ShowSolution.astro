---
let { text } = Astro.props;
text = text ?? 'Show solution';
---

<show-solution class="show-solution">
  <button>{text}</button>
  <div class="hidden">
    <slot />
  </div>
</show-solution>

<script>
  class ShowSolution extends HTMLElement {
    #contentDiv: HTMLDivElement | null = null;
    #button: HTMLButtonElement | null = null;
    constructor() {
      super();
      this.#button = this.querySelector('button');
      this.#button?.addEventListener('click', this.#toggle);
      this.#contentDiv = this.querySelector('div');
    }
    #toggle = () => {
      if (this.#button)
        if (this.#contentDiv?.classList.contains('hidden')) {
          this.#contentDiv.classList.remove('hidden');
          this.#button.innerText = this.#button.innerText.replace('Show', 'Hide');
        } else {
          this.#contentDiv?.classList.add('hidden');
          this.#button.innerText = this.#button.innerText.replace('Hide', 'Show');
        }
    };
  }
  customElements.define('show-solution', ShowSolution);
</script>

<style>
  .show-solution {
    display: block;
    width: 100%;
    margin-top: 1rem;
  }
  .show-solution button {
    width: 100%;
    background-color: var(--sl-color-gray-6);
    border: 1px solid var(--sl-color-gray-5);
    border-radius: 0.5rem;
    cursor: pointer;
  }
  .show-solution button:hover {
    background-color: var(--sl-color-gray-5);
    border: 1px solid var(--sl-color-gray-4);
  }
  .show-solution button:active {
    background-color: var(--sl-color-gray-4);
    border: 1px solid var(--sl-color-gray-3);
  }
</style>

<style is:global>
  .hidden {
    display: none !important;
  }
</style>
