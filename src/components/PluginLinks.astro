---
type Plugin = 'notification';

interface Props {
  plugin: Plugin;
  showJsLinks?: boolean;
}
const { plugin, showJsLinks = true } = Astro.props;
const github = `https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/${plugin}`;
const npm = showJsLinks ? `https://www.npmjs.com/package/@tauri-apps/plugin-${plugin}` : undefined;
const cratesio = `https://crates.io/crates/tauri-plugin-${plugin}`;
const jsApi = showJsLinks ? `/reference/javascript/${plugin}/` : undefined;
const docsrs = `https://docs.rs/tauri-plugin-${plugin}`;
const hasApiLinks = jsApi || docsrs;
---

<div class="flex row not-content">
  <a href={github} class="flex" target="_blank">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path
        d="M12,2.2467A10.00042,10.00042,0,0,0,8.83752,21.73419c.5.08752.6875-.21247.6875-.475,0-.23749-.01251-1.025-.01251-1.86249C7,19.85919,6.35,18.78423,6.15,18.22173A3.636,3.636,0,0,0,5.125,16.8092c-.35-.1875-.85-.65-.01251-.66248A2.00117,2.00117,0,0,1,6.65,17.17169a2.13742,2.13742,0,0,0,2.91248.825A2.10376,2.10376,0,0,1,10.2,16.65923c-2.225-.25-4.55-1.11254-4.55-4.9375a3.89187,3.89187,0,0,1,1.025-2.6875,3.59373,3.59373,0,0,1,.1-2.65s.83747-.26251,2.75,1.025a9.42747,9.42747,0,0,1,5,0c1.91248-1.3,2.75-1.025,2.75-1.025a3.59323,3.59323,0,0,1,.1,2.65,3.869,3.869,0,0,1,1.025,2.6875c0,3.83747-2.33752,4.6875-4.5625,4.9375a2.36814,2.36814,0,0,1,.675,1.85c0,1.33752-.01251,2.41248-.01251,2.75,0,.26251.1875.575.6875.475A10.0053,10.0053,0,0,0,12,2.2467Z"
      ></path>
    </svg>GitHub</a
  >
  {
    npm && (
      <a href={npm} class="flex" target="_blank">
        <svg viewBox="0 0 24 12" fill="currentColor">
          <path d="M4 4V8H6V5H7V8H8V4H4ZM9 4V9H11V8H13V4H9ZM12 5V7H11V5H12ZM14 4V8H16V5H17V8H18V5H19V8H20V4H14ZM3 3H21V9H12V10H8V9H3V3Z" />
        </svg>
        npm
      </a>
    )
  }
  {
    cratesio && (
      <a href={cratesio} class="flex" target="_blank">
        <svg viewBox="0 0 14 16" fill="currentColor">
          <path d="M11.2468 4.54809L7 2L2.77581 4.53452L7.02308 6.98668L11.2468 4.54809ZM12 6.42265L8 8.73205V13.4L12 11V6.42265ZM2 6.396V11L6 13.4V8.7054L2 6.396ZM7 0L14 4V12L7 16L0 12V4L7 0Z" />
        </svg>
        crates.io
      </a>
    )
  }
  {
    hasApiLinks && (
      <div class="flex api">
        API Reference
        {jsApi && (
          <a href={jsApi} class="flex" target="_blank">
            <svg viewBox="0 0 256 256" aria-label="JavaScript API Reference">
              <g clip-path="url(#clip0_2_2)">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M256 0H0V256H256V0ZM86.902 202.076L67.312 213.932L67.311 213.93C72.982 225.959 84.153 235.926 103.398 235.926C124.708 235.926 139.314 224.584 139.314 199.667V117.529H115.257V199.327C115.257 211.355 110.272 214.447 102.367 214.447C94.12 214.447 90.682 208.777 86.902 202.076ZM171.969 200.013L152.381 211.354L152.38 211.355C159.425 225.275 173.86 235.929 196.199 235.929C219.054 235.929 236.066 224.073 236.066 202.419C236.066 182.315 224.553 173.38 204.103 164.615L198.089 162.038C187.779 157.57 183.311 154.647 183.311 147.43C183.311 141.588 187.779 137.12 194.825 137.12C201.699 137.12 206.165 140.041 210.29 147.43L229.022 135.4C221.118 121.481 210.12 116.153 194.826 116.153C173.345 116.153 159.598 129.901 159.598 147.945C159.598 167.535 171.111 176.815 188.468 184.202L194.481 186.782C205.479 191.592 212.009 194.514 212.009 202.762C212.009 209.636 205.653 214.62 195.684 214.62C183.828 214.62 177.126 208.434 171.969 200.013Z"
                  fill="currentColor"
                />
              </g>
              <defs>
                <clipPath id="clip0_2_2">
                  <rect width="256" height="256" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </a>
        )}
        {docsrs && (
          <a href={docsrs} class="flex" target="_blank" aria-label="Docs.io API Reference">
            <svg viewBox="0 0 256 256" fill="currentColor">
              <path d="m254.251 124.862l-10.747-6.653a145.81 145.81 0 0 0-.306-3.13l9.236-8.615a3.686 3.686 0 0 0 1.105-3.427a3.685 3.685 0 0 0-2.33-2.744l-11.807-4.415a137.355 137.355 0 0 0-.925-3.048l7.365-10.229a3.698 3.698 0 0 0-2.407-5.814l-12.45-2.025c-.484-.944-.988-1.874-1.496-2.796l5.231-11.483a3.683 3.683 0 0 0-.288-3.59a3.678 3.678 0 0 0-3.204-1.642l-12.636.44a99.848 99.848 0 0 0-1.996-2.421l2.904-12.308a3.694 3.694 0 0 0-.986-3.466a3.698 3.698 0 0 0-3.464-.986l-12.305 2.901a106.192 106.192 0 0 0-2.426-1.996l.442-12.635a3.684 3.684 0 0 0-1.64-3.205a3.693 3.693 0 0 0-3.59-.29l-11.48 5.234a133.235 133.235 0 0 0-2.796-1.5l-2.03-12.452a3.7 3.7 0 0 0-5.812-2.407l-10.236 7.365c-1.007-.32-2.02-.629-3.042-.922L155.72 4.794a3.69 3.69 0 0 0-2.745-2.336a3.707 3.707 0 0 0-3.424 1.106l-8.615 9.243a111.11 111.11 0 0 0-3.13-.306l-6.653-10.75a3.698 3.698 0 0 0-6.289 0l-6.653 10.75a110.4 110.4 0 0 0-3.133.306l-8.617-9.243a3.695 3.695 0 0 0-6.169 1.23l-4.414 11.809c-1.023.293-2.035.604-3.045.922L82.599 10.16a3.687 3.687 0 0 0-3.579-.415a3.705 3.705 0 0 0-2.235 2.822l-2.03 12.452c-.94.487-1.869.988-2.796 1.5l-11.481-5.235a3.686 3.686 0 0 0-3.588.291a3.684 3.684 0 0 0-1.642 3.205l.44 12.635a118.03 118.03 0 0 0-2.426 1.996l-12.305-2.9a3.71 3.71 0 0 0-3.466.985a3.694 3.694 0 0 0-.986 3.466l2.899 12.308c-.673.797-1.338 1.604-1.991 2.421l-12.636-.44a3.721 3.721 0 0 0-3.204 1.641a3.696 3.696 0 0 0-.291 3.59l5.234 11.484c-.509.922-1.012 1.852-1.5 2.796l-12.449 2.025a3.7 3.7 0 0 0-2.407 5.814l7.365 10.23c-.32 1.01-.631 2.024-.925 3.047l-11.808 4.415a3.702 3.702 0 0 0-1.225 6.171l9.237 8.614c-.115 1.04-.217 2.087-.305 3.131L1.75 124.862A3.695 3.695 0 0 0 0 128.007c0 1.284.663 2.473 1.751 3.143l10.748 6.653c.088 1.047.19 2.092.305 3.131l-9.238 8.617a3.697 3.697 0 0 0 1.226 6.169l11.808 4.415c.294 1.022.605 2.037.925 3.047l-7.365 10.231a3.696 3.696 0 0 0 2.41 5.812l12.447 2.025c.487.944.986 1.874 1.5 2.8l-5.235 11.48a3.691 3.691 0 0 0 .291 3.59a3.684 3.684 0 0 0 3.204 1.641l12.63-.442c.659.821 1.322 1.626 1.997 2.426l-2.899 12.31a3.682 3.682 0 0 0 .986 3.459a3.683 3.683 0 0 0 3.466.983l12.305-2.898c.8.68 1.61 1.34 2.427 1.99l-.44 12.639a3.694 3.694 0 0 0 5.229 3.492l11.481-5.231a105.49 105.49 0 0 0 2.796 1.499l2.03 12.445a3.692 3.692 0 0 0 2.235 2.825a3.706 3.706 0 0 0 3.579-.413l10.229-7.37c1.01.32 2.025.633 3.047.927l4.415 11.804a3.685 3.685 0 0 0 2.744 2.331a3.677 3.677 0 0 0 3.425-1.106l8.617-9.238c1.04.12 2.086.22 3.133.313l6.653 10.748a3.702 3.702 0 0 0 3.143 1.75a3.703 3.703 0 0 0 3.145-1.75l6.653-10.748c1.047-.093 2.092-.193 3.131-.313l8.615 9.238a3.68 3.68 0 0 0 3.424 1.106a3.69 3.69 0 0 0 2.744-2.331l4.415-11.804c1.022-.294 2.038-.607 3.048-.927l10.231 7.37a3.7 3.7 0 0 0 5.812-2.412l2.03-12.445c.939-.487 1.868-.993 2.795-1.5l11.481 5.232a3.692 3.692 0 0 0 5.23-3.492l-.44-12.638a98.76 98.76 0 0 0 2.423-1.991l12.306 2.898c1.25.294 2.56-.07 3.463-.983a3.682 3.682 0 0 0 .986-3.459l-2.898-12.31c.675-.8 1.34-1.605 1.99-2.426l12.636.442a3.681 3.681 0 0 0 3.204-1.64a3.685 3.685 0 0 0 .289-3.592l-5.232-11.478c.511-.927 1.013-1.857 1.497-2.8l12.45-2.026a3.682 3.682 0 0 0 2.822-2.236a3.696 3.696 0 0 0-.415-3.576l-7.365-10.23c.318-1.011.629-2.026.925-3.048l11.806-4.415a3.684 3.684 0 0 0 2.331-2.745a3.677 3.677 0 0 0-1.106-3.424l-9.235-8.617c.112-1.04.215-2.086.305-3.13l10.748-6.654a3.69 3.69 0 0 0 1.751-3.143c0-1.281-.66-2.472-1.749-3.145Zm-71.932 89.156c-4.104-.885-6.714-4.93-5.833-9.047c.878-4.112 4.92-6.729 9.023-5.844c4.104.879 6.718 4.931 5.838 9.04c-.88 4.11-4.926 6.73-9.028 5.851Zm-3.652-24.699a6.929 6.929 0 0 0-8.23 5.332l-3.816 17.807c-11.775 5.344-24.85 8.313-38.621 8.313c-14.086 0-27.446-3.116-39.43-8.688l-3.814-17.806c-.802-3.747-4.486-6.134-8.228-5.33l-15.72 3.376a93.272 93.272 0 0 1-8.128-9.58h76.49c.865 0 1.442-.157 1.442-.945v-27.057c0-.787-.577-.944-1.443-.944H106.8v-17.15h24.195c2.208 0 11.809.63 14.878 12.902c.962 3.774 3.072 16.05 4.516 19.98c1.438 4.408 7.293 13.213 13.533 13.213h38.115c.433 0 .895-.049 1.382-.137a93.92 93.92 0 0 1-8.669 10.17l-16.082-3.456Zm-105.79 24.327c-4.105.886-8.146-1.731-9.029-5.843c-.878-4.119 1.732-8.162 5.836-9.047a7.607 7.607 0 0 1 9.028 5.85c.878 4.11-1.734 8.16-5.836 9.04ZM43.86 95.986c1.703 3.842-.03 8.345-3.867 10.045c-3.837 1.705-8.328-.03-10.03-3.875a7.615 7.615 0 0 1 3.867-10.045a7.598 7.598 0 0 1 10.03 3.874Zm-8.918 21.14l16.376-7.277a6.942 6.942 0 0 0 3.524-9.158l-3.372-7.626h13.264v59.788H37.973a93.7 93.7 0 0 1-3.566-25.672c0-3.398.183-6.756.535-10.056Zm71.862-5.807V93.696h31.586c1.632 0 11.52 1.886 11.52 9.28c0 6.139-7.584 8.34-13.821 8.34h-29.285v.003Zm114.792 15.862c0 2.338-.086 4.652-.257 6.948h-9.603c-.961 0-1.348.632-1.348 1.573v4.41c0 10.38-5.853 12.638-10.982 13.213c-4.884.55-10.3-2.045-10.967-5.034c-2.882-16.206-7.683-19.667-15.265-25.648c9.41-5.975 19.2-14.79 19.2-26.59c0-12.74-8.734-20.765-14.688-24.7c-8.352-5.506-17.6-6.61-20.095-6.61H58.279c13.467-15.03 31.719-25.677 52.362-29.551l11.706 12.28a6.923 6.923 0 0 0 9.799.226l13.098-12.528c27.445 5.11 50.682 22.194 64.073 45.633l-8.967 20.253c-1.548 3.505.032 7.604 3.527 9.157l17.264 7.668c.298 3.065.455 6.161.455 9.3ZM122.352 24.745c3.033-2.905 7.844-2.79 10.748.247c2.898 3.046 2.788 7.862-.252 10.765c-3.033 2.906-7.844 2.793-10.748-.25a7.621 7.621 0 0 1 .252-10.762Zm88.983 71.61a7.594 7.594 0 0 1 10.028-3.872c3.838 1.702 5.57 6.203 3.867 10.045a7.595 7.595 0 0 1-10.03 3.875c-3.833-1.703-5.565-6.2-3.865-10.048Z" />
            </svg>
          </a>
        )}
      </div>
    )
  }
</div>

<style>
  .flex {
    display: flex;
    font-size: var(--sl-text-sm);
    flex-wrap: wrap;
    align-items: center;
  }

  .flex.row {
    gap: 0.5em 1.5em;
  }

  a {
    gap: 0.5em;
    color: var(--sl-color-text);
    text-decoration: none;
  }
  a.flex:hover {
    color: var(--sl-color-text-accent);
    text-decoration: underline;
  }
  svg {
    height: calc(var(--sl-text-sm) * 1.5);
  }
  .flex.api {
    gap: 1em;
  }
</style>
