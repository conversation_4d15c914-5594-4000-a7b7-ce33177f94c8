---
import MobileMenuToggle from 'virtual:starlight/components/MobileMenuToggle';
import type { Props } from '@astrojs/starlight/props';
import Sidebar from '@lorenzo_lewis/starlight-utils/components/Sidebar.astro';
const { t } = Astro.locals;
---

<div class="page sl-flex">
  <header class="header"><slot name="header" /></header>
  <nav class="sidebar" aria-label={t('sidebarNav.accessibleLabel')}>
    <MobileMenuToggle {...Astro.props} />
    <div
      id="starlight__sidebar"
      class={Astro.props.entry.slug === '' || Astro.props.locale === Astro.props.entry.slug
        ? 'sidebar-pane lp-hide'
        : 'sidebar-pane'}
    >
      <div class="sidebar-content sl-flex">
        <Sidebar {...Astro.props} />
      </div>
    </div>
  </nav>
  <div class="main-frame"><slot /></div>
</div>

<style>
  .page {
    flex-direction: column;
    min-height: 100vh;
  }

  @media (min-width: 50rem) {
    .lp-hide {
      display: none;
    }
  }

  .header {
    z-index: var(--sl-z-index-navbar);
    position: fixed;
    inset-inline-start: 0;
    inset-block-start: 0;
    width: 100%;
    height: var(--sl-nav-height);
    border-bottom: 1px solid var(--sl-color-hairline-shade);
    padding: var(--sl-nav-pad-y) var(--sl-nav-pad-x);
    padding-inline-end: var(--sl-nav-pad-x);
    background-color: var(--sl-color-bg-nav);
  }

  :global([data-has-sidebar]) .header {
    padding-inline-end: calc(var(--sl-nav-gap) + var(--sl-nav-pad-x) + var(--sl-menu-button-size));
  }

  .sidebar-pane {
    visibility: var(--sl-sidebar-visibility, hidden);
    position: fixed;
    z-index: var(--sl-z-index-menu);
    inset-block: var(--sl-nav-height) 0;
    inset-inline-start: 0;
    width: 100%;
    background-color: var(--sl-color-black);
    overflow-y: auto;
  }

  :global([aria-expanded='true']) ~ .sidebar-pane {
    --sl-sidebar-visibility: visible;
  }

  .sidebar-content {
    height: 100%;
    min-height: max-content;
    padding: 1rem var(--sl-sidebar-pad-x) 0;
    flex-direction: column;
    gap: 1rem;
  }

  @media (min-width: 50rem) {
    .sidebar-content::after {
      content: '';
      padding-bottom: 1px;
    }
  }

  .main-frame {
    padding-top: calc(var(--sl-nav-height) + var(--sl-mobile-toc-height));
    padding-inline-start: var(--sl-content-inline-start);
  }

  @media (min-width: 50rem) {
    :global([data-has-sidebar]) .header {
      padding-inline-end: var(--sl-nav-pad-x);
    }
    .sidebar-pane {
      --sl-sidebar-visibility: visible;
      width: var(--sl-sidebar-width);
      background-color: var(--sl-color-bg-sidebar);
      border-inline-end: 1px solid var(--sl-color-hairline-shade);
    }
  }
</style>
