---
import type { Props } from '@astrojs/starlight/props';
import { Icon } from '@astrojs/starlight/components';
---

<script>
  class ThemeSwitcher extends HTMLElement {
    constructor() {
      super();
      const storedTheme =
        typeof localStorage !== 'undefined' && localStorage.getItem('starlight-theme');
      const theme =
        storedTheme ||
        (window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark');
      document.documentElement.dataset.theme = theme === 'light' ? 'light' : 'dark';
      this.handleMouseDown = this.handleMouseDown.bind(this);
    }
    connectedCallback() {
      this.addEventListener('click', this.handleMouseDown);
    }
    disconnectedCallback() {
      this.removeEventListener('click', this.handleMouseDown);
    }
    private handleMouseDown(e: MouseEvent) {
      const theme = document.documentElement.dataset.theme === 'light' ? 'dark' : 'light';
      document.documentElement.dataset.theme = theme;
      localStorage.setItem('starlight-theme', theme);
    }
  }
  customElements.define('theme-switcher', ThemeSwitcher);
</script>

<theme-switcher class="sl-flex">
  <Icon name="sun" class="theme-selector-light" />
  <Icon name="moon" class="theme-selector-dark" />
</theme-switcher>

<style>
  theme-switcher {
    align-items: center;
  }
  .theme-selector-light,
  .theme-selector-dark {
    user-select: none;
    z-index: 999999;
    position: relative;
    cursor: pointer;
  }
  .theme-selector-light:hover,
  .theme-selector-dark:hover {
    color: var(--sl-color-accent-high);
  }
  :root {
    .theme-selector-light {
      display: none;
    }
    .theme-selector-dark {
      display: inline-block;
    }
  }
  :root[data-theme='light'] {
    .theme-selector-light {
      display: inline-block;
    }
    .theme-selector-dark {
      display: none;
    }
    .theme-selector-light:hover,
    .theme-selector-dark:hover {
      color: var(--sl-color-accent);
    }
  }
</style>
