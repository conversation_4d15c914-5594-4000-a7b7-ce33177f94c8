---
import type { Props } from '@astrojs/starlight/props';
import LanguageSelect from '@astrojs/starlight/components/LanguageSelect.astro';
import Search from '@astrojs/starlight/components/Search.astro';
import SiteTitle from './SiteTitle.astro';
import SocialIcons from '@astrojs/starlight/components/SocialIcons.astro';
import ThemeSelect from './ThemeSelect.astro';

const curLocale = Astro.props.locale;


type Link = {
	title: string;
	value: string;
	transfer: boolean;
};

const links: Link[] = [
	{
		title: 'Guides',
		value: '/start/',
		transfer: true,
	},
	{
		title: 'References',
		value: '/reference/acl/capability/',
		transfer: true,
	},
	{
		title: 'Blog',
		value: '/blog/',
		transfer: false,
	},
	{
		title: 'Releases',
		value: '/release/',
		transfer: true,
	},
];
---

<div class="header sl-flex">
	<div class="full-width sl-flex">
		<SiteTitle {...Astro.props} />
	</div>
	<div class="sl-flex">
		<Search {...Astro.props} />
	</div>
	<div class="sl-hidden md:sl-flex right-group">
		<div class="desktop">
			{
				links.map((link) => (
					<a href={link.transfer && curLocale ? `/${curLocale}${link.value}` : link.value}>
						{link.title}
					</a>
				))
			}
		</div>
		<div class="sl-flex social-icons">
			<SocialIcons {...Astro.props} />
		</div>
		<LanguageSelect {...Astro.props} />
		<ThemeSelect {...Astro.props} />
	</div>
</div>

<style>
	.header {
		gap: var(--sl-nav-gap);
		justify-content: space-between;
		align-items: center;
		height: 100%;
	}
	.full-width {
		width: 100%;
	}
	.right-group,
	.social-icons {
		gap: 1rem;
		align-items: center;
	}
	.desktop,
	.release-link::after,
	.social-icons::after {
		content: '';
		height: 2rem;
		border-inline-end: 1px solid var(--sl-color-gray-5);
	}

	@media (min-width: 50rem) {
		:global(:root[data-has-sidebar]) {
			--__sidebar-pad: calc(2 * var(--sl-nav-pad-x));
		}
		:global(:root:not([data-has-toc])) {
			--__toc-width: 0rem;
		}
		.header {
			--__sidebar-width: max(0rem, var(--sl-content-inline-start, 0rem) - var(--sl-nav-pad-x));
			--__main-column-fr: calc(
				(
						100% + var(--__sidebar-pad, 0rem) - var(--__toc-width, var(--sl-sidebar-width)) -
							(2 * var(--__toc-width, var(--sl-nav-pad-x))) - var(--sl-content-inline-start, 0rem) -
							var(--sl-content-width)
					) / 2
			);
			display: grid;
			grid-template-columns:
        /* 1 (site title): runs up until the main content column’s left edge or the width of the title, whichever is the largest  */
				minmax(
					calc(var(--__sidebar-pad, 0rem)),
					auto
				)
				/* 2 (search box): all free space that is available. */
				1fr
				/* 3 (right items): use the space that these need. */
				auto;
			align-content: center;
		}
	}
	a:hover {
		color: var(--sl-color-gray-2);
	}
	:root[data-theme='light'] {
		.header a:hover {
			color: var(--sl-color-gray-3);
		}
	}
</style>
<style is:global>
	.header {
		border: none !important;
	}
body[data-mobile-menu-expanded] header {
	background-color: var(--sl-color-bg);
}
</style>

{
Astro.props.entry.slug === '' || Astro.props.locale === Astro.props.entry.slug ?
<style>
	.header {
		background-color: transparent;
    	-webkit-backdrop-filter: blur(16px);
		backdrop-filter: blur(16px);
		padding-inline-end: calc(var(--sl-nav-gap) + var(--sl-nav-pad-x));
	}

	@media (min-width: 50rem) {
		header {
			background-color: transparent !important;
		}
	}
	@media (min-width: 50rem) {
		.header {
			padding-inline-end: var(--sl-nav-pad-y);
		}
	}
</style>
: ''
}

<style>
	.desktop {
		display: none;
		align-items: center;
		gap: 1rem;
		padding-inline-end: 1rem;
	}
	@media (min-width: 72rem) {
		.desktop {
			display: flex;
		}
		.mobile {
			display: none;
		}
	}

	a {
		color: var(--sl-color-white);
		font-weight: 600;
		text-decoration: none;
	}

	/* From https://github.com/withastro/starlight/blob/main/packages/starlight/components/Select.astro */
	label {
		--sl-label-icon-size: 0.875rem;
		--sl-caret-size: 1.25rem;
		position: relative;
		display: flex;
		align-items: center;
		gap: 0.25rem;
		color: var(--sl-color-gray-1);
	}

	label:hover {
		color: var(--sl-color-gray-2);
	}

	.icon {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		pointer-events: none;
	}

	.label-icon {
		font-size: var(--sl-label-icon-size);
		inset-inline-start: 0;
	}

	.caret {
		font-size: var(--sl-caret-size);
		inset-inline-end: 0;
	}

	select {
		border: 0;
		padding-block: 0.625rem;
		padding-inline: calc(var(--sl-label-icon-size) + 0.25rem) calc(var(--sl-caret-size) + 0.25rem);
		width: var(--sl-select-width);
		background-color: transparent;
		text-overflow: ellipsis;
		color: inherit;
		cursor: pointer;
		appearance: none;
	}

	option {
		background-color: var(--sl-color-bg-nav);
		color: var(--sl-color-gray-1);
	}

	@media (min-width: 50rem) {
		select {
			font-size: var(--sl-text-sm);
		}
	}
</style>
