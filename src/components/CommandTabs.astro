---
import { Tabs, TabItem } from '@astrojs/starlight/components';
import { Code } from '@astrojs/starlight/components';

interface Props {
  npm?: string;
  yarn?: string;
  pnpm?: string;
  deno?: string;
  bun?: string;
  cargo?: string;
}

const { npm, yarn, pnpm, bun, cargo, deno } = Astro.props;
---

<Tabs syncKey="cmd">
  {
    npm && (
      <TabItem label="npm">
        <Code code={npm} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
  {
    yarn && (
      <TabItem label="yarn">
        <Code code={yarn} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
  {
    pnpm && (
      <TabItem label="pnpm">
        <Code code={pnpm} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
  {
    deno && (
      <TabItem label="deno">
        <Code code={deno} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
  {
    bun && (
      <TabItem label="bun">
        <Code code={bun} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
  {
    cargo && (
      <TabItem label="cargo">
        <Code code={cargo} lang="sh" theme="css-variables" frame="none" />
      </TabItem>
    )
  }
</Tabs>
