{"name": "mecap", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-clipboard-manager": "^2.3.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-log": "~2.6.0", "@tauri-apps/plugin-notification": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.0", "konva": "^9.3.20", "react": "^18.3.1", "react-dom": "^18.3.1", "react-konva": "^18.2.12", "use-image": "^1.1.4", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2.6.2", "@types/node": "^24.0.13", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.2.4"}}