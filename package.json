{"name": "tauri-docs", "type": "module", "private": true, "license": "MIT", "version": "0.0.1", "scripts": {"dev:setup:submodules": "git submodule update --init", "dev:setup:tauri": "pnpm install -C packages/tauri/packages/api --ignore-workspace --no-frozen-lockfile", "dev:setup:plugins-workspace": "pnpm -C packages/plugins-workspace install", "dev:setup": "pnpm dev:setup:submodules && pnpm dev:setup:tauri && pnpm dev:setup:plugins-workspace && pnpm build:compatibility-table", "dev": "astro dev", "format": "prettier -w --cache --plugin prettier-plugin-astro .", "format:check": "prettier -c --cache --plugin prettier-plugin-astro .", "build:compatibility-table": "pnpm --filter compatibility-table run build", "build:references": "pnpm --filter js-api-generator run build", "build:releases": "pnpm --filter releases-generator run build", "build:config": "pnpm --filter config-generator run build", "build:cli": "pnpm --filter cli-generator run build", "build:astro": "astro build", "build": "pnpm dev:setup && pnpm build:references && pnpm build:config && pnpm build:cli && pnpm build:releases && pnpm build:astro", "preview": "astro preview"}, "dependencies": {"@astrojs/markdown-remark": "^5.2.0", "@astrojs/rss": "^4.0.7", "@astrojs/starlight": "0.29.2", "@lorenzo_lewis/starlight-utils": "^0.2.0", "@lunariajs/core": "^0.1.1", "@lunariajs/starlight": "^0.1.1", "@types/json-schema": "^7.0.15", "astro": "^4.16.18", "astro-d2": "^0.6.0", "astro-feelback": "^0.3.4", "astrojs-service-worker": "^2.0.0", "jsdom": "^26.1.0", "prettier": "^3.2.5", "prettier-plugin-astro": "^0.14.0", "rehype-autolink-headings": "^7.1.0", "sass": "^1.89.0", "sharp": "^0.33.5", "shiki": "^3.0.0", "starlight-blog": "^0.15.0", "starlight-links-validator": "^0.13.0"}, "packageManager": "pnpm@10.12.4", "engines": {"pnpm": "^10.0.0"}, "pnpm": {"patchedDependencies": {"@astrojs/starlight@0.29.2": "patches/@<EMAIL>"}}}