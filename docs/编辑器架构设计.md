# Mecap 截图编辑器架构设计

## 技术选型

### 选择: Konva.js + react-konva

**核心理由:**
- **React集成**: react-konva提供完美的React组件化支持
- **性能优异**: 基于2D Canvas，渲染性能出色  
- **API简洁**: 学习曲线平缓，开发效率高
- **功能完备**: 支持所有需要的编辑功能
- **文档完善**: 丰富的示例和文档

## 组件架构

```
ScreenshotEditor/
├── index.tsx                 # 主编辑器组件
├── components/
│   ├── Canvas/
│   │   ├── EditorCanvas.tsx     # 主画布组件
│   │   ├── ImageLayer.tsx       # 图像层
│   │   ├── DrawingLayer.tsx     # 绘图层
│   │   └── SelectionLayer.tsx   # 选择层
│   ├── Toolbar/
│   │   ├── ToolbarContainer.tsx # 工具栏容器
│   │   ├── CropTool.tsx         # 裁剪工具
│   │   ├── DrawingTools.tsx     # 绘图工具
│   │   ├── TextTool.tsx         # 文字工具
│   │   └── ColorPicker.tsx      # 颜色选择器
│   └── Controls/
│       ├── UndoRedo.tsx         # 撤销重做
│       ├── SaveControls.tsx     # 保存控制
│       └── ZoomControls.tsx     # 缩放控制
├── hooks/
│   ├── useEditorState.ts        # 编辑器状态管理
│   ├── useDrawingTool.ts        # 绘图工具逻辑
│   ├── useCropTool.ts           # 裁剪工具逻辑
│   └── useHistory.ts            # 历史记录管理
├── types/
│   ├── editor.ts                # 编辑器类型定义
│   ├── tools.ts                 # 工具类型定义
│   └── shapes.ts                # 图形类型定义
└── utils/
    ├── imageUtils.ts            # 图像处理工具
    ├── exportUtils.ts           # 导出工具
    └── geometryUtils.ts         # 几何计算工具
```

## 数据流设计

### 状态管理 (Zustand)

```typescript
interface EditorState {
  // 编辑器状态
  currentTool: ToolType;
  isEditing: boolean;
  
  // 图像数据
  originalImage: string;
  editedImage: string | null;
  
  // 画布状态
  canvasSize: { width: number; height: number };
  zoom: number;
  
  // 绘图元素
  shapes: Shape[];
  selectedShapeId: string | null;
  
  // 历史记录
  history: HistoryState[];
  historyIndex: number;
  
  // 裁剪状态
  cropArea: CropArea | null;
  
  // Actions
  setCurrentTool: (tool: ToolType) => void;
  addShape: (shape: Shape) => void;
  updateShape: (id: string, updates: Partial<Shape>) => void;
  deleteShape: (id: string) => void;
  setCropArea: (area: CropArea | null) => void;
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
}
```

### 工具类型定义

```typescript
type ToolType = 
  | 'select'
  | 'crop' 
  | 'rectangle'
  | 'arrow'
  | 'text'
  | 'pen'
  | 'eraser';

interface Shape {
  id: string;
  type: 'rectangle' | 'arrow' | 'text' | 'path';
  x: number;
  y: number;
  width?: number;
  height?: number;
  points?: number[];
  text?: string;
  style: {
    stroke: string;
    strokeWidth: number;
    fill?: string;
    fontSize?: number;
    fontFamily?: string;
  };
}
```

## 核心功能实现

### 1. 裁剪工具
- 使用Konva.Transformer实现拖拽调整
- 实时预览裁剪区域
- 支持比例锁定

### 2. 绘图工具
- 矩形: Konva.Rect
- 箭头: 自定义Arrow组件
- 文字: Konva.Text + 编辑模式
- 自由绘制: Konva.Line

### 3. 历史记录
- 每次操作后保存状态快照
- 支持撤销/重做
- 限制历史记录数量(50条)

### 4. 导出功能
- 合并所有图层
- 支持PNG/JPG格式
- 保持原始分辨率

## 性能优化

1. **图层分离**: 背景图像、绘图元素、选择框分层渲染
2. **懒加载**: 大图像分块加载
3. **事件优化**: 防抖处理频繁的鼠标事件
4. **内存管理**: 及时清理不用的Canvas对象

## 集成方案

### 与现有工作流集成
1. 从ScreenshotPreview组件调用编辑器
2. 编辑完成后返回编辑结果
3. 更新截图存储和状态管理

### API设计
```typescript
interface EditorProps {
  imagePath: string;
  onSave: (editedImagePath: string) => void;
  onCancel: () => void;
  initialCropArea?: CropArea;
}
```

## 开发计划

1. **Phase 1**: 基础Canvas组件和图像显示
2. **Phase 2**: 裁剪工具实现
3. **Phase 3**: 绘图工具(矩形、箭头)
4. **Phase 4**: 文字工具和颜色选择
5. **Phase 5**: 历史记录和导出功能
6. **Phase 6**: 性能优化和集成测试
