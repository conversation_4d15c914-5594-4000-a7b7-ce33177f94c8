## 待解决问题
后续新增代码都使用Tauri 的log插件接口记录日志。
devtools控制台日志在 @devtools_console.log vs终端日志在 @vs_terminal.log，请读取


问题二：
    全局快捷键'Cmd+Shift+C'在应用非激活状态下无法触发截图。需要：
    1. 检查context7 Tauri的全局快捷键注册配置是否正确设置了system-wide属性
    2. 验证快捷键注册时机是否合适（建议在应用启动时就完成注册）
    3. 确保快捷键注册使用了正确的权限声明
    4. 添加快捷键注册状态的日志，便于问题定位

问题三：
    截图的编辑模式下，快捷操作工具栏需要优化布局：
    1. 工具栏应与选区保持固定的相对位置关系：
        - 默认贴附在选区边缘
        - 随选区移动自动保持相对位置不变
    2. 工具栏布局要求：
        - 在进入编辑模式截图显示完成后立即显示
        - 位置要便于操作但不遮挡选区内容
        - 支持用户拖动调整位置
    3. 交互体验要求：
        - 工具栏显示动画要流畅
        - 与选区联动移动要平滑
        - 确保工具栏始终在可视区域内

问题四：
    在智能窗口截图模式下，期望的处理是点击左键然后触发截图，但是当前代码好像是等待MouseUp的时候触发选择窗口并截图，情分析这个处理逻辑是否合理？要考虑到用户点击可能是为了拖拽画窗口

问题五：
    tauri的log持久化在哪里，没有找到，是否可能配置有问题

问题六：
    截图模式下，截图区域内鼠标还能长按左键选择文字，期望是进入截图模式后鼠标在截图上方处于禁用状态，直到用户选择快捷操作栏里对应的编辑工具，才允许在截图上进行编辑操作。
    这个问题是否会在实现快捷栏编辑功能的时候一并解决了？

