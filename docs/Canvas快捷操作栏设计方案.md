# Canvas快捷操作栏设计方案

## 概述

基于HTML5 Canvas实现的小型快捷操作栏，为Mecap截图功能提供直观、高效的工具访问界面。Canvas方案相比传统DOM具有更好的性能和视觉效果。

## 1. 设计理念

### 1.1 视觉设计原则
- **简洁明了**: 图标清晰，功能一目了然
- **现代化**: 采用扁平化设计，支持深色/浅色主题
- **响应式**: 适配不同DPI和屏幕尺寸
- **动画流畅**: 微交互增强用户体验

### 1.2 交互设计原则
- **就近原则**: 操作栏跟随鼠标或选区显示
- **快速访问**: 常用功能一键直达
- **渐进披露**: 高级功能通过展开菜单访问
- **键盘友好**: 支持快捷键操作

## 2. 功能模块设计

### 2.1 核心功能按钮
```javascript
const TOOLBAR_BUTTONS = [
    {
        id: 'capture',
        icon: '📷',
        label: '确认截图',
        shortcut: 'Enter',
        action: 'confirmCapture'
    },
    {
        id: 'cancel',
        icon: '❌',
        label: '取消',
        shortcut: 'Esc',
        action: 'cancelCapture'
    },
    {
        id: 'edit',
        icon: '✏️',
        label: '编辑',
        shortcut: 'E',
        action: 'openEditor'
    },
    {
        id: 'copy',
        icon: '📋',
        label: '复制',
        shortcut: 'Ctrl+C',
        action: 'copyToClipboard'
    },
    {
        id: 'save',
        icon: '💾',
        label: '保存',
        shortcut: 'Ctrl+S',
        action: 'saveToFile'
    },
    {
        id: 'more',
        icon: '⋯',
        label: '更多',
        action: 'showMoreOptions'
    }
];
```

### 2.2 扩展功能菜单
```javascript
const EXTENDED_MENU = [
    {
        id: 'pin',
        icon: '📌',
        label: '贴图',
        action: 'pinImage'
    },
    {
        id: 'ocr',
        icon: '🔤',
        label: '文字识别',
        action: 'performOCR'
    },
    {
        id: 'share',
        icon: '🔗',
        label: '分享',
        action: 'shareImage'
    },
    {
        id: 'settings',
        icon: '⚙️',
        label: '设置',
        action: 'openSettings'
    }
];
```

## 3. Canvas实现架构

### 3.1 核心类设计
```javascript
class CanvasToolbar {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.options = {
            theme: 'light',
            position: 'auto', // auto, top, bottom, left, right
            size: 'medium',   // small, medium, large
            animation: true,
            ...options
        };
        
        this.buttons = [];
        this.isVisible = false;
        this.isExpanded = false;
        this.hoveredButton = null;
        this.animationFrame = null;
        
        this.init();
    }
    
    init() {
        this.setupButtons();
        this.bindEvents();
        this.startRenderLoop();
    }
    
    setupButtons() {
        // 初始化按钮布局和属性
        this.calculateLayout();
        this.loadIcons();
    }
    
    bindEvents() {
        // 绑定鼠标和键盘事件
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('click', this.onClick.bind(this));
        this.canvas.addEventListener('mouseleave', this.onMouseLeave.bind(this));
        document.addEventListener('keydown', this.onKeyDown.bind(this));
    }
}
```

### 3.2 渲染系统
```javascript
class ToolbarRenderer {
    constructor(ctx, theme) {
        this.ctx = ctx;
        this.theme = theme;
        this.dpr = window.devicePixelRatio || 1;
    }
    
    render(toolbar) {
        this.clear();
        
        if (!toolbar.isVisible) return;
        
        // 绘制工具栏背景
        this.drawBackground(toolbar);
        
        // 绘制按钮
        toolbar.buttons.forEach(button => {
            this.drawButton(button, toolbar.hoveredButton === button);
        });
        
        // 绘制扩展菜单
        if (toolbar.isExpanded) {
            this.drawExtendedMenu(toolbar);
        }
        
        // 绘制工具提示
        if (toolbar.hoveredButton) {
            this.drawTooltip(toolbar.hoveredButton);
        }
    }
    
    drawBackground(toolbar) {
        const { x, y, width, height } = toolbar.bounds;
        
        // 绘制圆角矩形背景
        this.ctx.save();
        this.ctx.fillStyle = this.theme.backgroundColor;
        this.ctx.shadowColor = this.theme.shadowColor;
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetY = 2;
        
        this.roundRect(x, y, width, height, 8);
        this.ctx.fill();
        this.ctx.restore();
    }
    
    drawButton(button, isHovered) {
        const { x, y, width, height } = button.bounds;
        
        this.ctx.save();
        
        // 绘制按钮背景
        if (isHovered) {
            this.ctx.fillStyle = this.theme.hoverColor;
            this.roundRect(x, y, width, height, 4);
            this.ctx.fill();
        }
        
        // 绘制图标
        this.drawIcon(button.icon, x + width/2, y + height/2);
        
        this.ctx.restore();
    }
    
    drawIcon(icon, x, y) {
        // 支持多种图标格式：emoji、SVG路径、图片
        if (typeof icon === 'string' && icon.length === 1) {
            // Emoji图标
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(icon, x, y);
        } else if (icon.type === 'svg') {
            // SVG路径图标
            this.drawSVGPath(icon.path, x, y);
        } else if (icon.type === 'image') {
            // 图片图标
            this.ctx.drawImage(icon.image, x - 8, y - 8, 16, 16);
        }
    }
    
    roundRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
    }
}
```

### 3.3 动画系统
```javascript
class ToolbarAnimator {
    constructor(toolbar) {
        this.toolbar = toolbar;
        this.animations = new Map();
    }
    
    show(duration = 200) {
        this.animate('opacity', 0, 1, duration);
        this.animate('scale', 0.8, 1, duration, 'easeOutBack');
    }
    
    hide(duration = 150) {
        this.animate('opacity', 1, 0, duration);
        this.animate('scale', 1, 0.9, duration);
    }
    
    buttonHover(button, isHover) {
        const targetScale = isHover ? 1.1 : 1;
        this.animate(`button-${button.id}-scale`, button.scale || 1, targetScale, 100);
    }
    
    animate(property, from, to, duration, easing = 'easeOutQuad') {
        const animation = {
            property,
            from,
            to,
            duration,
            startTime: performance.now(),
            easing: this.getEasingFunction(easing)
        };
        
        this.animations.set(property, animation);
    }
    
    update(currentTime) {
        for (const [key, animation] of this.animations) {
            const elapsed = currentTime - animation.startTime;
            const progress = Math.min(elapsed / animation.duration, 1);
            
            if (progress >= 1) {
                this.applyProperty(animation.property, animation.to);
                this.animations.delete(key);
            } else {
                const easedProgress = animation.easing(progress);
                const value = animation.from + (animation.to - animation.from) * easedProgress;
                this.applyProperty(animation.property, value);
            }
        }
        
        return this.animations.size > 0; // 返回是否还有动画在进行
    }
    
    getEasingFunction(name) {
        const easings = {
            linear: t => t,
            easeOutQuad: t => t * (2 - t),
            easeOutBack: t => {
                const c1 = 1.70158;
                const c3 = c1 + 1;
                return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
            }
        };
        return easings[name] || easings.linear;
    }
}
```

## 4. 智能定位系统

### 4.1 自动定位算法
```javascript
class ToolbarPositioner {
    constructor(canvas) {
        this.canvas = canvas;
        this.margin = 10;
    }
    
    calculateOptimalPosition(selectionRect, toolbarSize) {
        const canvasRect = this.canvas.getBoundingClientRect();
        const positions = this.generateCandidatePositions(selectionRect, toolbarSize);
        
        // 评分系统：可见性、距离、用户习惯
        return positions.reduce((best, pos) => {
            const score = this.scorePosition(pos, selectionRect, canvasRect);
            return score > best.score ? { ...pos, score } : best;
        }, { score: -1 });
    }
    
    generateCandidatePositions(rect, size) {
        return [
            // 选区下方中央
            {
                x: rect.x + rect.width / 2 - size.width / 2,
                y: rect.y + rect.height + this.margin,
                priority: 'high'
            },
            // 选区上方中央
            {
                x: rect.x + rect.width / 2 - size.width / 2,
                y: rect.y - size.height - this.margin,
                priority: 'medium'
            },
            // 选区右侧
            {
                x: rect.x + rect.width + this.margin,
                y: rect.y + rect.height / 2 - size.height / 2,
                priority: 'medium'
            },
            // 选区左侧
            {
                x: rect.x - size.width - this.margin,
                y: rect.y + rect.height / 2 - size.height / 2,
                priority: 'low'
            }
        ];
    }
    
    scorePosition(pos, selectionRect, canvasRect) {
        let score = 0;
        
        // 可见性评分
        if (this.isFullyVisible(pos, canvasRect)) {
            score += 100;
        } else if (this.isPartiallyVisible(pos, canvasRect)) {
            score += 50;
        }
        
        // 距离评分
        const distance = this.getDistance(pos, selectionRect);
        score += Math.max(0, 50 - distance / 10);
        
        // 优先级评分
        const priorityScores = { high: 30, medium: 20, low: 10 };
        score += priorityScores[pos.priority] || 0;
        
        return score;
    }
}
```

## 5. 主题系统

### 5.1 主题配置
```javascript
const THEMES = {
    light: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: 'rgba(0, 0, 0, 0.1)',
        textColor: '#333333',
        hoverColor: 'rgba(0, 0, 0, 0.05)',
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        accentColor: '#007AFF'
    },
    dark: {
        backgroundColor: 'rgba(45, 45, 45, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        textColor: '#FFFFFF',
        hoverColor: 'rgba(255, 255, 255, 0.1)',
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        accentColor: '#0A84FF'
    },
    auto: {
        // 根据系统主题自动切换
        detect: () => {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
    }
};
```

## 6. 性能优化策略

### 6.1 渲染优化
- **脏矩形更新**: 只重绘变化的区域
- **离屏渲染**: 复杂图标预渲染到离屏Canvas
- **帧率控制**: 限制动画帧率，避免过度渲染

### 6.2 内存优化
- **图标缓存**: 预加载和缓存常用图标
- **事件节流**: 鼠标移动事件节流处理
- **及时清理**: 动画完成后清理相关资源

## 7. 集成方案

### 7.1 与截图覆盖层集成
```javascript
// 在截图覆盖层中集成工具栏
class ScreenshotOverlay {
    constructor() {
        this.canvas = document.getElementById('canvas');
        this.toolbar = new CanvasToolbar(this.canvas, {
            theme: 'auto',
            position: 'smart'
        });
        
        this.bindToolbarEvents();
    }
    
    onSelectionComplete(selectionRect) {
        // 选区完成后显示工具栏
        this.toolbar.show(selectionRect);
    }
    
    bindToolbarEvents() {
        this.toolbar.on('confirmCapture', () => {
            this.captureSelection();
        });
        
        this.toolbar.on('cancelCapture', () => {
            this.cancelSelection();
        });
        
        this.toolbar.on('openEditor', () => {
            this.openImageEditor();
        });
    }
}
```

### 7.2 与Tauri后端通信
```javascript
// 工具栏操作触发后端命令
class ToolbarActions {
    static async confirmCapture(selectionRect) {
        try {
            const result = await invoke('capture_region_from_overlay', {
                region: selectionRect
            });
            return result;
        } catch (error) {
            console.error('Capture failed:', error);
            throw error;
        }
    }
    
    static async copyToClipboard(imagePath) {
        return await invoke('copy_image_to_clipboard', { path: imagePath });
    }
    
    static async saveToFile(imagePath, savePath) {
        return await invoke('save_image_to_path', { 
            sourcePath: imagePath, 
            targetPath: savePath 
        });
    }
}
```

## 8. 使用示例

### 8.1 基础使用
```javascript
// 在截图覆盖层中使用Canvas工具栏
import CanvasToolbar from './CanvasToolbar.js';
import { invoke } from '@tauri-apps/api/core';

class ScreenshotOverlay {
    constructor() {
        this.canvas = document.getElementById('overlay-canvas');
        this.ctx = this.canvas.getContext('2d');

        // 创建工具栏实例
        this.toolbar = new CanvasToolbar(this.canvas, {
            theme: 'auto',
            buttonSize: 36,
            animationDuration: 200
        });

        this.setupToolbarCallbacks();
        this.bindEvents();
    }

    setupToolbarCallbacks() {
        // 确认截图
        this.toolbar.on('confirmCapture', async () => {
            try {
                const result = await invoke('capture_region_from_overlay', {
                    region: this.currentSelection
                });
                this.onCaptureComplete(result);
            } catch (error) {
                this.showError('截图失败: ' + error);
            }
        });

        // 取消截图
        this.toolbar.on('cancelCapture', () => {
            this.closeOverlay();
        });

        // 复制到剪贴板
        this.toolbar.on('copyToClipboard', async () => {
            try {
                await invoke('copy_selection_to_clipboard', {
                    region: this.currentSelection
                });
                this.showSuccess('已复制到剪贴板');
            } catch (error) {
                this.showError('复制失败: ' + error);
            }
        });

        // 打开编辑器
        this.toolbar.on('openEditor', () => {
            this.openImageEditor(this.currentSelection);
        });

        // 保存文件
        this.toolbar.on('saveToFile', async () => {
            try {
                const result = await invoke('save_selection_dialog', {
                    region: this.currentSelection
                });
                this.showSuccess('文件已保存');
            } catch (error) {
                this.showError('保存失败: ' + error);
            }
        });
    }

    onSelectionComplete(selectionRect) {
        this.currentSelection = selectionRect;
        // 显示工具栏
        this.toolbar.show(selectionRect);
    }

    onSelectionChange(selectionRect) {
        this.currentSelection = selectionRect;
        // 更新工具栏位置
        if (this.toolbar.isVisible) {
            this.toolbar.updatePosition(selectionRect);
        }
    }
}
```

### 8.2 自定义主题
```javascript
// 创建自定义主题的工具栏
const customTheme = {
    backgroundColor: 'rgba(30, 30, 30, 0.9)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    textColor: '#FFFFFF',
    hoverColor: 'rgba(255, 255, 255, 0.15)',
    shadowColor: 'rgba(0, 0, 0, 0.8)',
    accentColor: '#FF6B35'
};

const toolbar = new CanvasToolbar(canvas, {
    theme: customTheme,
    buttonSize: 40,
    borderRadius: 12,
    animationDuration: 300
});
```

### 8.3 添加自定义按钮
```javascript
// 扩展工具栏功能
class ExtendedCanvasToolbar extends CanvasToolbar {
    createButtons() {
        const baseButtons = super.createButtons();

        // 添加自定义按钮
        const customButtons = [
            {
                id: 'upload',
                icon: '☁️',
                label: '上传到云端',
                shortcut: 'Ctrl+U',
                action: 'uploadToCloud'
            },
            {
                id: 'qrcode',
                icon: '📱',
                label: '生成二维码',
                action: 'generateQRCode'
            }
        ];

        // 在"更多"按钮前插入自定义按钮
        const moreButtonIndex = baseButtons.findIndex(btn => btn.id === 'more');
        baseButtons.splice(moreButtonIndex, 0, ...customButtons);

        return baseButtons;
    }
}

// 使用扩展的工具栏
const extendedToolbar = new ExtendedCanvasToolbar(canvas);
extendedToolbar.on('uploadToCloud', async () => {
    // 实现云端上传逻辑
});
```

### 8.4 响应式布局
```javascript
// 根据屏幕尺寸调整工具栏
class ResponsiveCanvasToolbar extends CanvasToolbar {
    constructor(canvas, options = {}) {
        // 根据屏幕尺寸调整默认配置
        const screenWidth = window.innerWidth;
        const responsiveOptions = {
            buttonSize: screenWidth < 768 ? 32 : 36,
            padding: screenWidth < 768 ? 6 : 8,
            ...options
        };

        super(canvas, responsiveOptions);

        // 监听窗口大小变化
        window.addEventListener('resize', this.onResize.bind(this));
    }

    onResize() {
        // 重新计算布局
        this.calculateLayout();
        this.requestRender();
    }

    calculateOptimalPosition(selectionRect) {
        // 移动设备优化：优先显示在屏幕底部
        if (window.innerWidth < 768) {
            this.position = {
                x: (this.canvas.width - this.bounds.width) / 2,
                y: this.canvas.height - this.bounds.height - 20
            };
        } else {
            super.calculateOptimalPosition(selectionRect);
        }
    }
}
```

## 9. 性能基准测试

### 9.1 渲染性能
```javascript
// 性能测试工具
class ToolbarPerformanceMonitor {
    constructor(toolbar) {
        this.toolbar = toolbar;
        this.metrics = {
            renderTime: [],
            animationFrames: 0,
            memoryUsage: []
        };
    }

    startMonitoring() {
        const originalRender = this.toolbar.render.bind(this.toolbar);

        this.toolbar.render = () => {
            const startTime = performance.now();
            originalRender();
            const endTime = performance.now();

            this.metrics.renderTime.push(endTime - startTime);
            this.metrics.animationFrames++;

            // 记录内存使用
            if (performance.memory) {
                this.metrics.memoryUsage.push(performance.memory.usedJSHeapSize);
            }
        };
    }

    getReport() {
        const avgRenderTime = this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length;
        const maxRenderTime = Math.max(...this.metrics.renderTime);

        return {
            averageRenderTime: avgRenderTime.toFixed(2) + 'ms',
            maxRenderTime: maxRenderTime.toFixed(2) + 'ms',
            totalFrames: this.metrics.animationFrames,
            fps: (this.metrics.animationFrames / (Date.now() - this.startTime) * 1000).toFixed(1),
            memoryTrend: this.analyzeMemoryTrend()
        };
    }
}

// 使用示例
const monitor = new ToolbarPerformanceMonitor(toolbar);
monitor.startMonitoring();

// 运行一段时间后获取报告
setTimeout(() => {
    console.log('工具栏性能报告:', monitor.getReport());
}, 10000);
```

### 9.2 性能优化建议
```javascript
// 优化版本的工具栏
class OptimizedCanvasToolbar extends CanvasToolbar {
    constructor(canvas, options = {}) {
        super(canvas, options);

        // 启用性能优化
        this.enableOptimizations();
    }

    enableOptimizations() {
        // 1. 脏矩形渲染
        this.dirtyRegions = [];
        this.lastRenderState = null;

        // 2. 离屏Canvas缓存
        this.offscreenCanvas = document.createElement('canvas');
        this.offscreenCtx = this.offscreenCanvas.getContext('2d');

        // 3. 事件节流
        this.throttledMouseMove = this.throttle(this.onMouseMove.bind(this), 16); // 60fps

        // 4. 预渲染图标
        this.preRenderIcons();
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    preRenderIcons() {
        // 预渲染所有图标到离屏Canvas
        this.iconCache = new Map();

        [...this.buttons, ...this.extendedButtons].forEach(button => {
            const iconCanvas = document.createElement('canvas');
            iconCanvas.width = iconCanvas.height = 32;
            const iconCtx = iconCanvas.getContext('2d');

            // 渲染图标
            this.drawIcon(button.icon, 16, 16);
            this.iconCache.set(button.id, iconCanvas);
        });
    }

    render() {
        // 检查是否需要重新渲染
        if (!this.needsRender && !this.animations.size) {
            return;
        }

        // 使用脏矩形优化
        if (this.dirtyRegions.length > 0) {
            this.renderDirtyRegions();
        } else {
            super.render();
        }

        this.needsRender = false;
    }
}
```

## 总结

Canvas快捷操作栏方案具有以下优势：

1. **高性能**: 硬件加速渲染，流畅的动画效果
2. **灵活性**: 完全自定义的UI和交互逻辑
3. **一致性**: 跨平台视觉效果统一
4. **扩展性**: 易于添加新功能和自定义主题
5. **集成性**: 与现有Canvas覆盖层无缝集成
6. **响应式**: 适配不同屏幕尺寸和设备
7. **可测试**: 内置性能监控和优化机制

这个方案可以为Mecap提供专业级的截图工具体验，同时保持良好的性能和用户体验。通过模块化设计，可以根据具体需求进行定制和扩展。
