{"extends": ["config:recommended", "helpers:pinGitHubActionDigests"], "cloneSubmodules": true, "semanticCommitType": "chore", "labels": ["dependencies"], "postUpdateOptions": ["pnpmDedupe"], "git-submodules": {"enabled": true, "schedule": ["every weekend"]}, "packageRules": [{"matchPackageNames": ["/.*astro.*/i", "/.*starlight.*/i", "/.*typedoc.*/i"], "groupName": "astro & starlight packages", "separateMajorMinor": false}]}